"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const router = express_1.default.Router();
// Get all products
router.get('/', async (req, res) => {
    try {
        const productsData = await db_1.db.select().from(schema_1.products);
        res.json(productsData);
    }
    catch (error) {
        console.error('Error fetching products:', error);
        res.status(500).json({ error: 'Failed to fetch products' });
    }
});
// Get product by ID
router.get('/:id', async (req, res) => {
    try {
        const productId = Number(req.params.id);
        if (isNaN(productId)) {
            return res.status(400).json({ error: 'Invalid product ID' });
        }
        const productResult = await db_1.db
            .select()
            .from(schema_1.products)
            .where((0, drizzle_orm_1.eq)(schema_1.products.id, productId))
            .limit(1);
        const product = productResult[0];
        if (!product) {
            return res.status(404).json({ error: 'Product not found' });
        }
        // Get inventory levels for all stores
        const inventoryLevels = await db_1.db
            .select({
            storeId: schema_1.inventory.storeId,
            storeName: schema_1.stores.name,
            quantity: schema_1.inventory.quantity
        })
            .from(schema_1.inventory)
            .leftJoin(schema_1.stores, (0, drizzle_orm_1.eq)(schema_1.inventory.storeId, schema_1.stores.id))
            .where((0, drizzle_orm_1.eq)(schema_1.inventory.productId, product.id));
        res.json({ ...product, inventory: inventoryLevels });
    }
    catch (error) {
        console.error('Error fetching product:', error);
        res.status(500).json({ error: 'Failed to fetch product' });
    }
});
// Create new product
router.post('/', async (req, res) => {
    try {
        const { name, category, sku, brand, purchasePrice, retailPrice, warranty } = req.body;
        if (!name || !category || !sku || !brand || purchasePrice === undefined || retailPrice === undefined || !warranty) {
            return res.status(400).json({ error: 'Missing required fields' });
        }
        const result = await db_1.db
            .insert(schema_1.products)
            .values({
            name,
            categoryId: category,
            sku,
            brand,
            purchasePrice: purchasePrice.toString(),
            retailPrice: retailPrice.toString(),
            warranty
        })
            .returning();
        res.status(201).json(result[0]);
    }
    catch (error) {
        console.error('Error creating product:', error);
        res.status(500).json({ error: 'Failed to create product' });
    }
});
// Update product
router.put('/:id', async (req, res) => {
    try {
        const productId = Number(req.params.id);
        if (isNaN(productId)) {
            return res.status(400).json({ error: 'Invalid product ID' });
        }
        const { name, category, sku, brand, purchasePrice, retailPrice, warranty } = req.body;
        if (!name || !category || !sku || !brand || purchasePrice === undefined || retailPrice === undefined || !warranty) {
            return res.status(400).json({ error: 'Missing required fields' });
        }
        const result = await db_1.db
            .update(schema_1.products)
            .set({
            name,
            categoryId: category,
            sku,
            brand,
            purchasePrice: purchasePrice.toString(),
            retailPrice: retailPrice.toString(),
            warranty,
            updatedAt: new Date()
        })
            .where((0, drizzle_orm_1.eq)(schema_1.products.id, productId))
            .returning();
        if (!result[0]) {
            return res.status(404).json({ error: 'Product not found' });
        }
        res.json(result[0]);
    }
    catch (error) {
        console.error('Error updating product:', error);
        res.status(500).json({ error: 'Failed to update product' });
    }
});
// Delete product
router.delete('/:id', async (req, res) => {
    try {
        const productId = Number(req.params.id);
        if (isNaN(productId)) {
            return res.status(400).json({ error: 'Invalid product ID' });
        }
        // First delete inventory records
        await db_1.db.delete(schema_1.inventory).where((0, drizzle_orm_1.eq)(schema_1.inventory.productId, productId));
        // Then delete the product
        const result = await db_1.db.delete(schema_1.products).where((0, drizzle_orm_1.eq)(schema_1.products.id, productId)).returning();
        if (result.length === 0) {
            return res.status(404).json({ error: 'Product not found' });
        }
        res.json({ message: 'Product deleted successfully' });
    }
    catch (error) {
        console.error('Error deleting product:', error);
        res.status(500).json({ error: 'Failed to delete product' });
    }
});
exports.default = router;
