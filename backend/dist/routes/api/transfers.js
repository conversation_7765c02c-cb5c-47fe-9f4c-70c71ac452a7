"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const db_1 = require("../../db");
const schema_1 = require("../../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const auth_1 = require("../../middleware/auth");
const router = express_1.default.Router();
// Create transfer request
router.post('/', auth_1.authMiddleware, async (req, res) => {
    try {
        const { user } = req;
        const { fromStoreId, toStoreId, productId, quantity } = req.body;
        // Check if user has permission for this store
        if (user.role !== schema_1.UserRole.ADMIN && user.storeId !== fromStoreId) {
            return res.status(403).json({ error: 'Unauthorized store access' });
        }
        // Check if source store has enough inventory
        const inventoryResult = await db_1.db
            .select({ quantity: schema_1.inventory.quantity })
            .from(schema_1.inventory)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.inventory.storeId, fromStoreId), (0, drizzle_orm_1.eq)(schema_1.inventory.productId, productId)))
            .limit(1);
        const currentInventory = inventoryResult[0];
        if (!currentInventory || currentInventory.quantity < quantity) {
            return res.status(400).json({ error: 'Insufficient inventory' });
        }
        // Create transfer request
        const transfer = await db_1.db.insert(schema_1.transfers).values({
            fromStoreId,
            toStoreId,
            originStoreId: fromStoreId,
            destinationStoreId: toStoreId,
            createdBy: user.id,
            status: 'pending'
        }).returning().then(r => r[0]);
        res.json(transfer);
    }
    catch (error) {
        console.error('Create transfer error:', error);
        res.status(500).json({ error: 'Failed to create transfer request' });
    }
});
// Get transfer requests
router.get('/', auth_1.authMiddleware, async (req, res) => {
    try {
        const { user } = req;
        const { status, page = 1, limit = 10 } = req.query;
        // Build where conditions first
        const whereConditions = [];
        // Filter by status if provided
        if (status) {
            whereConditions.push((0, drizzle_orm_1.eq)(schema_1.transfers.status, status.toString()));
        }
        // Admin can see all transfers, others can only see their store's transfers
        if (user.role !== schema_1.UserRole.ADMIN) {
            whereConditions.push((0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.transfers.fromStoreId, user.storeId), (0, drizzle_orm_1.eq)(schema_1.transfers.toStoreId, user.storeId)));
        }
        // Build the complete query with conditions
        const baseQuery = db_1.db
            .select({
            id: schema_1.transfers.id,
            fromStoreId: schema_1.transfers.fromStoreId,
            toStoreId: schema_1.transfers.toStoreId,
            status: schema_1.transfers.status,
            notes: schema_1.transfers.notes,
            createdBy: schema_1.transfers.createdBy,
            createdAt: schema_1.transfers.createdAt
        })
            .from(schema_1.transfers);
        const finalQuery = whereConditions.length > 0
            ? baseQuery.where((0, drizzle_orm_1.and)(...whereConditions))
            : baseQuery;
        const [transfersResult, countResult] = await Promise.all([
            finalQuery
                .limit(Number(limit))
                .offset((Number(page) - 1) * Number(limit)),
            db_1.db.select({ count: (0, drizzle_orm_1.sql) `count(*)` }).from(schema_1.transfers)
        ]);
        const count = countResult[0];
        res.json({
            transfers: transfersResult,
            total: count.count,
            page: Number(page),
            limit: Number(limit)
        });
    }
    catch (error) {
        console.error('Get transfers error:', error);
        res.status(500).json({ error: 'Failed to get transfers' });
    }
});
// Approve transfer
router.put('/:id/approve', auth_1.authMiddleware, async (req, res) => {
    try {
        const { user } = req;
        const { id } = req.params;
        // Only admins or store owners can approve transfers
        if (user.role !== schema_1.UserRole.ADMIN && user.role !== schema_1.UserRole.STORE_OWNER) {
            return res.status(403).json({ error: 'Unauthorized to approve transfers' });
        }
        const transferResult = await db_1.db
            .select()
            .from(schema_1.transfers)
            .where((0, drizzle_orm_1.eq)(schema_1.transfers.id, parseInt(id)))
            .limit(1);
        const transfer = transferResult[0];
        if (!transfer || transfer.status !== 'pending') {
            return res.status(400).json({ error: 'Invalid transfer request' });
        }
        // Update transfer status (inventory logic removed since transfers table doesn't support product-level transfers)
        const result = await db_1.db.update(schema_1.transfers)
            .set({
            status: 'approved',
            updatedAt: new Date()
        })
            .where((0, drizzle_orm_1.eq)(schema_1.transfers.id, parseInt(id)))
            .returning()
            .then(r => r[0]);
        res.json(result);
    }
    catch (error) {
        console.error('Approve transfer error:', error);
        res.status(500).json({ error: 'Failed to approve transfer' });
    }
});
// Get transfer history for a product
router.get('/history/:productId', auth_1.authMiddleware, async (req, res) => {
    try {
        const { productId } = req.params;
        const { storeId } = req.query;
        // Note: transfers table doesn't support product-level transfers, returning empty for now
        const transfersData = [];
        res.json(transfersData);
    }
    catch (error) {
        console.error('Get transfer history error:', error);
        res.status(500).json({ error: 'Failed to get transfer history' });
    }
});
exports.default = router;
