"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.reminderService = exports.ReminderService = exports.defaultReminderConfig = void 0;
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const sms_1 = require("../utils/sms");
exports.defaultReminderConfig = {
    overdueDays: 1,
    smsProvider: 'twilio',
    messageTemplate: 'Dear {{customerName}}, your payment of ${{amountDue}} for credit sale #{{saleId}} is overdue. Please make the payment at your earliest convenience. Contact us if you need assistance.'
};
class ReminderService {
    constructor(config = {}) {
        this.config = { ...exports.defaultReminderConfig, ...config };
    }
    async checkOverdueSales() {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - this.config.overdueDays);
            const overdueSales = await db_1.db
                .select({
                creditSale: schema_1.creditSales,
                customer: schema_1.customers,
                totalPaid: (0, drizzle_orm_1.sql) `COALESCE(SUM(${schema_1.creditPayments.amount}), 0)`
            })
                .from(schema_1.creditSales)
                .leftJoin(schema_1.customers, (0, drizzle_orm_1.eq)(schema_1.customers.id, schema_1.creditSales.customerId))
                .leftJoin(schema_1.creditPayments, (0, drizzle_orm_1.eq)(schema_1.creditPayments.creditSaleId, schema_1.creditSales.id))
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.creditSales.status, 'pending'), (0, drizzle_orm_1.sql) `${schema_1.creditSales.dueDate} < ${cutoffDate}`))
                .groupBy(schema_1.creditSales.id, schema_1.customers.id)
                .orderBy(schema_1.creditSales.dueDate);
            for (const sale of overdueSales) {
                const totalAmount = parseFloat(sale.creditSale.totalAmount);
                const amountDue = totalAmount - sale.totalPaid;
                if (amountDue > 0) {
                    await this.createReminder(sale, amountDue);
                }
            }
        }
        catch (error) {
            console.error('Error checking overdue sales:', error);
        }
    }
    async createReminder(sale, amountDue) {
        try {
            const customerName = `${sale.customer.firstName} ${sale.customer.lastName}`;
            const message = this.config.messageTemplate
                .replace('{{customerName}}', customerName)
                .replace('{{amountDue}}', amountDue.toFixed(2))
                .replace('{{saleId}}', sale.creditSale.id);
            await db_1.db.insert(schema_1.notifications).values({
                creditSaleId: sale.creditSale.id,
                userId: null,
                storeId: sale.creditSale.storeId,
                type: 'sms',
                title: 'Payment Reminder',
                message,
                status: 'pending'
            });
            // Send SMS immediately
            await this.sendReminder(sale.creditSale.id);
        }
        catch (error) {
            console.error('Error creating reminder:', error);
        }
    }
    async sendReminder(creditSaleId) {
        try {
            const reminderResult = await db_1.db
                .select({
                notification: schema_1.notifications,
                customer: schema_1.customers
            })
                .from(schema_1.notifications)
                .leftJoin(schema_1.creditSales, (0, drizzle_orm_1.eq)(schema_1.creditSales.id, schema_1.notifications.creditSaleId))
                .leftJoin(schema_1.customers, (0, drizzle_orm_1.eq)(schema_1.customers.id, schema_1.creditSales.customerId))
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.notifications.creditSaleId, creditSaleId), (0, drizzle_orm_1.eq)(schema_1.notifications.status, 'pending')))
                .limit(1);
            const result = reminderResult[0];
            if (!result?.notification || !result?.customer?.phone)
                return;
            await (0, sms_1.sendSMS)(result.customer.phone, result.notification.message);
            await db_1.db.update(schema_1.notifications)
                .set({
                status: 'sent',
                updatedAt: new Date()
            })
                .where((0, drizzle_orm_1.eq)(schema_1.notifications.id, result.notification.id));
        }
        catch (error) {
            console.error('Error sending reminder:', error);
        }
    }
    async getOverdueCustomers() {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - this.config.overdueDays);
        return db_1.db
            .select({
            creditSale: schema_1.creditSales,
            customer: schema_1.customers,
            totalPaid: (0, drizzle_orm_1.sql) `COALESCE(SUM(${schema_1.creditPayments.amount}), 0)`,
            lastPayment: (0, drizzle_orm_1.sql) `MAX(${schema_1.creditPayments.createdAt})`,
            lastReminder: (0, drizzle_orm_1.sql) `MAX(${schema_1.notifications.sentAt})`
        })
            .from(schema_1.creditSales)
            .leftJoin(schema_1.customers, (0, drizzle_orm_1.eq)(schema_1.customers.id, schema_1.creditSales.customerId))
            .leftJoin(schema_1.creditPayments, (0, drizzle_orm_1.eq)(schema_1.creditPayments.creditSaleId, schema_1.creditSales.id))
            .leftJoin(schema_1.notifications, (0, drizzle_orm_1.eq)(schema_1.notifications.creditSaleId, schema_1.creditSales.id))
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.creditSales.status, 'pending'), (0, drizzle_orm_1.sql) `${schema_1.creditSales.dueDate} < ${cutoffDate}`))
            .groupBy(schema_1.creditSales.id, schema_1.customers.id)
            .orderBy((0, drizzle_orm_1.sql) `credit_sales.due_date ASC`);
    }
    async getPaymentHistory(customerId) {
        return db_1.db
            .select({
            creditSale: schema_1.creditSales,
            payment: schema_1.creditPayments,
            notification: schema_1.notifications
        })
            .from(schema_1.creditPayments)
            .leftJoin(schema_1.creditSales, (0, drizzle_orm_1.eq)(schema_1.creditSales.id, schema_1.creditPayments.creditSaleId))
            .leftJoin(schema_1.notifications, (0, drizzle_orm_1.eq)(schema_1.notifications.creditSaleId, schema_1.creditPayments.creditSaleId))
            .where((0, drizzle_orm_1.eq)(schema_1.creditSales.customerId, customerId))
            .orderBy((0, drizzle_orm_1.sql) `creditPayments.createdAt DESC`);
    }
}
exports.ReminderService = ReminderService;
exports.reminderService = new ReminderService();
