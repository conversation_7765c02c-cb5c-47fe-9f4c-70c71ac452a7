"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cashDrawerService = exports.CashDrawerService = void 0;
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
class CashDrawerService {
    async openCashDrawer(data, userId) {
        try {
            // Check if there's an open drawer
            const openDrawerResult = await db_1.db
                .select({ id: schema_1.cashDrawers.id })
                .from(schema_1.cashDrawers)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.cashDrawers.storeId, data.storeId), (0, drizzle_orm_1.sql) `${schema_1.cashDrawers.status} IN ('open', 'reconciling')`))
                .limit(1);
            const openDrawer = openDrawerResult[0];
            if (openDrawer) {
                throw new Error('A cash drawer is already open for this store');
            }
            const drawerId = await db_1.db
                .insert(schema_1.cashDrawers)
                .values({
                name: data.name,
                storeId: data.storeId,
                status: schema_1.CashDrawerStatus.OPEN,
                openingBalance: data.openingBalance.toString(),
                actualBalance: data.openingBalance.toString(),
                openedBy: userId,
                openedAt: new Date()
            })
                .returning({ id: schema_1.cashDrawers.id });
            return drawerId[0].id;
        }
        catch (error) {
            console.error('Error opening cash drawer:', error);
            throw error;
        }
    }
    async recordTransaction(data, userId) {
        try {
            const drawerResult = await db_1.db
                .select({
                id: schema_1.cashDrawers.id,
                status: schema_1.cashDrawers.status,
                actualBalance: schema_1.cashDrawers.actualBalance
            })
                .from(schema_1.cashDrawers)
                .where((0, drizzle_orm_1.eq)(schema_1.cashDrawers.id, data.drawerId))
                .limit(1);
            const drawer = drawerResult[0];
            if (!drawer || drawer.status !== schema_1.CashDrawerStatus.OPEN) {
                throw new Error('Cash drawer is not open');
            }
            // Update actual balance
            const currentBalance = parseFloat(drawer.actualBalance || '0');
            let newBalance = currentBalance;
            if (data.transactionType === 'sale' || data.transactionType === 'deposit') {
                newBalance += data.amount;
            }
            else if (data.transactionType === 'return' || data.transactionType === 'withdrawal') {
                newBalance -= data.amount;
            }
            await db_1.db.update(schema_1.cashDrawers)
                .set({
                actualBalance: newBalance.toString(),
                updatedAt: new Date()
            })
                .where((0, drizzle_orm_1.eq)(schema_1.cashDrawers.id, data.drawerId));
            // Record transaction
            const transactionId = await db_1.db
                .insert(schema_1.cashDrawerTransactions)
                .values({
                cashDrawerId: data.drawerId,
                drawerId: data.drawerId,
                transactionType: data.transactionType,
                amount: data.amount.toString(),
                referenceNumber: data.referenceNumber,
                notes: data.notes,
                createdBy: userId
            })
                .returning();
            return transactionId[0].id;
        }
        catch (error) {
            console.error('Error recording transaction:', error);
            throw error;
        }
    }
    async closeCashDrawer(drawerId, userId) {
        try {
            const drawerResult = await db_1.db
                .select({
                id: schema_1.cashDrawers.id,
                status: schema_1.cashDrawers.status,
                actualBalance: schema_1.cashDrawers.actualBalance
            })
                .from(schema_1.cashDrawers)
                .where((0, drizzle_orm_1.eq)(schema_1.cashDrawers.id, drawerId))
                .limit(1);
            const drawer = drawerResult[0];
            if (!drawer || drawer.status !== schema_1.CashDrawerStatus.OPEN) {
                throw new Error('Cash drawer is not open');
            }
            await db_1.db.update(schema_1.cashDrawers)
                .set({
                status: schema_1.CashDrawerStatus.CLOSED,
                closingBalance: drawer.actualBalance,
                closedBy: userId,
                closedAt: new Date(),
                updatedAt: new Date()
            })
                .where((0, drizzle_orm_1.eq)(schema_1.cashDrawers.id, drawerId));
        }
        catch (error) {
            console.error('Error closing cash drawer:', error);
            throw error;
        }
    }
    async reconcileCashDrawer(data, userId) {
        try {
            const drawerResult = await db_1.db
                .select({
                id: schema_1.cashDrawers.id,
                status: schema_1.cashDrawers.status,
                actualBalance: schema_1.cashDrawers.actualBalance,
                openingBalance: schema_1.cashDrawers.openingBalance,
                closingBalance: schema_1.cashDrawers.closingBalance
            })
                .from(schema_1.cashDrawers)
                .where((0, drizzle_orm_1.eq)(schema_1.cashDrawers.id, data.drawerId))
                .limit(1);
            const drawer = drawerResult[0];
            if (!drawer || drawer.status !== schema_1.CashDrawerStatus.CLOSED) {
                throw new Error('Cash drawer must be closed before reconciliation');
            }
            const openingBalance = parseFloat(drawer.openingBalance || '0');
            const closingBalance = parseFloat(drawer.closingBalance || '0');
            const expectedBalance = openingBalance + closingBalance;
            const variance = expectedBalance - data.actualBalance;
            // Create reconciliation record
            const reconciliationId = await db_1.db
                .insert(schema_1.cashDrawerReconciliations)
                .values({
                cashDrawerId: data.drawerId,
                drawerId: data.drawerId,
                expectedAmount: expectedBalance.toString(),
                actualAmount: data.actualBalance.toString(),
                variance: variance.toString(),
                notes: data.notes,
                reconciledBy: userId
            })
                .returning();
            // Update drawer status
            await db_1.db.update(schema_1.cashDrawers)
                .set({
                status: schema_1.CashDrawerStatus.RECONCILED,
                variance: variance.toString(),
                updatedAt: new Date()
            })
                .where((0, drizzle_orm_1.eq)(schema_1.cashDrawers.id, data.drawerId));
            return reconciliationId[0].id;
        }
        catch (error) {
            console.error('Error reconciling cash drawer:', error);
            throw error;
        }
    }
    async getDailySummary(storeId, date) {
        try {
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);
            const summaryResult = await db_1.db
                .select({
                totalSales: (0, drizzle_orm_1.sql) `SUM(
            CASE WHEN ${schema_1.cashDrawerTransactions.transactionType} = 'sale' THEN ${schema_1.cashDrawerTransactions.amount} ELSE 0 END
          )`,
                totalReturns: (0, drizzle_orm_1.sql) `SUM(
            CASE WHEN ${schema_1.cashDrawerTransactions.transactionType} = 'return' THEN ${schema_1.cashDrawerTransactions.amount} ELSE 0 END
          )`,
                totalDeposits: (0, drizzle_orm_1.sql) `SUM(
            CASE WHEN ${schema_1.cashDrawerTransactions.transactionType} = 'deposit' THEN ${schema_1.cashDrawerTransactions.amount} ELSE 0 END
          )`,
                totalWithdrawals: (0, drizzle_orm_1.sql) `SUM(
            CASE WHEN ${schema_1.cashDrawerTransactions.transactionType} = 'withdrawal' THEN ${schema_1.cashDrawerTransactions.amount} ELSE 0 END
          )`,
                totalAdjustments: (0, drizzle_orm_1.sql) `SUM(
            CASE WHEN ${schema_1.cashDrawerTransactions.transactionType} = 'adjustment' THEN ${schema_1.cashDrawerTransactions.amount} ELSE 0 END
          )`,
                openingBalance: (0, drizzle_orm_1.sql) `COALESCE(SUM(
            CASE WHEN ${schema_1.cashDrawers.openedAt} BETWEEN ${startOfDay} AND ${endOfDay} 
            THEN ${schema_1.cashDrawers.openingBalance} ELSE 0 END
          ), 0)`,
                closingBalance: (0, drizzle_orm_1.sql) `COALESCE(SUM(
            CASE WHEN ${schema_1.cashDrawers.closedAt} BETWEEN ${startOfDay} AND ${endOfDay} 
            THEN ${schema_1.cashDrawers.closingBalance} ELSE 0 END
          ), 0)`,
                actualBalance: (0, drizzle_orm_1.sql) `COALESCE(SUM(
            CASE WHEN ${schema_1.cashDrawers.closedAt} BETWEEN ${startOfDay} AND ${endOfDay} 
            THEN ${schema_1.cashDrawers.actualBalance} ELSE 0 END
          ), 0)`,
                totalVariance: (0, drizzle_orm_1.sql) `COALESCE(SUM(${schema_1.cashDrawers.variance}), 0)`,
                totalReconciliations: (0, drizzle_orm_1.sql) `COUNT(DISTINCT ${schema_1.cashDrawerReconciliations.id})`
            })
                .from(schema_1.cashDrawers)
                .leftJoin(schema_1.cashDrawerTransactions, (0, drizzle_orm_1.eq)(schema_1.cashDrawerTransactions.drawerId, schema_1.cashDrawers.id))
                .leftJoin(schema_1.cashDrawerReconciliations, (0, drizzle_orm_1.eq)(schema_1.cashDrawerReconciliations.drawerId, schema_1.cashDrawers.id))
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.cashDrawers.storeId, storeId), (0, drizzle_orm_1.sql) `${schema_1.cashDrawers.openedAt} BETWEEN ${startOfDay} AND ${endOfDay}`))
                .limit(1);
            const summary = summaryResult[0];
            return summary;
        }
        catch (error) {
            console.error('Error getting daily summary:', error);
            throw error;
        }
    }
    async getTransactionHistory(drawerId, startDate, endDate, limit = 10, page = 1) {
        try {
            const whereConditions = [(0, drizzle_orm_1.eq)(schema_1.cashDrawerTransactions.drawerId, drawerId)];
            if (startDate && endDate) {
                whereConditions.push((0, drizzle_orm_1.sql) `${schema_1.cashDrawerTransactions.createdAt} BETWEEN ${startDate} AND ${endDate}`);
            }
            const query = db_1.db
                .select({
                id: schema_1.cashDrawerTransactions.id,
                transactionType: schema_1.cashDrawerTransactions.transactionType,
                amount: schema_1.cashDrawerTransactions.amount,
                referenceNumber: schema_1.cashDrawerTransactions.referenceNumber,
                notes: schema_1.cashDrawerTransactions.notes,
                createdBy: (0, drizzle_orm_1.sql) `CONCAT(users.first_name, ' ', users.last_name)`,
                createdAt: schema_1.cashDrawerTransactions.createdAt
            })
                .from(schema_1.cashDrawerTransactions)
                .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.users.id, schema_1.cashDrawerTransactions.createdBy))
                .where((0, drizzle_orm_1.and)(...whereConditions))
                .orderBy((0, drizzle_orm_1.sql) `cash_drawer_transactions.created_at DESC`);
            return await query
                .limit(limit)
                .offset((page - 1) * limit);
        }
        catch (error) {
            console.error('Error getting transaction history:', error);
            throw error;
        }
    }
}
exports.CashDrawerService = CashDrawerService;
exports.cashDrawerService = new CashDrawerService();
