"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.backupService = exports.BackupService = void 0;
const db_1 = require("../db");
const path_1 = require("path");
const fs_1 = require("fs");
const child_process_1 = require("child_process");
const XLSX = __importStar(require("xlsx"));
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
class BackupService {
    constructor(config) {
        this.config = config;
        this.backupPath = (0, path_1.join)(config.backupPath, 'postgres_backups');
        // Create backup directory if it doesn't exist
        if (!(0, fs_1.existsSync)(this.backupPath)) {
            (0, fs_1.mkdirSync)(this.backupPath, { recursive: true });
        }
    }
    getBackupPath() {
        return this.backupPath;
    }
    async performBackup() {
        try {
            const timestamp = new Date().toISOString().split('.')[0].replace(/[:.]/g, '-');
            const backupFile = (0, path_1.join)(this.backupPath, `backup-${timestamp}.sql`);
            const command = `
        PGPASSWORD=${this.config.databasePassword} \
        pg_dump \
        -h ${this.config.databaseHost} \
        -U ${this.config.databaseUser} \
        -F c \
        -b \
        -v \
        -f ${backupFile} \
        ${this.config.databaseName}
      `;
            return new Promise((resolve, reject) => {
                (0, child_process_1.exec)(command, (error, stdout, stderr) => {
                    if (error) {
                        console.error('Backup error:', error);
                        reject(error);
                    }
                    if (stderr) {
                        console.error('Backup stderr:', stderr);
                        reject(stderr);
                    }
                    console.log('Backup successful:', stdout);
                    resolve(backupFile);
                });
            });
        }
        catch (error) {
            console.error('Error performing backup:', error);
            throw error;
        }
    }
    async restoreBackup(backupFile) {
        try {
            const command = `
        PGPASSWORD=${this.config.databasePassword} \
        pg_restore \
        -h ${this.config.databaseHost} \
        -U ${this.config.databaseUser} \
        -d ${this.config.databaseName} \
        -v \
        ${backupFile}
      `;
            return new Promise((resolve, reject) => {
                (0, child_process_1.exec)(command, (error, stdout, stderr) => {
                    if (error) {
                        console.error('Restore error:', error);
                        reject(error);
                    }
                    if (stderr) {
                        console.error('Restore stderr:', stderr);
                        reject(stderr);
                    }
                    console.log('Restore successful:', stdout);
                    resolve();
                });
            });
        }
        catch (error) {
            console.error('Error restoring backup:', error);
            throw error;
        }
    }
    async cleanOldBackups() {
        try {
            const command = `
        find ${this.backupPath} \
        -type f \
        -name "backup-*.sql" \
        -mtime +${this.config.retentionDays} \
        -exec rm {} \;
      `;
            (0, child_process_1.exec)(command, (error, stdout, stderr) => {
                if (error) {
                    console.error('Error cleaning old backups:', error);
                }
                if (stderr) {
                    console.error('Clean old backups stderr:', stderr);
                }
                console.log('Clean old backups successful:', stdout);
            });
        }
        catch (error) {
            console.error('Error cleaning old backups:', error);
            throw error;
        }
    }
    async exportInventoryToExcel(storeId) {
        try {
            const inventoryData = await db_1.db
                .select({
                product: schema_1.products.name,
                variant: schema_1.productVariants.name,
                quantity: schema_1.inventory.quantity,
                unitCost: schema_1.inventory.unitCost,
                lastUpdated: schema_1.inventory.lastUpdated
            })
                .from(schema_1.inventory)
                .leftJoin(schema_1.productVariants, (0, drizzle_orm_1.eq)(schema_1.productVariants.id, schema_1.inventory.variantId))
                .leftJoin(schema_1.products, (0, drizzle_orm_1.eq)(schema_1.products.id, schema_1.productVariants.productId))
                .where((0, drizzle_orm_1.eq)(schema_1.inventory.storeId, storeId));
            const worksheet = XLSX.utils.json_to_sheet(inventoryData);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Inventory');
            return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
        }
        catch (error) {
            console.error('Error exporting inventory:', error);
            throw error;
        }
    }
    async exportSalesToExcel(storeId, startDate, endDate) {
        try {
            const salesData = await db_1.db
                .select({
                saleDate: schema_1.sales.createdAt,
                customer: schema_1.customers.firstName,
                product: schema_1.products.name,
                variant: schema_1.productVariants.name,
                quantity: schema_1.salesItems.quantity,
                unitPrice: schema_1.salesItems.unitPrice,
                total: (0, drizzle_orm_1.sql) `${schema_1.salesItems.quantity} * ${schema_1.salesItems.unitPrice}`,
                paymentMethod: schema_1.sales.paymentMethod,
                status: schema_1.sales.status
            })
                .from(schema_1.sales)
                .leftJoin(schema_1.customers, (0, drizzle_orm_1.eq)(schema_1.customers.id, schema_1.sales.customerId))
                .leftJoin(schema_1.salesItems, (0, drizzle_orm_1.eq)(schema_1.salesItems.saleId, schema_1.sales.id))
                .leftJoin(schema_1.productVariants, (0, drizzle_orm_1.eq)(schema_1.productVariants.id, schema_1.salesItems.variantId))
                .leftJoin(schema_1.products, (0, drizzle_orm_1.eq)(schema_1.products.id, schema_1.productVariants.productId))
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.sales.storeId, storeId), (0, drizzle_orm_1.sql) `${schema_1.sales.createdAt} BETWEEN ${startDate} AND ${endDate}`))
                .orderBy((0, drizzle_orm_1.sql) `sales.created_at DESC`);
            const worksheet = XLSX.utils.json_to_sheet(salesData);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Sales');
            return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
        }
        catch (error) {
            console.error('Error exporting sales:', error);
            throw error;
        }
    }
}
exports.BackupService = BackupService;
exports.backupService = new BackupService({
    backupPath: process.env.BACKUP_PATH || '/var/backups',
    backupFrequency: 'daily',
    retentionDays: 7,
    databaseName: process.env.DB_NAME || 'bestgadget',
    databaseUser: process.env.DB_USER || 'postgres',
    databasePassword: process.env.DB_PASSWORD,
    databaseHost: process.env.DB_HOST || 'localhost'
});
