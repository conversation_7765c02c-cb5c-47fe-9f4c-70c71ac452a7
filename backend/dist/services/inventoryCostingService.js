"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.inventoryCostingService = exports.InventoryCostingService = void 0;
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
class InventoryCostingService {
    async recordTransaction(data, userId) {
        try {
            const storeResult = await db_1.db
                .select({
                costingMethod: schema_1.stores.inventoryCostingMethod
            })
                .from(schema_1.stores)
                .where((0, drizzle_orm_1.eq)(schema_1.stores.id, data.storeId))
                .limit(1);
            const store = storeResult[0];
            if (!store) {
                throw new Error('Store not found');
            }
            const transactionId = await db_1.db
                .insert(schema_1.inventoryTransactions)
                .values({
                storeId: data.storeId,
                productId: data.productId,
                variantId: data.variantId,
                quantity: data.quantity,
                unitCost: data.unitCost.toString(),
                totalCost: (data.quantity * data.unitCost).toString(),
                transactionType: data.transactionType,
                referenceId: data.referenceId,
                notes: data.notes,
                createdBy: userId
            })
                .returning();
            await this.updateInventoryCost(data.storeId, data.variantId, data.quantity, data.unitCost, store.costingMethod);
            return transactionId[0].id;
        }
        catch (error) {
            console.error('Error recording transaction:', error);
            throw error;
        }
    }
    async updateInventoryCost(storeId, variantId, quantity, unitCost, costingMethod) {
        try {
            const currentInventoryResult = await db_1.db
                .select({
                quantity: schema_1.inventory.quantity,
                unitCost: schema_1.inventory.unitCost,
                totalCost: schema_1.inventory.totalCost
            })
                .from(schema_1.inventory)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.inventory.storeId, storeId), (0, drizzle_orm_1.eq)(schema_1.inventory.variantId, variantId)))
                .limit(1);
            const currentInventory = currentInventoryResult[0];
            let newQuantity = (currentInventory?.quantity || 0) + quantity;
            let newUnitCost;
            let newTotalCost;
            switch (costingMethod) {
                case schema_1.InventoryCostingMethod.FIFO:
                    // FIFO: Oldest inventory is sold first
                    newTotalCost = Number(currentInventory?.totalCost) || 0;
                    newUnitCost = unitCost;
                    break;
                case schema_1.InventoryCostingMethod.LIFO:
                    // LIFO: Newest inventory is sold first
                    newTotalCost = Number(currentInventory?.totalCost) || 0;
                    newUnitCost = unitCost;
                    break;
                case schema_1.InventoryCostingMethod.AVERAGE:
                    // Average Costing: Average of all purchases
                    const totalQuantity = (currentInventory?.quantity || 0) + quantity;
                    const totalCost = (Number(currentInventory?.totalCost) || 0) + (quantity * unitCost);
                    newUnitCost = totalQuantity > 0 ? totalCost / totalQuantity : unitCost;
                    newTotalCost = totalQuantity * newUnitCost;
                    break;
                default:
                    throw new Error(`Invalid costing method: ${costingMethod}`);
            }
            // Check if inventory record exists
            const existingInventory = await db_1.db
                .select()
                .from(schema_1.inventory)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.inventory.storeId, storeId), (0, drizzle_orm_1.eq)(schema_1.inventory.variantId, variantId)))
                .limit(1);
            if (existingInventory.length > 0) {
                // Update existing record
                await db_1.db
                    .update(schema_1.inventory)
                    .set({
                    quantity: newQuantity,
                    unitCost: newUnitCost.toString(),
                    totalCost: newTotalCost.toString(),
                    updatedAt: new Date()
                })
                    .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.inventory.storeId, storeId), (0, drizzle_orm_1.eq)(schema_1.inventory.variantId, variantId)));
            }
            else {
                // Insert new record
                await db_1.db
                    .insert(schema_1.inventory)
                    .values({
                    productId: 1, // TODO: Get actual productId from variant
                    storeId,
                    variantId,
                    quantity: newQuantity,
                    unitCost: newUnitCost.toString(),
                    totalCost: newTotalCost.toString(),
                    threshold: 5,
                    createdAt: new Date(),
                    updatedAt: new Date()
                });
            }
        }
        catch (error) {
            console.error('Error updating inventory cost:', error);
            throw error;
        }
    }
    async getInventoryCost(storeId, variantId, quantity, costingMethod) {
        try {
            const inventoryRecordResult = await db_1.db
                .select({
                quantity: schema_1.inventory.quantity,
                unitCost: schema_1.inventory.unitCost,
                totalCost: schema_1.inventory.totalCost
            })
                .from(schema_1.inventory)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.inventory.storeId, storeId), (0, drizzle_orm_1.eq)(schema_1.inventory.variantId, variantId)))
                .limit(1);
            const inventoryRecord = inventoryRecordResult[0];
            if (!inventoryRecord) {
                return 0;
            }
            let cost;
            switch (costingMethod) {
                case schema_1.InventoryCostingMethod.FIFO:
                    // FIFO: Use oldest cost
                    const oldestTransactionResult = await db_1.db
                        .select({
                        unitCost: schema_1.inventoryTransactions.unitCost
                    })
                        .from(schema_1.inventoryTransactions)
                        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.inventoryTransactions.storeId, storeId), (0, drizzle_orm_1.eq)(schema_1.inventoryTransactions.variantId, variantId), (0, drizzle_orm_1.eq)(schema_1.inventoryTransactions.transactionType, 'purchase')))
                        .orderBy((0, drizzle_orm_1.sql) `inventory_transactions.created_at ASC`)
                        .limit(1);
                    const oldestTransaction = oldestTransactionResult[0];
                    cost = Number(oldestTransaction?.unitCost) || Number(inventoryRecord.unitCost);
                    break;
                case schema_1.InventoryCostingMethod.LIFO:
                    // LIFO: Use newest cost
                    const newestTransactionResult = await db_1.db
                        .select({
                        unitCost: schema_1.inventoryTransactions.unitCost
                    })
                        .from(schema_1.inventoryTransactions)
                        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.inventoryTransactions.storeId, storeId), (0, drizzle_orm_1.eq)(schema_1.inventoryTransactions.variantId, variantId), (0, drizzle_orm_1.eq)(schema_1.inventoryTransactions.transactionType, 'purchase')))
                        .orderBy((0, drizzle_orm_1.sql) `inventory_transactions.created_at DESC`)
                        .limit(1);
                    const newestTransaction = newestTransactionResult[0];
                    cost = Number(newestTransaction?.unitCost) || Number(inventoryRecord.unitCost);
                    break;
                case schema_1.InventoryCostingMethod.AVERAGE:
                    // Average Costing: Use current average
                    cost = Number(inventoryRecord.unitCost);
                    break;
                default:
                    throw new Error(`Invalid costing method: ${costingMethod}`);
            }
            return cost * quantity;
        }
        catch (error) {
            console.error('Error calculating inventory cost:', error);
            throw error;
        }
    }
    async getCOGS(storeId, variantId, quantity, saleDate) {
        try {
            const storeResult = await db_1.db
                .select({
                costingMethod: schema_1.stores.inventoryCostingMethod
            })
                .from(schema_1.stores)
                .where((0, drizzle_orm_1.eq)(schema_1.stores.id, storeId))
                .limit(1);
            const store = storeResult[0];
            if (!store) {
                throw new Error('Store not found');
            }
            const cost = await this.getInventoryCost(storeId, variantId, quantity, store.costingMethod);
            // Record sale transaction
            await db_1.db.insert(schema_1.inventoryTransactions).values({
                storeId,
                productId: 1, // TODO: Get actual productId from variant
                variantId,
                transactionType: 'sale',
                quantity: -quantity,
                unitCost: (cost / quantity).toString(),
                totalCost: (-cost).toString(),
                referenceNumber: `SALE-${saleDate.toISOString().split('T')[0]}`
            });
            return cost;
        }
        catch (error) {
            console.error('Error calculating COGS:', error);
            throw error;
        }
    }
    async getInventoryValuation(storeId, variantId, startDate, endDate) {
        try {
            const whereConditions = [(0, drizzle_orm_1.eq)(schema_1.inventory.storeId, storeId)];
            if (variantId) {
                whereConditions.push((0, drizzle_orm_1.eq)(schema_1.inventory.variantId, variantId));
            }
            if (startDate && endDate) {
                whereConditions.push((0, drizzle_orm_1.sql) `${schema_1.inventory.lastUpdated} BETWEEN ${startDate} AND ${endDate}`);
            }
            return await db_1.db
                .select({
                variantId: schema_1.inventory.variantId,
                variantName: schema_1.productVariants.name,
                quantity: schema_1.inventory.quantity,
                unitCost: schema_1.inventory.unitCost,
                totalCost: schema_1.inventory.totalCost,
                lastUpdated: schema_1.inventory.lastUpdated
            })
                .from(schema_1.inventory)
                .leftJoin(schema_1.productVariants, (0, drizzle_orm_1.eq)(schema_1.productVariants.id, schema_1.inventory.variantId))
                .where((0, drizzle_orm_1.and)(...whereConditions));
        }
        catch (error) {
            console.error('Error getting inventory valuation:', error);
            throw error;
        }
    }
    async getInventoryHistory(storeId, variantId, startDate, endDate) {
        try {
            let query = db_1.db
                .select({
                transactionId: schema_1.inventoryTransactions.id,
                transactionType: schema_1.inventoryTransactions.transactionType,
                quantity: schema_1.inventoryTransactions.quantity,
                unitCost: schema_1.inventoryTransactions.unitCost,
                totalCost: schema_1.inventoryTransactions.totalCost,
                referenceNumber: schema_1.inventoryTransactions.referenceNumber,
                notes: schema_1.inventoryTransactions.notes,
                createdBy: (0, drizzle_orm_1.sql) `CONCAT(users.first_name, ' ', users.last_name)`,
                createdAt: schema_1.inventoryTransactions.createdAt
            })
                .from(schema_1.inventoryTransactions)
                .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.users.id, schema_1.inventoryTransactions.createdBy))
                .where(startDate && endDate
                ? (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.inventoryTransactions.storeId, storeId), (0, drizzle_orm_1.eq)(schema_1.inventoryTransactions.variantId, variantId), (0, drizzle_orm_1.sql) `${schema_1.inventoryTransactions.createdAt} BETWEEN ${startDate} AND ${endDate}`)
                : (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.inventoryTransactions.storeId, storeId), (0, drizzle_orm_1.eq)(schema_1.inventoryTransactions.variantId, variantId)))
                .orderBy((0, drizzle_orm_1.sql) `inventory_transactions.created_at DESC`);
            return query;
        }
        catch (error) {
            console.error('Error getting inventory history:', error);
            throw error;
        }
    }
}
exports.InventoryCostingService = InventoryCostingService;
exports.inventoryCostingService = new InventoryCostingService();
