"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.expenseService = exports.ExpenseService = void 0;
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
class ExpenseService {
    async createExpenseCategory(data) {
        try {
            const result = await db_1.db.insert(schema_1.expenseCategories).values(data).returning();
            return result[0].id;
        }
        catch (error) {
            console.error('Error creating expense category:', error);
            throw error;
        }
    }
    async getExpenseCategories(storeId) {
        try {
            const categories = await db_1.db
                .select({
                id: schema_1.expenseCategories.id,
                name: schema_1.expenseCategories.name,
                description: schema_1.expenseCategories.description,
                type: schema_1.expenseCategories.type,
                isRecurring: schema_1.expenseCategories.isRecurring,
                recurringPeriod: schema_1.expenseCategories.recurringPeriod,
                totalSpent: (0, drizzle_orm_1.sql) `COALESCE(SUM(${schema_1.expenses.amount}), 0)`
            })
                .from(schema_1.expenseCategories)
                .leftJoin(schema_1.expenses, (0, drizzle_orm_1.eq)(schema_1.expenses.categoryId, schema_1.expenseCategories.id))
                .where((0, drizzle_orm_1.eq)(schema_1.expenses.storeId, storeId))
                .groupBy(schema_1.expenseCategories.id);
            return categories;
        }
        catch (error) {
            console.error('Error getting expense categories:', error);
            throw error;
        }
    }
    async createExpense(data, storeId, userId) {
        try {
            const result = await db_1.db.insert(schema_1.expenses).values({
                categoryId: data.categoryId,
                amount: data.amount.toString(),
                date: new Date(data.date),
                description: data.description,
                paymentMethod: data.paymentMethod,
                referenceNumber: data.referenceNumber,
                storeId,
                createdBy: userId,
                status: data.status || 'paid'
            }).returning();
            return result[0].id;
        }
        catch (error) {
            console.error('Error creating expense:', error);
            throw error;
        }
    }
    async getExpenses(storeId, categoryId, startDate, endDate, status, limit = 10, page = 1) {
        try {
            const whereConditions = [(0, drizzle_orm_1.eq)(schema_1.expenses.storeId, storeId)];
            if (categoryId) {
                whereConditions.push((0, drizzle_orm_1.eq)(schema_1.expenses.categoryId, categoryId));
            }
            if (startDate && endDate) {
                whereConditions.push((0, drizzle_orm_1.sql) `${schema_1.expenses.date} BETWEEN ${startDate} AND ${endDate}`);
            }
            if (status) {
                whereConditions.push((0, drizzle_orm_1.eq)(schema_1.expenses.status, status));
            }
            return await db_1.db
                .select({
                id: schema_1.expenses.id,
                category: schema_1.expenseCategories.name,
                amount: schema_1.expenses.amount,
                date: schema_1.expenses.date,
                description: schema_1.expenses.description,
                paymentMethod: schema_1.expenses.paymentMethod,
                referenceNumber: schema_1.expenses.referenceNumber,
                status: schema_1.expenses.status,
                createdBy: (0, drizzle_orm_1.sql) `CONCAT(users.first_name, ' ', users.last_name)`
            })
                .from(schema_1.expenses)
                .leftJoin(schema_1.expenseCategories, (0, drizzle_orm_1.eq)(schema_1.expenseCategories.id, schema_1.expenses.categoryId))
                .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.users.id, schema_1.expenses.createdBy))
                .where((0, drizzle_orm_1.and)(...whereConditions))
                .orderBy((0, drizzle_orm_1.sql) `expenses.date DESC`)
                .limit(limit)
                .offset((page - 1) * limit);
        }
        catch (error) {
            console.error('Error getting expenses:', error);
            throw error;
        }
    }
    async getFinancialSummary(storeId, startDate, endDate) {
        try {
            // Get total sales
            const totalSales = await db_1.db
                .select({
                total: (0, drizzle_orm_1.sql) `SUM(${schema_1.sales.totalAmount})`
            })
                .from(schema_1.sales)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.sales.storeId, storeId), (0, drizzle_orm_1.sql) `${schema_1.sales.saleDate} BETWEEN ${startDate} AND ${endDate}`))
                .limit(1);
            // Get total expenses
            const totalExpenses = await db_1.db
                .select({
                total: (0, drizzle_orm_1.sql) `SUM(${schema_1.expenses.amount})`
            })
                .from(schema_1.expenses)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.expenses.storeId, storeId), (0, drizzle_orm_1.sql) `${schema_1.expenses.date} BETWEEN ${startDate} AND ${endDate}`))
                .limit(1);
            // Get expense breakdown by category
            const expenseBreakdown = await db_1.db
                .select({
                category: schema_1.expenseCategories.name,
                amount: (0, drizzle_orm_1.sql) `SUM(${schema_1.expenses.amount})`
            })
                .from(schema_1.expenses)
                .leftJoin(schema_1.expenseCategories, (0, drizzle_orm_1.eq)(schema_1.expenseCategories.id, schema_1.expenses.categoryId))
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.expenses.storeId, storeId), (0, drizzle_orm_1.sql) `${schema_1.expenses.date} BETWEEN ${startDate} AND ${endDate}`))
                .groupBy(schema_1.expenseCategories.name);
            const totalSalesAmount = totalSales[0]?.total || 0;
            const totalExpensesAmount = totalExpenses[0]?.total || 0;
            // Get profit and loss
            const profit = totalSalesAmount - totalExpensesAmount;
            return {
                totalSales: totalSalesAmount,
                totalExpenses: totalExpensesAmount,
                profit,
                expenseBreakdown,
                period: {
                    start: startDate,
                    end: endDate
                }
            };
        }
        catch (error) {
            console.error('Error getting financial summary:', error);
            throw error;
        }
    }
}
exports.ExpenseService = ExpenseService;
exports.expenseService = new ExpenseService();
