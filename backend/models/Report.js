const mongoose = require('mongoose');

const reportSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: [
      'daily_sales',
      'monthly_sales',
      'yearly_sales',
      'inventory_status',
      'supplier_performance',
      'customer_activity',
      'profit_margin',
      'store_performance'
    ]
  },
  store: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Store'
  },
  dateRange: {
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    }
  },
  data: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  metrics: {
    totalSales: {
      type: Number,
      currency: 'RWF'
    },
    totalExpenses: {
      type: Number,
      currency: 'RWF'
    },
    totalProfit: {
      type: Number,
      currency: 'RWF'
    },
    averageSale: {
      type: Number,
      currency: 'RWF'
    },
    totalCustomers: Number,
    totalItemsSold: Number,
    stockAlerts: Number,
    lowStockItems: Number
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Index for faster searches
reportSchema.index({ type: 1 });
reportSchema.index({ store: 1 });
reportSchema.index({ 'dateRange.startDate': 1 });

const Report = mongoose.model('Report', reportSchema);
module.exports = Report;
