const mongoose = require('mongoose');

const supplierSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  businessName: {
    type: String,
    trim: true
  },
  contactPerson: {
    firstName: String,
    lastName: String,
    email: String,
    phoneNumber: String
  },
  address: {
    street: String,
    city: String,
    country: String,
    postalCode: String
  },
  phoneNumber: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    unique: true
  },
  website: String,
  categories: [{
    type: String,
    enum: ['Electronics', 'Mobile Phones', 'Accessories', 'Computers', 'TVs', 'Audio', 'Gaming']
  }],
  paymentTerms: {
    type: String,
    default: '30 days'
  },
  creditLimit: {
    type: Number,
    min: 0,
    currency: 'RWF'
  },
  currentBalance: {
    type: Number,
    default: 0,
    currency: 'RWF'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  notes: String,
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Index for faster searches
supplierSchema.index({ email: 1 });
supplierSchema.index({ phoneNumber: 1 });

const Supplier = mongoose.model('Supplier', supplierSchema);
module.exports = Supplier;
