const mongoose = require('mongoose');

const purchaseSchema = new mongoose.Schema({
  supplier: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Supplier',
    required: true
  },
  store: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Store',
    required: true
  },
  purchaseOrderNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  items: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 1
    },
    unitPrice: {
      type: Number,
      required: true,
      min: 0,
      currency: 'RWF'
    },
    totalAmount: {
      type: Number,
      required: true,
      min: 0,
      currency: 'RWF'
    },
    discount: {
      type: Number,
      min: 0,
      max: 100
    }
  }],
  totalAmount: {
    type: Number,
    required: true,
    min: 0,
    currency: 'RWF'
  },
  paymentTerms: {
    type: String,
    required: true
  },
  dueDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'paid', 'partially_paid', 'overdue'],
    default: 'pending'
  },
  paymentStatus: {
    type: String,
    enum: ['unpaid', 'partially_paid', 'paid'],
    default: 'unpaid'
  },
  paymentHistory: [{
    amount: {
      type: Number,
      required: true,
      min: 0,
      currency: 'RWF'
    },
    paymentDate: {
      type: Date,
      required: true
    },
    paymentMethod: {
      type: String,
      enum: ['cash', 'bank_transfer', 'mobile_money'],
      required: true
    },
    note: String
  }],
  notes: String,
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Calculate total amount before saving
purchaseSchema.pre('save', function(next) {
  this.totalAmount = this.items.reduce((total, item) => {
    const itemTotal = item.unitPrice * item.quantity;
    const discount = (item.discount / 100) * itemTotal;
    return total + (itemTotal - discount);
  }, 0);
  next();
});

// Index for faster searches
purchaseSchema.index({ purchaseOrderNumber: 1 });
purchaseSchema.index({ supplier: 1 });
purchaseSchema.index({ store: 1 });

const Purchase = mongoose.model('Purchase', purchaseSchema);
module.exports = Purchase;
