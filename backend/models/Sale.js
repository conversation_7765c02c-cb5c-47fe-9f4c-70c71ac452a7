const mongoose = require('mongoose');
const { Product, Store, User } = require('./Product');

const saleSchema = new mongoose.Schema({
  store: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Store',
    required: true
  },
  cashier: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  customer: {
    name: String,
    phoneNumber: String,
    email: String
  },
  items: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 1
    },
    price: {
      type: Number,
      required: true,
      min: 0
    },
    discount: {
      type: Number,
      min: 0,
      max: 100
    }
  }],
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  paidAmount: {
    type: Number,
    required: true,
    min: 0
  },
  changeAmount: {
    type: Number,
    min: 0
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'mobile_money'],
    required: true
  },
  status: {
    type: String,
    enum: ['completed', 'cancelled', 'pending'],
    default: 'completed'
  },
  note: String,
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Calculate total amount before saving
saleSchema.pre('save', function(next) {
  this.totalAmount = this.items.reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const discount = (item.discount / 100) * itemTotal;
    return total + (itemTotal - discount);
  }, 0);
  next();
});

const Sale = mongoose.model('Sale', saleSchema);
module.exports = Sale;
