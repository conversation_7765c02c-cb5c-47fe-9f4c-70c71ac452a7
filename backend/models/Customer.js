const mongoose = require('mongoose');

const customerSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    trim: true
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    unique: true
  },
  phoneNumber: {
    type: String,
    required: true,
    trim: true
  },
  address: {
    street: String,
    city: String,
    district: String,
    sector: String,
    cell: String,
    village: String
  },
  preferredStore: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Store'
  },
  totalPurchases: {
    type: Number,
    default: 0
  },
  totalSpent: {
    type: Number,
    default: 0,
    currency: 'RWF'
  },
  lastPurchase: {
    type: Date
  },
  notes: String,
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Index for faster searches
customerSchema.index({ email: 1 });
customerSchema.index({ phoneNumber: 1 });

const Customer = mongoose.model('Customer', customerSchema);
module.exports = Customer;
