const jwt = require('jsonwebtoken');
const { User } = require('../models/User');
const { Store } = require('../models/Store');

const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      throw new Error('Authentication required');
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      throw new Error('User not found');
    }

    // Get user's store access
    const storeAccess = await Store.find({ employees: user._id });
    
    req.token = token;
    req.user = user;
    req.storeAccess = storeAccess;
    next();
  } catch (error) {
    res.status(401).json({
      success: false,
      message: 'Please authenticate',
      error: error.message
    });
  }
};

module.exports = auth;
