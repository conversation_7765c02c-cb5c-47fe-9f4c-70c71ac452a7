import { db } from '../db';
import { creditSales, customers, creditPayments, notifications, UserRole } from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { sendSMS } from '../utils/sms';

export interface ReminderConfig {
  overdueDays: number;
  smsProvider: 'twilio' | 'africastalking';
  messageTemplate: string;
}

export const defaultReminderConfig: ReminderConfig = {
  overdueDays: 1,
  smsProvider: 'twilio',
  messageTemplate: 'Dear {{customerName}}, your payment of ${{amountDue}} for credit sale #{{saleId}} is overdue. Please make the payment at your earliest convenience. Contact us if you need assistance.'
};

export class ReminderService {
  private config: ReminderConfig;

  constructor(config: Partial<ReminderConfig> = {}) {
    this.config = { ...defaultReminderConfig, ...config };
  }

  async checkOverdueSales(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.overdueDays);

      const overdueSales = await db
        .select({
          creditSale: creditSales,
          customer: customers,
          totalPaid: sql<number>`COALESCE(SUM(${creditPayments.amount}), 0)`
        })
        .from(creditSales)
        .leftJoin(customers, eq(customers.id, creditSales.customerId))
        .leftJoin(creditPayments, eq(creditPayments.creditSaleId, creditSales.id))
        .where(
          and(
            eq(creditSales.status, 'pending'),
            sql`${creditSales.dueDate} < ${cutoffDate}`
          )
        )
        .groupBy(creditSales.id, customers.id)
        .orderBy(sql`credit_sales.due_date ASC`);

      for (const sale of overdueSales) {
        const totalAmount = parseFloat(sale.creditSale.totalAmount);
        const amountDue = totalAmount - sale.totalPaid;
        if (amountDue > 0) {
          await this.createReminder(sale, amountDue);
        }
      }
    } catch (error) {
      console.error('Error checking overdue sales:', error);
    }
  }

  async createReminder(sale: any, amountDue: number): Promise<void> {
    try {
      const customerName = `${sale.customer.firstName} ${sale.customer.lastName}`;
      const message = this.config.messageTemplate
        .replace('{{customerName}}', customerName)
        .replace('{{amountDue}}', amountDue.toFixed(2))
        .replace('{{saleId}}', sale.creditSale.id);

      await db.insert(notifications).values({
        creditSaleId: sale.creditSale.id,
        userId: null,
        storeId: sale.creditSale.storeId,
        type: 'sms',
        title: 'Payment Reminder',
        message,
        status: 'pending'
      });

      // Send SMS immediately
      await this.sendReminder(sale.creditSale.id);
    } catch (error) {
      console.error('Error creating reminder:', error);
    }
  }

  async sendReminder(creditSaleId: number): Promise<void> {
    try {
      const reminderResult = await db
        .select({
          notification: notifications,
          customer: customers
        })
        .from(notifications)
        .leftJoin(creditSales, eq(creditSales.id, notifications.creditSaleId))
        .leftJoin(customers, eq(customers.id, creditSales.customerId))
        .where(
          and(
            eq(notifications.creditSaleId, creditSaleId),
            eq(notifications.status, 'pending')
          )
        )
        .limit(1);

      const result = reminderResult[0];
      if (!result?.notification || !result?.customer?.phone) return;

      await sendSMS(result.customer.phone, result.notification.message);

      await db.update(notifications)
        .set({
          status: 'sent',
          updatedAt: new Date()
        })
        .where(eq(notifications.id, result.notification.id));
    } catch (error) {
      console.error('Error sending reminder:', error);
    }
  }

  async getOverdueCustomers(): Promise<any[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.overdueDays);

    return db
      .select({
        creditSale: creditSales,
        customer: customers,
        totalPaid: sql<number>`COALESCE(SUM(${creditPayments.amount}), 0)`,
        lastPayment: sql<null | Date>`MAX(${creditPayments.createdAt})`,
        lastReminder: sql<null | Date>`MAX(${notifications.sentAt})`
      })
      .from(creditSales)
      .leftJoin(customers, eq(customers.id, creditSales.customerId))
      .leftJoin(creditPayments, eq(creditPayments.creditSaleId, creditSales.id))
      .leftJoin(notifications, eq(notifications.creditSaleId, creditSales.id))
      .where(
        and(
          eq(creditSales.status, 'pending'),
          sql`${creditSales.dueDate} < ${cutoffDate}`
        )
      )
      .groupBy(creditSales.id, customers.id)
      .orderBy(sql`credit_sales.due_date ASC`);
  }

  async getPaymentHistory(customerId: number): Promise<any[]> {
    return db
      .select({
        creditSale: creditSales,
        payment: creditPayments,
        notification: notifications
      })
      .from(creditPayments)
      .leftJoin(creditSales, eq(creditSales.id, creditPayments.creditSaleId))
      .leftJoin(notifications, eq(notifications.creditSaleId, creditPayments.creditSaleId))
      .where(eq(creditSales.customerId, customerId))
      .orderBy(sql`creditPayments.createdAt DESC`);
  }
}

export const reminderService = new ReminderService();
