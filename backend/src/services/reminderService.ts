import { db } from '../db';
import { creditSales, customers, creditPayments, notifications, UserRole } from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { sendSMS } from './smsService';

export interface ReminderConfig {
  overdueDays: number;
  smsProvider: 'twilio' | 'africastalking';
  messageTemplate: string;
}

export const defaultReminderConfig: ReminderConfig = {
  overdueDays: 1,
  smsProvider: 'twilio',
  messageTemplate: 'Dear {{customerName}}, your payment of ${{amountDue}} for credit sale #{{saleId}} is overdue. Please make the payment at your earliest convenience. Contact us if you need assistance.'
};

export class ReminderService {
  private config: ReminderConfig;

  constructor(config: Partial<ReminderConfig> = {}) {
    this.config = { ...defaultReminderConfig, ...config };
  }

  async checkOverdueSales(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.overdueDays);

      const overdueSales = await db
        .select({
          creditSale: creditSales,
          customer: customers,
          totalPaid: sql<number>`COALESCE(SUM(${creditPayments.amount}), 0)`
        })
        .from(creditSales)
        .leftJoin(customers, eq(customers.id, creditSales.customerId))
        .leftJoin(creditPayments, eq(creditPayments.creditSaleId, creditSales.id))
        .where(
          and(
            eq(creditSales.status, 'pending'),
            sql`${creditSales.dueDate} < ${cutoffDate}`
          )
        )
        .groupBy(creditSales.id, customers.id)
        .orderBy(sql`credit_sales.due_date ASC`);

      for (const sale of overdueSales) {
        const amountDue = sale.creditSale.totalAmount - sale.totalPaid;
        if (amountDue > 0) {
          await this.createReminder(sale, amountDue);
        }
      }
    } catch (error) {
      console.error('Error checking overdue sales:', error);
    }
  }

  async createReminder(sale: any, amountDue: number): Promise<void> {
    try {
      const message = this.config.messageTemplate
        .replace('{{customerName}}', sale.customer.name)
        .replace('{{amountDue}}', amountDue.toFixed(2))
        .replace('{{saleId}}', sale.creditSale.id);

      await db.insert(notifications).values({
        creditSaleId: sale.creditSale.id,
        userId: sale.customer.id, // Using customer ID as user ID for now
        storeId: 1, // Default store ID
        type: 'sms',
        title: 'Payment Reminder',
        message,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Send SMS immediately
      await this.sendReminder(sale.creditSale.id);
    } catch (error) {
      console.error('Error creating reminder:', error);
    }
  }

  async sendReminder(creditSaleId: number): Promise<void> {
    try {
      const reminderResult = await db
        .select()
        .from(notifications)
        .where(
          and(
            eq(notifications.creditSaleId, creditSaleId),
            eq(notifications.status, 'pending')
          )
        )
        .limit(1);

      const reminder = reminderResult[0];
      if (!reminder) return;

      const customerResult = await db
        .select()
        .from(customers)
        .where(eq(customers.id, reminder.userId)) // Using userId since that's what we stored
        .limit(1);

      const customer = customerResult[0];

      if (!customer?.phone) return;

      await sendSMS(customer.phone, reminder.message);

      await db.update(notifications)
        .set({
          status: 'sent',
          updatedAt: new Date()
        })
        .where(eq(notifications.id, reminder.id));
    } catch (error) {
      console.error('Error sending reminder:', error);
    }
  }

  async getOverdueCustomers(): Promise<any[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.overdueDays);

    return db
      .select({
        creditSale: creditSales,
        customer: customers,
        totalPaid: sql<number>`COALESCE(SUM(${creditPayments.amount}), 0)`,
        lastPayment: sql<null | Date>`MAX(${creditPayments.createdAt})`,
        lastReminder: sql<null | Date>`MAX(${notifications.sentAt})`
      })
      .from(creditSales)
      .leftJoin(customers, eq(customers.id, creditSales.customerId))
      .leftJoin(creditPayments, eq(creditPayments.creditSaleId, creditSales.id))
      .leftJoin(notifications, eq(notifications.creditSaleId, creditSales.id))
      .where(
        and(
          eq(creditSales.status, 'pending'),
          sql`${creditSales.dueDate} < ${cutoffDate}`
        )
      )
      .groupBy(creditSales.id, customers.id)
      .orderBy(sql`credit_sales.due_date ASC`);
  }

  async getPaymentHistory(customerId: number): Promise<any[]> {
    return db
      .select({
        creditSale: creditSales,
        payment: creditPayments,
        notification: notifications
      })
      .from(creditPayments)
      .leftJoin(creditSales, eq(creditSales.id, creditPayments.creditSaleId))
      .leftJoin(notifications, eq(notifications.creditSaleId, creditPayments.creditSaleId))
      .where(eq(creditSales.customerId, customerId))
      .orderBy(sql`creditPayments.createdAt DESC`);
  }
}

export const reminderService = new ReminderService();
