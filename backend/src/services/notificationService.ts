import { db } from '../db';
import {
  notifications,
  UserRole,
  products,
  inventory,
  transfers,
  stores,
  creditSales,
  creditPayments,
  customers
} from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';

export interface NotificationConfig {
  lowStockThreshold: number;
  overdueDays: number;
  transferReminderDays: number;
}

export const defaultConfig: NotificationConfig = {
  lowStockThreshold: 5,
  overdueDays: 1,
  transferReminderDays: 3
};

export class NotificationService {
  private config: NotificationConfig;

  constructor(config: Partial<NotificationConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  async createNotification(
    type: string,
    title: string,
    message: string,
    userId?: number,
    storeId?: number,
    creditSaleId?: number,
    data?: any
  ): Promise<void> {
    try {
      await db.insert(notifications).values({
        type,
        title,
        message,
        userId,
        storeId,
        creditSaleId,
        data
      });
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  async checkLowStock(): Promise<void> {
    try {
      const lowStockItems = await db
        .select({
          productId: products.id,
          productName: products.name,
          storeId: inventory.storeId,
          quantity: inventory.quantity,
          threshold: inventory.threshold
        })
        .from(products)
        .leftJoin(inventory, eq(inventory.productId, products.id))
        .where(
          sql`${inventory.quantity} <= ${this.config.lowStockThreshold}`
        );

      for (const item of lowStockItems) {
        await this.createNotification(
          'low_stock',
          'Low Stock Alert',
          `Product ${item.productName} is running low in store ${item.storeId}. Current quantity: ${item.quantity}`,
          undefined,
          item.storeId,
          undefined,
          { productId: item.productId, type: 'product' }
        );
      }
    } catch (error) {
      console.error('Error checking low stock:', error);
    }
  }

  async checkPendingTransfers(): Promise<void> {
    try {
      const pendingTransfers = await db
        .select({
          transferId: transfers.id,
          originStore: stores.name,
          destinationStoreId: transfers.destinationStoreId,
          createdAt: transfers.createdAt
        })
        .from(transfers)
        .leftJoin(stores, eq(stores.id, transfers.originStoreId))
        .where(
          and(
            eq(transfers.status, 'pending'),
            sql`${transfers.createdAt} < NOW() - INTERVAL '${this.config.transferReminderDays} days'`
          )
        );

      for (const transfer of pendingTransfers) {
        await this.createNotification(
          'pending_transfer',
          'Pending Transfer',
          `Transfer has been pending for ${this.config.transferReminderDays} days`,
          undefined,
          transfer.destinationStoreId,
          undefined,
          { transferId: transfer.transferId, type: 'transfer' }
        );
      }
    } catch (error) {
      console.error('Error checking pending transfers:', error);
    }
  }

  async checkOverdueBNPL(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.overdueDays);

      const overdueSales = await db
        .select({
          saleId: creditSales.id,
          customerId: creditSales.customerId,
          customerName: sql<string>`${customers.firstName} || ' ' || ${customers.lastName}`,
          storeId: creditSales.storeId,
          dueDate: creditSales.dueDate,
          totalAmount: creditSales.totalAmount,
          paidAmount: creditSales.paidAmount
        })
        .from(creditSales)
        .leftJoin(customers, eq(customers.id, creditSales.customerId))
        .where(
          and(
            eq(creditSales.status, 'pending'),
            sql`${creditSales.dueDate} < ${cutoffDate}`
          )
        );

      for (const sale of overdueSales) {
        const amountDue = parseFloat(sale.totalAmount) - parseFloat(sale.paidAmount || '0');
        await this.createNotification(
          'overdue_bnpl',
          'Overdue Payment',
          `Customer ${sale.customerName} has an overdue payment of $${amountDue.toFixed(2)} for credit sale #${sale.saleId}`,
          undefined,
          sale.storeId,
          sale.saleId
        );
      }
    } catch (error) {
      console.error('Error checking overdue BNPL:', error);
    }
  }

  async markAsRead(notificationId: number): Promise<void> {
    try {
      await db.update(notifications)
        .set({
          isRead: true,
          updatedAt: new Date()
        })
        .where(eq(notifications.id, notificationId));
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  async deleteNotification(notificationId: number): Promise<void> {
    try {
      await db.delete(notifications)
        .where(eq(notifications.id, notificationId));
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  async getNotifications(
    userId?: number,
    storeId?: number,
    type?: string,
    status?: string,
    limit = 10,
    page = 1
  ): Promise<any[]> {
    try {
      const conditions = [];

      if (userId) {
        conditions.push(eq(notifications.userId, userId));
      }

      if (storeId) {
        conditions.push(eq(notifications.storeId, storeId));
      }

      if (type) {
        conditions.push(eq(notifications.type, type));
      }

      if (status) {
        conditions.push(eq(notifications.status, status));
      }

      const offset = (page - 1) * limit;

      const results = await db
        .select()
        .from(notifications)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(sql`${notifications.createdAt} DESC`)
        .limit(limit)
        .offset(offset);

      return results;
    } catch (error) {
      console.error('Error getting notifications:', error);
      throw error;
    }
  }
}

export const notificationService = new NotificationService();
