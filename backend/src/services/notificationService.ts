import { db } from '../db';
import { notifications, UserRole } from '../db/schema';
import { eq, and } from 'drizzle-orm';

export interface NotificationConfig {
  lowStockThreshold: number;
  overdueDays: number;
  transferReminderDays: number;
}

export const defaultConfig: NotificationConfig = {
  lowStockThreshold: 5,
  overdueDays: 1,
  transferReminderDays: 3
};

export class NotificationService {
  private config: NotificationConfig;

  constructor(config: Partial<NotificationConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  async createNotification(
    type: string,
    title: string,
    message: string,
    userId?: number,
    storeId?: number,
    relatedId?: number,
    relatedType?: string
  ): Promise<void> {
    try {
      await db.insert(notifications).values({
        type,
        title,
        message,
        userId,
        storeId,
        relatedId,
        relatedType
      });
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  async checkLowStock(): Promise<void> {
    try {
      const lowStockItems = await db
        .select({
          productId: products.id,
          productName: products.name,
          storeId: inventory.storeId,
          quantity: inventory.quantity,
          threshold: inventory.threshold
        })
        .from(products)
        .leftJoin(inventory, eq(inventory.productId, products.id))
        .where(
          sql`${inventory.quantity} <= ${this.config.lowStockThreshold}`
        )
        .all();

      for (const item of lowStockItems) {
        await this.createNotification(
          'low_stock',
          'Low Stock Alert',
          `Product ${item.productName} is running low in store ${item.storeId}. Current quantity: ${item.quantity}`,
          undefined,
          item.storeId,
          item.productId,
          'product'
        );
      }
    } catch (error) {
      console.error('Error checking low stock:', error);
    }
  }

  async checkPendingTransfers(): Promise<void> {
    try {
      const pendingTransfers = await db
        .select({
          transferId: transfers.id,
          originStore: stores.name,
          destinationStore: sql<string>`destination_store.name`,
          createdAt: transfers.createdAt
        })
        .from(transfers)
        .leftJoin(stores, eq(stores.id, transfers.originStoreId))
        .leftJoin(sql`stores as destination_store`, sql`destination_store.id = ${transfers.destinationStoreId}`)
        .where(
          and(
            eq(transfers.status, 'pending'),
            sql`${transfers.createdAt} < NOW() - INTERVAL '${this.config.transferReminderDays} days'`
          )
        );

      for (const transfer of pendingTransfers) {
        await this.createNotification(
          'pending_transfer',
          'Pending Transfer',
          `Transfer from ${transfer.originStore} to ${transfer.destinationStore} has been pending for ${this.config.transferReminderDays} days`,
          undefined,
          transfer.destinationStoreId,
          transfer.transferId,
          'transfer'
        );
      }
    } catch (error) {
      console.error('Error checking pending transfers:', error);
    }
  }

  async checkOverdueBNPL(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.overdueDays);

      const overdueSales = await db
        .select({
          saleId: creditSales.id,
          customerId: creditSales.customerId,
          customerName: customers.name,
          storeId: creditSales.storeId,
          dueDate: creditSales.dueDate,
          amountDue: sql<number>`${creditSales.totalAmount} - COALESCE(SUM(${creditPayments.amount}), 0)`
        })
        .from(creditSales)
        .leftJoin(customers, eq(customers.id, creditSales.customerId))
        .leftJoin(creditPayments, eq(creditPayments.creditSaleId, creditSales.id))
        .where(
          and(
            eq(creditSales.status, 'pending'),
            sql`${creditSales.dueDate} < ${cutoffDate}`
          )
        )
        .groupBy(creditSales.id, customers.id)
        .all();

      for (const sale of overdueSales) {
        await this.createNotification(
          'overdue_bnpl',
          'Overdue Payment',
          `Customer ${sale.customerName} has an overdue payment of $${sale.amountDue.toFixed(2)} for credit sale #${sale.saleId}`,
          undefined,
          sale.storeId,
          sale.saleId,
          'credit_sale'
        );
      }
    } catch (error) {
      console.error('Error checking overdue BNPL:', error);
    }
  }

  async markAsRead(notificationId: number): Promise<void> {
    try {
      await db.update(notifications)
        .set({
          status: 'read',
          updatedAt: new Date()
        })
        .where(eq(notifications.id, notificationId));
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  async dismissNotification(notificationId: number): Promise<void> {
    try {
      await db.update(notifications)
        .set({
          isDismissed: true,
          updatedAt: new Date()
        })
        .where(eq(notifications.id, notificationId));
    } catch (error) {
      console.error('Error dismissing notification:', error);
      throw error;
    }
  }

  async resolveNotification(
    notificationId: number,
    resolvedBy: number
  ): Promise<void> {
    try {
      await db.update(notifications)
        .set({
          isResolved: true,
          resolvedBy,
          resolvedAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(notifications.id, notificationId));
    } catch (error) {
      console.error('Error resolving notification:', error);
      throw error;
    }
  }

  async getNotifications(
    userId: number,
    storeId: number,
    type?: string,
    status?: string,
    limit = 10,
    page = 1
  ): Promise<any[]> {
    try {
      let query = db
        .select({
          notification: notifications,
          user: users,
          store: stores,
          related: sql`(
            SELECT json_build_object(
              'type', ${notifications.relatedType},
              'id', ${notifications.relatedId},
              CASE ${notifications.relatedType}
                WHEN 'product' THEN json_build_object(
                  'name', products.name,
                  'quantity', inventory.quantity
                )
                WHEN 'transfer' THEN json_build_object(
                  'origin', origin_stores.name,
                  'destination', destination_stores.name,
                  'status', transfers.status
                )
                WHEN 'credit_sale' THEN json_build_object(
                  'customer', customers.name,
                  'amount', creditSales.totalAmount,
                  'due_date', creditSales.dueDate
                )
                ELSE null
              END
            )
          )`
        })
        .from(notifications)
        .leftJoin(users, eq(users.id, notifications.userId))
        .leftJoin(stores, eq(stores.id, notifications.storeId))
        .leftJoin(products, and(
          eq(products.id, notifications.relatedId),
          eq(notifications.relatedType, 'product')
        ))
        .leftJoin(inventory, and(
          eq(inventory.productId, notifications.relatedId),
          eq(notifications.relatedType, 'product')
        ))
        .leftJoin(transfers, and(
          eq(transfers.id, notifications.relatedId),
          eq(notifications.relatedType, 'transfer')
        ))
        .leftJoin(sql`stores as origin_stores`, sql`origin_stores.id = ${transfers.originStoreId}`)
        .leftJoin(sql`stores as destination_stores`, sql`destination_stores.id = ${transfers.destinationStoreId}`)
        .leftJoin(creditSales, and(
          eq(creditSales.id, notifications.relatedId),
          eq(notifications.relatedType, 'credit_sale')
        ))
        .leftJoin(customers, eq(customers.id, creditSales.customerId))
        .where(
          and(
            notifications.userId === null,
            notifications.storeId === storeId,
            notifications.isDismissed === false
          )
        )
        .orderBy(sql`notifications.created_at DESC`);

      if (type) {
        query = query.where(eq(notifications.type, type));
      }

      if (status) {
        query = query.where(eq(notifications.status, status));
      }

      return query
        .limit(limit)
        .offset((page - 1) * limit)
        .all();
    } catch (error) {
      console.error('Error getting notifications:', error);
      throw error;
    }
  }
}

export const notificationService = new NotificationService();
