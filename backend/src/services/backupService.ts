import { db } from '../db';
import { join } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { exec } from 'child_process';
import * as XLSX from 'xlsx';
import {
  sales,
  salesItems,
  products,
  productVariants,
  customers,
  inventory,
  UserRole
} from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';

export interface BackupConfig {
  backupPath: string;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  retentionDays: number;
  databaseName: string;
  databaseUser: string;
  databasePassword: string;
  databaseHost: string;
}

export class BackupService {
  private config: BackupConfig;
  private backupPath: string;

  constructor(config: BackupConfig) {
    this.config = config;
    this.backupPath = join(config.backupPath, 'postgres_backups');
    
    // Create backup directory if it doesn't exist
    if (!existsSync(this.backupPath)) {
      mkdirSync(this.backupPath, { recursive: true });
    }
  }

  async performBackup(): Promise<string> {
    try {
      const timestamp = new Date().toISOString().split('.')[0].replace(/[:.]/g, '-');
      const backupFile = join(this.backupPath, `backup-${timestamp}.sql`);

      const command = `
        PGPASSWORD=${this.config.databasePassword} \
        pg_dump \
        -h ${this.config.databaseHost} \
        -U ${this.config.databaseUser} \
        -F c \
        -b \
        -v \
        -f ${backupFile} \
        ${this.config.databaseName}
      `;

      return new Promise((resolve, reject) => {
        exec(command, (error, stdout, stderr) => {
          if (error) {
            console.error('Backup error:', error);
            reject(error);
          }
          if (stderr) {
            console.error('Backup stderr:', stderr);
            reject(stderr);
          }
          console.log('Backup successful:', stdout);
          resolve(backupFile);
        });
      });
    } catch (error) {
      console.error('Error performing backup:', error);
      throw error;
    }
  }

  async restoreBackup(backupFile: string): Promise<void> {
    try {
      const command = `
        PGPASSWORD=${this.config.databasePassword} \
        pg_restore \
        -h ${this.config.databaseHost} \
        -U ${this.config.databaseUser} \
        -d ${this.config.databaseName} \
        -v \
        ${backupFile}
      `;

      return new Promise((resolve, reject) => {
        exec(command, (error, stdout, stderr) => {
          if (error) {
            console.error('Restore error:', error);
            reject(error);
          }
          if (stderr) {
            console.error('Restore stderr:', stderr);
            reject(stderr);
          }
          console.log('Restore successful:', stdout);
          resolve();
        });
      });
    } catch (error) {
      console.error('Error restoring backup:', error);
      throw error;
    }
  }

  async cleanOldBackups(): Promise<void> {
    try {
      const command = `
        find ${this.backupPath} \
        -type f \
        -name "backup-*.sql" \
        -mtime +${this.config.retentionDays} \
        -exec rm {} \;
      `;

      exec(command, (error, stdout, stderr) => {
        if (error) {
          console.error('Error cleaning old backups:', error);
        }
        if (stderr) {
          console.error('Clean old backups stderr:', stderr);
        }
        console.log('Clean old backups successful:', stdout);
      });
    } catch (error) {
      console.error('Error cleaning old backups:', error);
      throw error;
    }
  }

  async exportInventoryToExcel(storeId: number): Promise<Buffer> {
    try {
      const inventory = await db
        .select({
          product: products.name,
          variant: productVariants.name,
          quantity: inventory.quantity,
          unitCost: inventory.unitCost,
          unitPrice: products.unitPrice,
          lastUpdated: inventory.lastUpdated
        })
        .from(inventory)
        .leftJoin(productVariants, eq(productVariants.id, inventory.variantId))
        .leftJoin(products, eq(products.id, productVariants.productId))
        .where(eq(inventory.storeId, storeId));

      const worksheet = XLSX.utils.json_to_sheet(inventory);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Inventory');

      return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    } catch (error) {
      console.error('Error exporting inventory:', error);
      throw error;
    }
  }

  async exportSalesToExcel(storeId: number, startDate: string, endDate: string): Promise<Buffer> {
    try {
      const sales = await db
        .select({
          saleDate: sales.createdAt,
          customer: customers.name,
          product: products.name,
          variant: productVariants.name,
          quantity: salesItems.quantity,
          unitPrice: salesItems.unitPrice,
          total: sql<number>`${salesItems.quantity} * ${salesItems.unitPrice}`,
          paymentMethod: sales.paymentMethod,
          status: sales.status
        })
        .from(sales)
        .leftJoin(customers, eq(customers.id, sales.customerId))
        .leftJoin(salesItems, eq(salesItems.saleId, sales.id))
        .leftJoin(productVariants, eq(productVariants.id, salesItems.variantId))
        .leftJoin(products, eq(products.id, productVariants.productId))
        .where(
          and(
            eq(sales.storeId, storeId),
            sql`${sales.createdAt} BETWEEN ${startDate} AND ${endDate}`
          )
        )
        .orderBy(sql`sales.created_at DESC`);

      const worksheet = XLSX.utils.json_to_sheet(sales);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sales');

      return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    } catch (error) {
      console.error('Error exporting sales:', error);
      throw error;
    }
  }
}

export const backupService = new BackupService({
  backupPath: process.env.BACKUP_PATH || '/var/backups',
  backupFrequency: 'daily',
  retentionDays: 7,
  databaseName: process.env.DB_NAME || 'bestgadget',
  databaseUser: process.env.DB_USER || 'postgres',
  databasePassword: process.env.DB_PASSWORD,
  databaseHost: process.env.DB_HOST || 'localhost'
});
