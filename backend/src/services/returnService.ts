import { db } from '../db';
import {
  returns,
  returnItems,
  sales,
  salesItems,
  inventory,
  customers,
  refunds,
  UserRole
} from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';

export interface ReturnRequest {
  saleId: number;
  items: {
    saleItemId: number;
    quantity: number;
    condition: string;
    notes?: string;
  }[];
  reason: string;
  refundAmount: number;
  refundMethod: string;
  notes?: string;
}

export class ReturnService {
  async createReturn(request: ReturnRequest): Promise<number> {
    try {
      // Start transaction
      const result = await db.transaction(async (tx) => {
        // Create return
        const returnId = await tx.insert(returns).values({
          saleId: request.saleId,
          reason: request.reason,
          refundAmount: request.refundAmount.toString(),
          refundMethod: request.refundMethod,
          totalAmount: request.refundAmount.toString(),
          storeId: 1, // Default store ID
          userId: 1 // Default user ID
        }).returning({ id: returns.id });

        // Create return items
        const items = await Promise.all(
          request.items.map(async (item) => {
            const saleItemResult = await tx
              .select({
                productId: salesItems.productId,
                variantId: salesItems.variantId,
                unitPrice: salesItems.unitPrice,
                storeId: inventory.storeId
              })
              .from(salesItems)
              .leftJoin(inventory, eq(inventory.variantId, salesItems.variantId))
              .where(eq(salesItems.id, item.saleItemId))
              .limit(1);

            const saleItem = saleItemResult[0];
            if (!saleItem) {
              throw new Error(`Sale item not found: ${item.saleItemId}`);
            }

            // Update inventory
            await tx.update(inventory)
              .set({
                quantity: sql`${inventory.quantity} + ${item.quantity}`
              })
              .where(
                and(
                  eq(inventory.variantId, saleItem.variantId),
                  eq(inventory.storeId, saleItem.storeId)
                )
              );

            // Create return item
            return tx.insert(returnItems).values({
              returnId: returnId[0].id,
              productId: saleItem.productId,
              variantId: saleItem.variantId,
              quantity: item.quantity,
              unitPrice: saleItem.unitPrice,
              subtotal: parseFloat(saleItem.unitPrice) * item.quantity,
              condition: item.condition,
              reason: item.notes
            });
          })
        );

        // Update sale status
        await tx.update(sales)
          .set({
            status: 'returned',
            updatedAt: new Date()
          })
          .where(eq(sales.id, request.saleId));

        return returnId[0].id;
      });

      return result;
    } catch (error) {
      console.error('Error creating return:', error);
      throw error;
    }
  }

  async approveReturn(returnId: number, userId: number): Promise<void> {
    try {
      await db.transaction(async (tx) => {
        // Update return status
        await tx.update(returns)
          .set({
            status: 'approved',
            processedBy: userId,
            updatedAt: new Date()
          })
          .where(eq(returns.id, returnId));

        // Create refund
        const returnResult = await tx
          .select({
            refundAmount: returns.refundAmount,
            refundMethod: returns.refundMethod,
            saleId: returns.saleId
          })
          .from(returns)
          .where(eq(returns.id, returnId))
          .limit(1);

        const returnData = returnResult[0];
        if (returnData) {
          await tx.insert(refunds).values({
            returnId,
            amount: returnData.refundAmount.toString(),
            method: returnData.refundMethod,
            status: 'pending',
            processedBy: userId,
            createdAt: new Date(),
            updatedAt: new Date()
          });
        }
      });
    } catch (error) {
      console.error('Error approving return:', error);
      throw error;
    }
  }

  async rejectReturn(returnId: number, userId: number, reason: string): Promise<void> {
    try {
      await db.update(returns)
        .set({
          status: 'rejected',
          processedBy: userId,
          reason: reason
        })
        .where(eq(returns.id, returnId));
    } catch (error) {
      console.error('Error rejecting return:', error);
      throw error;
    }
  }

  async getReturns(
    storeId: number,
    status?: string,
    startDate?: string,
    endDate?: string,
    limit = 10,
    page = 1
  ): Promise<any[]> {
    try {
      const whereConditions = [eq(sales.storeId, storeId)];

      if (status) {
        whereConditions.push(eq(returns.status, status));
      }

      if (startDate && endDate) {
        whereConditions.push(
          sql`${returns.returnDate} BETWEEN ${startDate} AND ${endDate}`
        );
      }

      return await db
        .select({
          return: returns,
          customer: sql<string>`CONCAT(customers.first_name, ' ', customers.last_name)`,
          saleDate: sales.saleDate,
          totalAmount: sales.totalAmount,
          items: sql`json_agg(
            json_build_object(
              'id', return_items.id,
              'quantity', return_items.quantity,
              'condition', return_items.condition,
              'notes', return_items.notes
            )
          )`
        })
        .from(returns)
        .leftJoin(sales, eq(sales.id, returns.saleId))
        .leftJoin(customers, eq(customers.id, sales.customerId))
        .leftJoin(returnItems, eq(returnItems.returnId, returns.id))
        .where(and(...whereConditions))
        .groupBy(returns.id)
        .orderBy(sql`returns.created_at DESC`)
        .limit(limit)
        .offset((page - 1) * limit);
    } catch (error) {
      console.error('Error getting returns:', error);
      throw error;
    }
  }

  async getReturnDetails(returnId: number): Promise<any> {
    try {
      const returnDataResult = await db
        .select({
          return: returns,
          customer: sql<string>`CONCAT(customers.first_name, ' ', customers.last_name)`,
          sale: sales,
          items: sql`json_agg(
            json_build_object(
              'id', return_items.id,
              'quantity', return_items.quantity,
              'condition', return_items.condition,
              'notes', return_items.notes,
              'saleItem', (
                SELECT json_build_object(
                  'id', sales_items.id,
                  'variantId', sales_items.variant_id,
                  'quantity', sales_items.quantity,
                  'unitPrice', sales_items.unit_price,
                  'product', products.name
                )
                FROM sales_items
                LEFT JOIN products ON products.id = sales_items.product_id
                WHERE sales_items.product_id = return_items.product_id
              )
            )
          )`
        })
        .from(returns)
        .leftJoin(sales, eq(sales.id, returns.saleId))
        .leftJoin(customers, eq(customers.id, sales.customerId))
        .leftJoin(returnItems, eq(returnItems.returnId, returns.id))
        .where(eq(returns.id, returnId))
        .groupBy(returns.id)
        .limit(1);

      return returnDataResult[0];
    } catch (error) {
      console.error('Error getting return details:', error);
      throw error;
    }
  }
}

export const returnService = new ReturnService();
