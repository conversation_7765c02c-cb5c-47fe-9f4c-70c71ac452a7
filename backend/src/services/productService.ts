import { db } from '../db';
import {
  products,
  productCategories,
  productVariants,
  variantAttributes,
  UserRole
} from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';

export interface ProductInput {
  name: string;
  category: string;
  sku: string;
  brand: string;
  purchasePrice: number;
  retailPrice: number;
  warranty: number;
  isActive?: boolean;
}

export class ProductService {
  async createProduct(data: ProductInput, userId: number): Promise<number> {
    try {
      const categoryResult = await db
        .select({ id: productCategories.id })
        .from(productCategories)
        .where(eq(productCategories.name, data.category))
        .limit(1);

      const categoryId = categoryResult[0]?.id;

      if (!categoryId) {
        throw new Error(`Category not found: ${data.category}`);
      }

      const productId = await db
        .insert(products)
        .values({
          name: data.name,
          sku: data.sku,
          brand: data.brand,
          purchasePrice: data.purchasePrice.toString(),
          retailPrice: data.retailPrice.toString(),
          warrantyMonths: data.warranty,
          categoryId: categoryId,
          isActive: data.isActive ?? true
        })
        .returning({ id: products.id });

      return productId[0].id;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  async getProducts(
    storeId: number,
    category?: string,
    status?: string,
    limit: number = 10,
    page: number = 1
  ): Promise<any[]> {
    try {
      let query = db
        .select({
          id: products.id,
          name: products.name,
          category: productCategories.name,
          sku: products.sku,
          brand: products.brand,
          purchasePrice: products.purchasePrice,
          retailPrice: products.retailPrice,
          warranty: products.warranty,
          isActive: products.isActive,
          createdAt: products.createdAt,
          updatedAt: products.updatedAt
        })
        .from(products)
        .leftJoin(productCategories, eq(productCategories.id, products.categoryId))
        .where(
          category
            ? and(
                eq(products.isActive, status === 'inactive' ? false : true),
                eq(productCategories.name, category)
              )
            : eq(products.isActive, status === 'inactive' ? false : true)
        );

      const productResults = await query
        .orderBy(products.createdAt)
        .limit(limit)
        .offset((page - 1) * limit);

      return productResults;
    } catch (error) {
      console.error('Error getting products:', error);
      throw error;
    }
  }

  async getProduct(storeId: number, productId: number): Promise<any> {
    try {
      const productResult = await db
        .select({
          id: products.id,
          name: products.name,
          category: productCategories.name,
          sku: products.sku,
          brand: products.brand,
          purchasePrice: products.purchasePrice,
          retailPrice: products.retailPrice,
          warranty: products.warranty,
          isActive: products.isActive,
          createdAt: products.createdAt,
          updatedAt: products.updatedAt,
          variants: sql`
            SELECT json_agg(json_build_object(
              'id', pv.id,
              'name', pv.name,
              'sku', pv.sku,
              'unitPrice', pv.unitPrice,
              'isActive', pv.isActive,
              'defaultVariant', pv.defaultVariant,
              'attributes', (
                SELECT json_agg(json_build_object(
                  'name', pa.name,
                  'value', av.value
                ))
                FROM variant_attributes va
                JOIN attribute_values av ON va.attributeValueId = av.id
                JOIN product_attributes pa ON av.attributeId = pa.id
                WHERE va.variantId = pv.id
              )
            )) as variants
            FROM product_variants pv
            WHERE pv.productId = ${productId}
          `
        })
        .from(products)
        .leftJoin(productCategories, eq(productCategories.id, products.categoryId))
        .where(eq(products.id, productId))
        .limit(1);

      const product = productResult[0];

      if (!product) {
        throw new Error('Product not found');
      }

      return product;
    } catch (error) {
      console.error('Error getting product:', error);
      throw error;
    }
  }

  async updateProduct(productId: number, data: Partial<ProductInput>): Promise<void> {
    try {
      const updateData: any = {};

      if (data.name !== undefined) updateData.name = data.name;
      if (data.sku !== undefined) updateData.sku = data.sku;
      if (data.brand !== undefined) updateData.brand = data.brand;
      if (data.purchasePrice !== undefined) updateData.purchasePrice = data.purchasePrice.toString();
      if (data.retailPrice !== undefined) updateData.retailPrice = data.retailPrice.toString();
      if (data.warranty !== undefined) updateData.warrantyMonths = data.warranty;
      if (data.isActive !== undefined) updateData.isActive = data.isActive;

      await db.update(products)
        .set(updateData)
        .where(eq(products.id, productId));
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  }

  async deleteProduct(productId: number): Promise<void> {
    try {
      // First delete variants and their attributes
      const variants = await db
        .select({ id: productVariants.id })
        .from(productVariants)
        .where(eq(productVariants.productId, productId));

      await Promise.all(
        variants.map(async (variant) => {
          // Delete variant attributes
          await db.delete(variantAttributes)
            .where(eq(variantAttributes.variantId, variant.id));

          // Delete variant
          await db.delete(productVariants)
            .where(eq(productVariants.id, variant.id));
        })
      );

      // Then delete the product
      await db.delete(products)
        .where(eq(products.id, productId));
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }
}

export const productService = new ProductService();
