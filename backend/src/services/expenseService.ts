import { db } from '../db';
import { 
  expenseCategories,
  expenses,
  stores,
  UserRole 
} from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';

export interface ExpenseCategoryInput {
  name: string;
  description?: string;
  type: string; // fixed, variable, recurring
  isRecurring?: boolean;
  recurringPeriod?: string; // monthly, yearly, etc.
}

export interface ExpenseInput {
  categoryId: number;
  amount: number;
  date: string;
  description?: string;
  paymentMethod: string;
  referenceNumber?: string;
  status?: string;
}

export class ExpenseService {
  async createExpenseCategory(data: ExpenseCategoryInput): Promise<number> {
    try {
      const result = await db.insert(expenseCategories).values(data).returning();
      return result[0].id;
    } catch (error) {
      console.error('Error creating expense category:', error);
      throw error;
    }
  }

  async getExpenseCategories(storeId: number): Promise<any[]> {
    try {
      const categories = await db
        .select({
          id: expenseCategories.id,
          name: expenseCategories.name,
          description: expenseCategories.description,
          type: expenseCategories.type,
          isRecurring: expenseCategories.isRecurring,
          recurringPeriod: expenseCategories.recurringPeriod,
          totalSpent: sql<number>`COALESCE(SUM(${expenses.amount}), 0)`
        })
        .from(expenseCategories)
        .leftJoin(expenses, eq(expenses.categoryId, expenseCategories.id))
        .where(eq(expenses.storeId, storeId))
        .groupBy(expenseCategories.id);

      return categories;
    } catch (error) {
      console.error('Error getting expense categories:', error);
      throw error;
    }
  }

  async createExpense(data: ExpenseInput, storeId: number, userId: number): Promise<number> {
    try {
      const result = await db.insert(expenses).values({
        ...data,
        storeId,
        createdBy: userId,
        status: data.status || 'paid'
      }).returning();

      return result[0].id;
    } catch (error) {
      console.error('Error creating expense:', error);
      throw error;
    }
  }

  async getExpenses(
    storeId: number,
    categoryId?: number,
    startDate?: string,
    endDate?: string,
    status?: string,
    limit = 10,
    page = 1
  ): Promise<any[]> {
    try {
      let query = db
        .select({
          id: expenses.id,
          category: expenseCategories.name,
          amount: expenses.amount,
          date: expenses.date,
          description: expenses.description,
          paymentMethod: expenses.paymentMethod,
          referenceNumber: expenses.referenceNumber,
          status: expenses.status,
          createdBy: sql<string>`CONCAT(users.first_name, ' ', users.last_name)`
        })
        .from(expenses)
        .leftJoin(expenseCategories, eq(expenseCategories.id, expenses.categoryId))
        .leftJoin(users, eq(users.id, expenses.createdBy))
        .where(eq(expenses.storeId, storeId))
        .orderBy(sql`expenses.date DESC`);

      if (categoryId) {
        query = query.where(eq(expenses.categoryId, categoryId));
      }

      if (startDate && endDate) {
        query = query.where(
          sql`${expenses.date} BETWEEN ${startDate} AND ${endDate}`
        );
      }

      if (status) {
        query = query.where(eq(expenses.status, status));
      }

      return query
        .limit(limit)
        .offset((page - 1) * limit)
        .all();
    } catch (error) {
      console.error('Error getting expenses:', error);
      throw error;
    }
  }

  async getFinancialSummary(
    storeId: number,
    startDate: string,
    endDate: string
  ): Promise<any> {
    try {
      // Get total sales
      const totalSales = await db
        .select({
          total: sql<number>`SUM(${sales.totalAmount})`
        })
        .from(sales)
        .where(
          and(
            eq(sales.storeId, storeId),
            sql`${sales.saleDate} BETWEEN ${startDate} AND ${endDate}`
          )
        )
        .limit(1);

      // Get total expenses
      const totalExpenses = await db
        .select({
          total: sql<number>`SUM(${expenses.amount})`
        })
        .from(expenses)
        .where(
          and(
            eq(expenses.storeId, storeId),
            sql`${expenses.date} BETWEEN ${startDate} AND ${endDate}`
          )
        )
        .limit(1);

      // Get expense breakdown by category
      const expenseBreakdown = await db
        .select({
          category: expenseCategories.name,
          amount: sql<number>`SUM(${expenses.amount})`
        })
        .from(expenses)
        .leftJoin(expenseCategories, eq(expenseCategories.id, expenses.categoryId))
        .where(
          and(
            eq(expenses.storeId, storeId),
            sql`${expenses.date} BETWEEN ${startDate} AND ${endDate}`
          )
        )
        .groupBy(expenseCategories.name);

      const totalSalesAmount = totalSales[0]?.total || 0;
      const totalExpensesAmount = totalExpenses[0]?.total || 0;

      // Get profit and loss
      const profit = totalSalesAmount - totalExpensesAmount;

      return {
        totalSales: totalSalesAmount,
        totalExpenses: totalExpensesAmount,
        profit,
        expenseBreakdown,
        period: {
          start: startDate,
          end: endDate
        }
      };
    } catch (error) {
      console.error('Error getting financial summary:', error);
      throw error;
    }
  }
}

export const expenseService = new ExpenseService();
