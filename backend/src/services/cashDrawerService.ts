import { db } from '../db';
import {
  cashDrawers,
  cashDrawerTransactions,
  cashDrawerReconciliations,
  users,
  UserRole,
  CashDrawerStatus
} from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';

export interface CashDrawerInput {
  storeId: number;
  name: string;
  openingBalance: number;
  notes?: string;
}

export interface CashDrawerTransactionInput {
  drawerId: number;
  transactionType: string;
  amount: number;
  referenceNumber?: string;
  notes?: string;
}

export interface CashDrawerReconciliationInput {
  drawerId: number;
  actualBalance: number;
  notes?: string;
}

export class CashDrawerService {
  async openCashDrawer(data: CashDrawerInput, userId: number): Promise<number> {
    try {
      // Check if there's an open drawer
      const openDrawerResult = await db
        .select({ id: cashDrawers.id })
        .from(cashDrawers)
        .where(
          and(
            eq(cashDrawers.storeId, data.storeId),
            sql`${cashDrawers.status} IN ('open', 'reconciling')`
          )
        )
        .limit(1);

      const openDrawer = openDrawerResult[0];

      if (openDrawer) {
        throw new Error('A cash drawer is already open for this store');
      }

      const drawerId = await db
        .insert(cashDrawers)
        .values({
          name: data.name,
          storeId: data.storeId,
          status: CashDrawerStatus.OPEN,
          openingBalance: data.openingBalance.toString(),
          actualBalance: data.openingBalance.toString(),
          openedBy: userId,
          openedAt: new Date()
        })
        .returning({ id: cashDrawers.id });

      return drawerId[0].id;
    } catch (error) {
      console.error('Error opening cash drawer:', error);
      throw error;
    }
  }

  async recordTransaction(data: CashDrawerTransactionInput, userId: number): Promise<number> {
    try {
      const drawerResult = await db
        .select({
          id: cashDrawers.id,
          status: cashDrawers.status,
          actualBalance: cashDrawers.actualBalance
        })
        .from(cashDrawers)
        .where(eq(cashDrawers.id, data.drawerId))
        .limit(1);

      const drawer = drawerResult[0];

      if (!drawer || drawer.status !== CashDrawerStatus.OPEN) {
        throw new Error('Cash drawer is not open');
      }

      // Update actual balance
      const currentBalance = parseFloat(drawer.actualBalance || '0');
      let newBalance = currentBalance;
      if (data.transactionType === 'sale' || data.transactionType === 'deposit') {
        newBalance += data.amount;
      } else if (data.transactionType === 'return' || data.transactionType === 'withdrawal') {
        newBalance -= data.amount;
      }

      await db.update(cashDrawers)
        .set({
          actualBalance: newBalance.toString(),
          updatedAt: new Date()
        })
        .where(eq(cashDrawers.id, data.drawerId));

      // Record transaction
      const transactionId = await db
        .insert(cashDrawerTransactions)
        .values({
          cashDrawerId: data.drawerId,
          drawerId: data.drawerId,
          transactionType: data.transactionType,
          amount: data.amount.toString(),
          referenceNumber: data.referenceNumber,
          notes: data.notes,
          createdBy: userId
        })
        .returning();

      return transactionId[0].id;
    } catch (error) {
      console.error('Error recording transaction:', error);
      throw error;
    }
  }

  async closeCashDrawer(drawerId: number, userId: number): Promise<void> {
    try {
      const drawerResult = await db
        .select({
          id: cashDrawers.id,
          status: cashDrawers.status,
          actualBalance: cashDrawers.actualBalance
        })
        .from(cashDrawers)
        .where(eq(cashDrawers.id, drawerId))
        .limit(1);

      const drawer = drawerResult[0];

      if (!drawer || drawer.status !== CashDrawerStatus.OPEN) {
        throw new Error('Cash drawer is not open');
      }

      await db.update(cashDrawers)
        .set({
          status: CashDrawerStatus.CLOSED,
          closingBalance: drawer.actualBalance,
          closedBy: userId,
          closedAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(cashDrawers.id, drawerId));
    } catch (error) {
      console.error('Error closing cash drawer:', error);
      throw error;
    }
  }

  async reconcileCashDrawer(data: CashDrawerReconciliationInput, userId: number): Promise<number> {
    try {
      const drawerResult = await db
        .select({
          id: cashDrawers.id,
          status: cashDrawers.status,
          actualBalance: cashDrawers.actualBalance,
          openingBalance: cashDrawers.openingBalance,
          closingBalance: cashDrawers.closingBalance
        })
        .from(cashDrawers)
        .where(eq(cashDrawers.id, data.drawerId))
        .limit(1);

      const drawer = drawerResult[0];

      if (!drawer || drawer.status !== CashDrawerStatus.CLOSED) {
        throw new Error('Cash drawer must be closed before reconciliation');
      }

      const openingBalance = parseFloat(drawer.openingBalance || '0');
      const closingBalance = parseFloat(drawer.closingBalance || '0');
      const expectedBalance = openingBalance + closingBalance;
      const variance = expectedBalance - data.actualBalance;

      // Create reconciliation record
      const reconciliationId = await db
        .insert(cashDrawerReconciliations)
        .values({
          cashDrawerId: data.drawerId,
          drawerId: data.drawerId,
          expectedAmount: expectedBalance.toString(),
          actualAmount: data.actualBalance.toString(),
          variance: variance.toString(),
          notes: data.notes,
          reconciledBy: userId
        })
        .returning();

      // Update drawer status
      await db.update(cashDrawers)
        .set({
          status: CashDrawerStatus.RECONCILED,
          variance: variance.toString(),
          updatedAt: new Date()
        })
        .where(eq(cashDrawers.id, data.drawerId));

      return reconciliationId[0].id;
    } catch (error) {
      console.error('Error reconciling cash drawer:', error);
      throw error;
    }
  }

  async getDailySummary(storeId: number, date: Date): Promise<any> {
    try {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      const summaryResult = await db
        .select({
          totalSales: sql<number>`SUM(
            CASE WHEN ${cashDrawerTransactions.transactionType} = 'sale' THEN ${cashDrawerTransactions.amount} ELSE 0 END
          )`,
          totalReturns: sql<number>`SUM(
            CASE WHEN ${cashDrawerTransactions.transactionType} = 'return' THEN ${cashDrawerTransactions.amount} ELSE 0 END
          )`,
          totalDeposits: sql<number>`SUM(
            CASE WHEN ${cashDrawerTransactions.transactionType} = 'deposit' THEN ${cashDrawerTransactions.amount} ELSE 0 END
          )`,
          totalWithdrawals: sql<number>`SUM(
            CASE WHEN ${cashDrawerTransactions.transactionType} = 'withdrawal' THEN ${cashDrawerTransactions.amount} ELSE 0 END
          )`,
          totalAdjustments: sql<number>`SUM(
            CASE WHEN ${cashDrawerTransactions.transactionType} = 'adjustment' THEN ${cashDrawerTransactions.amount} ELSE 0 END
          )`,
          openingBalance: sql<number>`COALESCE(SUM(
            CASE WHEN ${cashDrawers.openedAt} BETWEEN ${startOfDay} AND ${endOfDay} 
            THEN ${cashDrawers.openingBalance} ELSE 0 END
          ), 0)`,
          closingBalance: sql<number>`COALESCE(SUM(
            CASE WHEN ${cashDrawers.closedAt} BETWEEN ${startOfDay} AND ${endOfDay} 
            THEN ${cashDrawers.closingBalance} ELSE 0 END
          ), 0)`,
          actualBalance: sql<number>`COALESCE(SUM(
            CASE WHEN ${cashDrawers.closedAt} BETWEEN ${startOfDay} AND ${endOfDay} 
            THEN ${cashDrawers.actualBalance} ELSE 0 END
          ), 0)`,
          totalVariance: sql<number>`COALESCE(SUM(${cashDrawers.variance}), 0)`,
          totalReconciliations: sql<number>`COUNT(DISTINCT ${cashDrawerReconciliations.id})`
        })
        .from(cashDrawers)
        .leftJoin(cashDrawerTransactions, eq(cashDrawerTransactions.drawerId, cashDrawers.id))
        .leftJoin(cashDrawerReconciliations, eq(cashDrawerReconciliations.drawerId, cashDrawers.id))
        .where(
          and(
            eq(cashDrawers.storeId, storeId),
            sql`${cashDrawers.openedAt} BETWEEN ${startOfDay} AND ${endOfDay}`
          )
        )
        .limit(1);

      const summary = summaryResult[0];
      return summary;
    } catch (error) {
      console.error('Error getting daily summary:', error);
      throw error;
    }
  }

  async getTransactionHistory(
    drawerId: number,
    startDate?: string,
    endDate?: string,
    limit = 10,
    page = 1
  ): Promise<any[]> {
    try {
      const whereConditions = [eq(cashDrawerTransactions.drawerId, drawerId)];

      if (startDate && endDate) {
        whereConditions.push(
          sql`${cashDrawerTransactions.createdAt} BETWEEN ${startDate} AND ${endDate}`
        );
      }

      const query = db
        .select({
          id: cashDrawerTransactions.id,
          transactionType: cashDrawerTransactions.transactionType,
          amount: cashDrawerTransactions.amount,
          referenceNumber: cashDrawerTransactions.referenceNumber,
          notes: cashDrawerTransactions.notes,
          createdBy: sql<string>`CONCAT(users.first_name, ' ', users.last_name)`,
          createdAt: cashDrawerTransactions.createdAt
        })
        .from(cashDrawerTransactions)
        .leftJoin(users, eq(users.id, cashDrawerTransactions.createdBy))
        .where(and(...whereConditions))
        .orderBy(sql`cash_drawer_transactions.created_at DESC`);

      return await query
        .limit(limit)
        .offset((page - 1) * limit);
    } catch (error) {
      console.error('Error getting transaction history:', error);
      throw error;
    }
  }
}

export const cashDrawerService = new CashDrawerService();
