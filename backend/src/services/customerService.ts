import { db } from '../db';
import {
  customers,
  sales,
  creditSales,
  customerTypes,
  loyaltyTiers,
  customerPurchases,
  UserRole
} from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';

export interface CustomerProfile {
  id: number;
  name: string;
  phone: string;
  email: string;
  address: string;
  totalSpent: number;
  totalPurchases: number;
  lastPurchaseDate: Date | null;
}

export class CustomerService {
  async createCustomer(data: any): Promise<number> {
    try {
      const result = await db.insert(customers).values(data).returning();
      return result[0].id;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  }

  async updateCustomer(id: number, data: any): Promise<void> {
    try {
      await db.update(customers).set(data).where(eq(customers.id, id));
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  }

  async getCustomerProfile(customerId: number): Promise<CustomerProfile> {
    try {
      const customerResult = await db
        .select({
          id: customers.id,
          name: sql<string>`CONCAT(${customers.firstName}, ' ', ${customers.lastName})`,
          phone: customers.phone,
          email: customers.email,
          address: customers.address,
          customerType: sql<string>`customer_types.name`,
          loyaltyTier: sql<string>`loyalty_tiers.name`,
          preferredStore: sql<string>`preferred_store.name`,
          totalSpent: customers.totalSpent,
          totalPurchases: customers.totalPurchases,
          lastPurchaseDate: customers.lastPurchaseDate,
          points: customers.points
        })
        .from(customers)
        .leftJoin(customerTypes, eq(customerTypes.id, customers.customerTypeId))
        .leftJoin(loyaltyTiers, eq(loyaltyTiers.id, customers.loyaltyTierId))
        .leftJoin(sql`stores as preferred_store`, sql`preferred_store.id = ${customers.preferredStoreId}`)
        .where(eq(customers.id, customerId))
        .limit(1);

      const customer = customerResult[0];

      return customer;
    } catch (error) {
      console.error('Error getting customer profile:', error);
      throw error;
    }
  }

  async updateLoyaltyTier(customerId: number): Promise<void> {
    try {
      const customerResult = await db
        .select({
          points: customers.points,
          currentTierId: customers.loyaltyTierId
        })
        .from(customers)
        .where(eq(customers.id, customerId))
        .limit(1);

      const customer = customerResult[0];

      if (!customer) return;

      const newTierResult = await db
        .select({
          id: loyaltyTiers.id,
          name: loyaltyTiers.name,
          pointsRequired: loyaltyTiers.pointsRequired
        })
        .from(loyaltyTiers)
        .where(sql`${loyaltyTiers.pointsRequired} <= ${customer.points}`)
        .orderBy(sql`points_required DESC`)
        .limit(1);

      const newTier = newTierResult[0];

      if (newTier && newTier.id !== customer.currentTierId) {
        await db.update(customers)
          .set({
            loyaltyTierId: newTier.id,
            updatedAt: new Date()
          })
          .where(eq(customers.id, customerId));
      }
    } catch (error) {
      console.error('Error updating loyalty tier:', error);
      throw error;
    }
  }

  async processPurchase(customerId: number, saleId: number, amount: number): Promise<void> {
    try {
      // Calculate points based on amount
      const points = Math.floor(amount / 100); // 1 point per $100 spent

      // Update customer stats
      await db.transaction(async (tx) => {
        // Update customer
        await tx.update(customers)
          .set({
            totalSpent: sql`total_spent + ${amount}`,
            totalPurchases: sql`total_purchases + 1`,
            lastPurchaseDate: new Date(),
            points: sql`points + ${points}`
          })
          .where(eq(customers.id, customerId));

        // Update loyalty tier
        await this.updateLoyaltyTier(customerId);

        // Create purchase record
        await tx.insert(customerPurchases).values({
          customerId,
          saleId,
          amount: amount.toString(),
          pointsEarned: points
        });

        // Update preferred store if needed
        const saleResult = await tx
          .select({ storeId: sales.storeId })
          .from(sales)
          .where(eq(sales.id, saleId))
          .limit(1);

        const sale = saleResult[0];

        if (sale) {
          const currentPreferredStoreResult = await tx
            .select({ storeId: customers.preferredStoreId })
            .from(customers)
            .where(eq(customers.id, customerId))
            .limit(1);

          const currentPreferredStore = currentPreferredStoreResult[0];

          if (!currentPreferredStore?.storeId || currentPreferredStore.storeId !== sale.storeId) {
            await tx.update(customers)
              .set({ preferredStoreId: sale.storeId })
              .where(eq(customers.id, customerId));
          }
        }
      });
    } catch (error) {
      console.error('Error processing purchase:', error);
      throw error;
    }
  }

  async getCustomerHistory(customerId: number): Promise<any[]> {
    try {
      const history = await db
        .select({
          id: sales.id,
          date: sales.createdAt,
          amount: sales.totalAmount,
          store: sql<string>`stores.name`,
          isBnpl: sql<boolean>`EXISTS (
            SELECT 1 FROM credit_sales WHERE credit_sales.sale_id = sales.id
          )`
        })
        .from(sales)
        .leftJoin(sql`stores`, sql`stores.id = ${sales.storeId}`)
        .where(
          sql`EXISTS (
            SELECT 1 FROM customer_purchases 
            WHERE customer_purchases.customer_id = ${customerId} 
            AND customer_purchases.sale_id = sales.id
          )`
        )
        .orderBy(sql`sales.created_at DESC`);

      return history;
    } catch (error) {
      console.error('Error getting customer history:', error);
      throw error;
    }
  }

  async getLoyaltyDiscount(customerId: number): Promise<number> {
    try {
      const customerResult = await db
        .select({
          discountRate: sql<number>`loyalty_tiers.discount_rate`
        })
        .from(customers)
        .leftJoin(loyaltyTiers, eq(loyaltyTiers.id, customers.loyaltyTierId))
        .where(eq(customers.id, customerId))
        .limit(1);

      const customer = customerResult[0];

      return customer?.discountRate || 0;
    } catch (error) {
      console.error('Error getting loyalty discount:', error);
      throw error;
    }
  }

  async getCustomerTypeDiscount(customerId: number): Promise<number> {
    try {
      const customerResult2 = await db
        .select({
          discountRate: sql<number>`customer_types.discount_rate`
        })
        .from(customers)
        .leftJoin(customerTypes, eq(customerTypes.id, customers.customerTypeId))
        .where(eq(customers.id, customerId))
        .limit(1);

      const customer = customerResult2[0];

      return customer?.discountRate || 0;
    } catch (error) {
      console.error('Error getting customer type discount:', error);
      throw error;
    }
  }
}

export const customerService = new CustomerService();
