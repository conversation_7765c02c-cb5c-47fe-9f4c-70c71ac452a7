import { db } from '../db';
import { 
  products, 
  productVariants, 
  variantAttributes, 
  attributeValues, 
  productAttributes,
  inventory,
  UserRole,
  salesItems
} from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';
// Note: onConflictDoUpdate is not needed for current implementation

export interface ProductVariant {
  id: number;
  productId: number;
  sku: string;
  name: string;
  description: string;
  basePrice: number;
  costPrice: number;
  weight: number;
  length: number;
  width: number;
  height: number;
  attributes: Array<{
    name: string;
    value: string;
  }>;
  stock: number;
  threshold: number;
  isDefault: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export class ProductVariantService {
  async createVariant(data: any): Promise<number> {
    try {
      // Create variant
      const variantResult = await db.insert(productVariants).values(data).returning();
      const variantId = variantResult[0].id;

      // Create variant attributes if provided
      if (data.attributes) {
        await Promise.all(
          data.attributes.map(async (attribute: any) => {
            const attributeValueResult = await db
              .select({ id: attributeValues.id })
              .from(attributeValues)
              .where(
                and(
                  eq(attributeValues.attributeId, attribute.attributeId),
                  eq(attributeValues.value, attribute.value)
                )
              )
              .limit(1);

            const attributeValue = attributeValueResult[0];
            if (!attributeValue) {
              throw new Error(`Attribute value not found: ${attribute.value}`);
            }

            await db.insert(variantAttributes).values({
              variantId,
              attributeId: attribute.attributeId,
              valueId: attributeValue.id
            });
          })
        );
      }

      return variantId;
    } catch (error) {
      console.error('Error creating variant:', error);
      throw error;
    }
  }

  async getVariants(productId: number): Promise<ProductVariant[]> {
    try {
      const variants = await db
        .select({
          variant: productVariants,
          attributes: sql`json_agg(
            json_build_object(
              'name', product_attributes.name,
              'value', attribute_values.value
            )
          )`,
          stock: sql<number>`COALESCE(SUM(${inventory.quantity}), 0)`,
          threshold: sql<number>`MIN(${inventory.threshold})`
        })
        .from(productVariants)
        .leftJoin(variantAttributes, eq(variantAttributes.variantId, productVariants.id))
        .leftJoin(attributeValues, eq(attributeValues.id, variantAttributes.valueId))
        .leftJoin(productAttributes, eq(productAttributes.id, attributeValues.attributeId))
        .leftJoin(inventory, eq(inventory.variantId, productVariants.id))
        .where(eq(productVariants.productId, productId))
        .groupBy(productVariants.id);

      return variants.map((variant: any) => ({
        id: variant.id,
        productId: variant.productId,
        sku: variant.sku,
        name: variant.name,
        description: variant.description,
        basePrice: variant.basePrice,
        costPrice: variant.costPrice,
        weight: variant.weight,
        dimensions: {
          length: variant.length,
          width: variant.width,
          height: variant.height
        },
        attributes: variant.attributes || [],
        stock: variant.stock || 0,
        threshold: variant.threshold || 0,
        isDefault: variant.isDefault,
        isActive: variant.isActive
      }));
    } catch (error) {
      console.error('Error getting variants:', error);
      throw error;
    }
  }

  async getDefaultVariant(productId: number): Promise<ProductVariant | null> {
    try {
      const variants = await this.getVariants(productId);
      return variants.find(v => v.isDefault) || null;
    } catch (error) {
      console.error('Error getting default variant:', error);
      throw error;
    }
  }

  async deleteVariant(variantId: number): Promise<void> {
    try {
      // Delete variant attributes first
      await db.delete(variantAttributes)
        .where(eq(variantAttributes.variantId, variantId));

      // Then delete the variant
      await db.delete(productVariants)
        .where(eq(productVariants.id, variantId));
    } catch (error) {
      console.error('Error deleting variant:', error);
      throw error;
    }
  }

  async setDefaultVariant(productId: number, variantId: number): Promise<void> {
    try {
      // First set all variants to not default
      await db.update(productVariants)
        .set({ isDefault: false })
        .where(eq(productVariants.productId, productId));

      // Then set the specified variant as default
      await db.update(productVariants)
        .set({ isDefault: true })
        .where(eq(productVariants.id, variantId));

      // Update any existing sales items to use the new default variant
      await db.update(salesItems)
        .set({ variantId })
        .where(
          and(
            eq(salesItems.productId, productId),
            sql`${salesItems.variantId} IS NULL`
          )
        );
    } catch (error) {
      console.error('Error setting default variant:', error);
      throw error;
    }
  }

  async updateVariantStock(variantId: number, storeId: number, quantity: number, productId: number): Promise<void> {
    try {
      // First try to update existing inventory
      const existingInventory = await db
        .select()
        .from(inventory)
        .where(and(
          eq(inventory.variantId, variantId),
          eq(inventory.storeId, storeId)
        ))
        .limit(1);

      if (existingInventory.length > 0) {
        await db.update(inventory)
          .set({
            quantity,
            updatedAt: new Date()
          })
          .where(and(
            eq(inventory.variantId, variantId),
            eq(inventory.storeId, storeId)
          ));
      } else {
        await db
          .insert(inventory)
          .values({
            productId,
            variantId,
            storeId,
            quantity,
            threshold: 5,
            createdAt: new Date(),
            updatedAt: new Date()
          });
      }
    } catch (error) {
      console.error('Error updating variant stock:', error);
      throw error;
    }
  }

  async updateVariant(variantId: number, data: Partial<ProductVariant>): Promise<void> {
    try {
      await db.update(productVariants)
        .set(data)
        .where(eq(productVariants.id, variantId));

      // Update attributes if provided
      if (data.attributes) {
        // Delete existing attributes
        await db.delete(variantAttributes)
          .where(eq(variantAttributes.variantId, variantId));

        // Insert new attributes
        await Promise.all(
          data.attributes.map(async (attributeData: any) => {
            const attributeResult = await db
              .select({ id: productAttributes.id })
              .from(productAttributes)
              .where(eq(productAttributes.name, attributeData.name))
              .limit(1);

            const attribute = attributeResult[0];
            if (!attribute) {
              throw new Error(`Attribute not found: ${attributeData.name}`);
            }

            const attributeValueResult = await db
              .select({ id: attributeValues.id })
              .from(attributeValues)
              .where(
                and(
                  eq(attributeValues.attributeId, attribute.id),
                  eq(attributeValues.value, attributeData.value)
                )
              )
              .limit(1);

            const attributeValue = attributeValueResult[0];
            if (!attributeValue) {
              throw new Error(`Attribute value not found: ${attributeData.value}`);
            }

            await db.insert(variantAttributes).values({
              variantId,
              attributeId: attribute.id,
              valueId: attributeValue.id
            });
          })
        );
      }
    } catch (error) {
      console.error('Error updating variant:', error);
      throw error;
    }
  }

  async getVariantStock(variantId: number, storeId: number): Promise<any> {
    try {
      const stockResult = await db
        .select({
          quantity: inventory.quantity,
          threshold: inventory.threshold
        })
        .from(inventory)
        .where(
          and(
            eq(inventory.variantId, variantId),
            eq(inventory.storeId, storeId)
          )
        )
        .limit(1);

      return stockResult[0] || { quantity: 0, threshold: 5 };
    } catch (error) {
      console.error('Error getting variant stock:', error);
      throw error;
    }
  }



  async getAvailableVariants(productId: number, storeId: number): Promise<ProductVariant[]> {
    try {
      const variants = await db
        .select({
          variant: productVariants,
          attributes: sql`json_agg(
            json_build_object(
              'name', product_attributes.name,
              'value', variant_attributes.value
            )
          )`,
          stock: sql`COALESCE(SUM(${inventory.quantity}), 0)`
        })
        .from(productVariants)
        .leftJoin(variantAttributes, eq(variantAttributes.variantId, productVariants.id))
        .leftJoin(productAttributes, eq(productAttributes.id, variantAttributes.attributeId))
        .leftJoin(inventory, and(
          eq(inventory.variantId, productVariants.id),
          eq(inventory.storeId, storeId)
        ))
        .where(eq(productVariants.productId, productId))
        .groupBy(productVariants.id);

      return variants
        .map((variant) => ({
          id: variant.variant.id,
          productId: variant.variant.productId,
          sku: variant.variant.sku,
          name: variant.variant.name,
          description: variant.variant.description,
          basePrice: variant.variant.basePrice,
          costPrice: variant.variant.costPrice,
          weight: variant.variant.weight,
          length: variant.variant.length,
          width: variant.variant.width,
          height: variant.variant.height,
          attributes: variant.attributes,
          stock: variant.stock,
          threshold: variant.variant.threshold,
          isDefault: variant.variant.isDefault,
          isActive: variant.variant.isActive,
          createdAt: variant.variant.createdAt,
          updatedAt: variant.variant.updatedAt
        }))
        .filter(v => v.stock > 0 && v.isActive);
    } catch (error) {
      console.error('Error getting available variants:', error);
      throw error;
    }
  }

  async getVariantStock(variantId: number, storeId: number): Promise<number> {
    try {
      const resultArray = await db
        .select({ total: sql`SUM(${inventory.quantity})` })
        .from(inventory)
        .where(
          and(
            eq(inventory.variantId, variantId),
            eq(inventory.storeId, storeId)
          )
        )
        .limit(1);

      const result = resultArray[0];

      return result?.total || 0;
    } catch (error) {
      console.error('Error getting variant stock:', error);
      throw error;
    }
  }

  async checkStockThreshold(productId: number, storeId: number): Promise<{ variantId: number; currentStock: number; threshold: number }[]> {
    try {
      const lowStockVariants = await db
        .select({
          variantId: inventory.variantId,
          currentStock: sql<number>`SUM(${inventory.quantity})`,
          threshold: inventory.threshold
        })
        .from(inventory)
        .leftJoin(productVariants, eq(productVariants.id, inventory.variantId))
        .where(
          and(
            eq(productVariants.productId, productId),
            eq(inventory.storeId, storeId),
            sql`SUM(${inventory.quantity}) < ${inventory.threshold}`
          )
        )
        .groupBy(inventory.variantId, inventory.threshold);

      return lowStockVariants;
    } catch (error) {
      console.error('Error checking stock threshold:', error);
      throw error;
    }
  }

  async updateVariantAttributes(variantId: number, attributes: Array<{ name: string; value: string }>): Promise<void> {
    try {
      // Delete existing attributes
      await db.delete(variantAttributes)
        .where(eq(variantAttributes.variantId, variantId));

      // Insert new attributes
      await Promise.all(
        attributes.map(async (attribute) => {
          const attributeResult = await db
            .select({ id: productAttributes.id })
            .from(productAttributes)
            .where(eq(productAttributes.name, attribute.name))
            .limit(1);

          const attributeId = attributeResult[0];
          if (!attributeId) {
            throw new Error(`Attribute not found: ${attribute.name}`);
          }

          await db.insert(variantAttributes).values({
            variantId,
            attributeId: attributeId.id,
            customValue: attribute.value
          });
        })
      );
    } catch (error) {
      console.error('Error updating variant attributes:', error);
      throw error;
    }
  }
}

export const productVariantService = new ProductVariantService();
