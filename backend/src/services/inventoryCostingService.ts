import { db } from '../db';
import {
  inventory,
  inventoryTransactions,
  stores,
  users,
  productVariants,
  UserRole,
  InventoryCostingMethod
} from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';

export interface InventoryTransactionInput {
  storeId: number;
  variantId: number;
  transactionType: string;
  quantity: number;
  unitCost: number;
  referenceNumber?: string;
  notes?: string;
}

export class InventoryCostingService {
  async recordTransaction(data: InventoryTransactionInput, userId: number): Promise<number> {
    try {
      const storeResult = await db
        .select({
          costingMethod: stores.inventoryCostingMethod
        })
        .from(stores)
        .where(eq(stores.id, data.storeId))
        .limit(1);

      const store = storeResult[0];

      if (!store) {
        throw new Error('Store not found');
      }

      const transactionId = await db
        .insert(inventoryTransactions)
        .values({
          ...data,
          createdBy: userId,
          totalCost: data.quantity * data.unitCost
        })
        .returning();

      await this.updateInventoryCost(
        data.storeId,
        data.variantId,
        data.quantity,
        data.unitCost,
        store.costingMethod as InventoryCostingMethod
      );

      return transactionId[0].id;
    } catch (error) {
      console.error('Error recording transaction:', error);
      throw error;
    }
  }

  private async updateInventoryCost(
    storeId: number,
    variantId: number,
    quantity: number,
    unitCost: number,
    costingMethod: InventoryCostingMethod
  ): Promise<void> {
    try {
      const currentInventoryResult = await db
        .select({
          quantity: inventory.quantity,
          unitCost: inventory.unitCost,
          totalCost: inventory.totalCost
        })
        .from(inventory)
        .where(
          and(
            eq(inventory.storeId, storeId),
            eq(inventory.variantId, variantId)
          )
        )
        .limit(1);

      const currentInventory = currentInventoryResult[0];

      let newQuantity = (currentInventory?.quantity || 0) + quantity;
      let newUnitCost: number;
      let newTotalCost: number;

      switch (costingMethod) {
        case InventoryCostingMethod.FIFO:
          // FIFO: Oldest inventory is sold first
          newTotalCost = currentInventory?.totalCost || 0;
          newUnitCost = unitCost;
          break;

        case InventoryCostingMethod.LIFO:
          // LIFO: Newest inventory is sold first
          newTotalCost = currentInventory?.totalCost || 0;
          newUnitCost = unitCost;
          break;

        case InventoryCostingMethod.AVERAGE:
          // Average Costing: Average of all purchases
          const totalQuantity = (currentInventory?.quantity || 0) + quantity;
          const totalCost = (currentInventory?.totalCost || 0) + (quantity * unitCost);
          newUnitCost = totalQuantity > 0 ? totalCost / totalQuantity : unitCost;
          newTotalCost = totalQuantity * newUnitCost;
          break;

        default:
          throw new Error(`Invalid costing method: ${costingMethod}`);
      }

      await db
        .insert(inventory)
        .values({
          storeId,
          variantId,
          quantity: newQuantity,
          unitCost: newUnitCost,
          totalCost: newTotalCost
        })
        .onConflictDoUpdate({
          target: [inventory.storeId, inventory.variantId],
          set: {
            quantity: newQuantity,
            unitCost: newUnitCost,
            totalCost: newTotalCost,
            lastUpdated: new Date()
          }
        });
    } catch (error) {
      console.error('Error updating inventory cost:', error);
      throw error;
    }
  }

  async getInventoryCost(
    storeId: number,
    variantId: number,
    quantity: number,
    costingMethod: InventoryCostingMethod
  ): Promise<number> {
    try {
      const inventoryRecordResult = await db
        .select({
          quantity: inventory.quantity,
          unitCost: inventory.unitCost,
          totalCost: inventory.totalCost
        })
        .from(inventory)
        .where(
          and(
            eq(inventory.storeId, storeId),
            eq(inventory.variantId, variantId)
          )
        )
        .limit(1);

      const inventoryRecord = inventoryRecordResult[0];

      if (!inventoryRecord) {
        return 0;
      }

      let cost: number;

      switch (costingMethod) {
        case InventoryCostingMethod.FIFO:
          // FIFO: Use oldest cost
          const oldestTransactionResult = await db
            .select({
              unitCost: inventoryTransactions.unitCost
            })
            .from(inventoryTransactions)
            .where(
              and(
                eq(inventoryTransactions.storeId, storeId),
                eq(inventoryTransactions.variantId, variantId),
                eq(inventoryTransactions.transactionType, 'purchase')
              )
            )
            .orderBy(sql`inventory_transactions.created_at ASC`)
            .limit(1);

          const oldestTransaction = oldestTransactionResult[0];

          cost = oldestTransaction?.unitCost || inventoryRecord.unitCost;
          break;

        case InventoryCostingMethod.LIFO:
          // LIFO: Use newest cost
          const newestTransactionResult = await db
            .select({
              unitCost: inventoryTransactions.unitCost
            })
            .from(inventoryTransactions)
            .where(
              and(
                eq(inventoryTransactions.storeId, storeId),
                eq(inventoryTransactions.variantId, variantId),
                eq(inventoryTransactions.transactionType, 'purchase')
              )
            )
            .orderBy(sql`inventory_transactions.created_at DESC`)
            .limit(1);

          const newestTransaction = newestTransactionResult[0];

          cost = newestTransaction?.unitCost || inventoryRecord.unitCost;
          break;

        case InventoryCostingMethod.AVERAGE:
          // Average Costing: Use current average
          cost = inventoryRecord.unitCost;
          break;

        default:
          throw new Error(`Invalid costing method: ${costingMethod}`);
      }

      return cost * quantity;
    } catch (error) {
      console.error('Error calculating inventory cost:', error);
      throw error;
    }
  }

  async getCOGS(
    storeId: number,
    variantId: number,
    quantity: number,
    saleDate: Date
  ): Promise<number> {
    try {
      const storeResult = await db
        .select({
          costingMethod: stores.inventoryCostingMethod
        })
        .from(stores)
        .where(eq(stores.id, storeId))
        .limit(1);

      const store = storeResult[0];

      if (!store) {
        throw new Error('Store not found');
      }

      const cost = await this.getInventoryCost(
        storeId,
        variantId,
        quantity,
        store.costingMethod as InventoryCostingMethod
      );

      // Record sale transaction
      await db.insert(inventoryTransactions).values({
        storeId,
        variantId,
        transactionType: 'sale',
        quantity: -quantity,
        unitCost: cost / quantity,
        totalCost: -cost,
        referenceNumber: `SALE-${saleDate.toISOString().split('T')[0]}`
      });

      return cost;
    } catch (error) {
      console.error('Error calculating COGS:', error);
      throw error;
    }
  }

  async getInventoryValuation(
    storeId: number,
    variantId?: number,
    startDate?: string,
    endDate?: string
  ): Promise<any[]> {
    try {
      const whereConditions = [eq(inventory.storeId, storeId)];

      if (variantId) {
        whereConditions.push(eq(inventory.variantId, variantId));
      }

      if (startDate && endDate) {
        whereConditions.push(
          sql`${inventory.lastUpdated} BETWEEN ${startDate} AND ${endDate}`
        );
      }

      return await db
        .select({
          variantId: inventory.variantId,
          variantName: productVariants.name,
          quantity: inventory.quantity,
          unitCost: inventory.unitCost,
          totalCost: inventory.totalCost,
          lastUpdated: inventory.lastUpdated
        })
        .from(inventory)
        .leftJoin(productVariants, eq(productVariants.id, inventory.variantId))
        .where(and(...whereConditions));
    } catch (error) {
      console.error('Error getting inventory valuation:', error);
      throw error;
    }
  }

  async getInventoryHistory(
    storeId: number,
    variantId: number,
    startDate?: string,
    endDate?: string
  ): Promise<any[]> {
    try {
      let query = db
        .select({
          transactionId: inventoryTransactions.id,
          transactionType: inventoryTransactions.transactionType,
          quantity: inventoryTransactions.quantity,
          unitCost: inventoryTransactions.unitCost,
          totalCost: inventoryTransactions.totalCost,
          referenceNumber: inventoryTransactions.referenceNumber,
          notes: inventoryTransactions.notes,
          createdBy: sql<string>`CONCAT(users.first_name, ' ', users.last_name)`,
          createdAt: inventoryTransactions.createdAt
        })
        .from(inventoryTransactions)
        .leftJoin(users, eq(users.id, inventoryTransactions.createdBy))
        .where(
          and(
            eq(inventoryTransactions.storeId, storeId),
            eq(inventoryTransactions.variantId, variantId)
          )
        )
        .orderBy(sql`inventory_transactions.created_at DESC`);

      if (startDate && endDate) {
        query = query.where(
          sql`${inventoryTransactions.createdAt} BETWEEN ${startDate} AND ${endDate}`
        );
      }

      return query;
    } catch (error) {
      console.error('Error getting inventory history:', error);
      throw error;
    }
  }
}

export const inventoryCostingService = new InventoryCostingService();
