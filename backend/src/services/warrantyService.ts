import { db } from '../db';
import {
  warrantyTypes,
  productWarranties,
  saleWarranties,
  warrantyClaims,
  warrantyNotifications,
  sales,
  salesItems,
  customers,
  products,
  UserRole
} from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { sendEmail } from '../utils/email';
import { SMSService } from '../utils/sms';

export interface WarrantyTypeInput {
  name: string;
  description?: string;
  durationMonths: number;
  termsAndConditions?: string;
}

export interface WarrantyInput {
  productId: number;
  warrantyTypeId: number;
  durationMonths: number;
  termsAndConditions?: string;
}

export interface SaleWarrantyInput {
  saleItemId: number;
  warrantyId: number;
  maxClaims?: number;
  notes?: string;
}

export class WarrantyService {
  async createWarrantyType(data: WarrantyTypeInput): Promise<number> {
    try {
      const result = await db.insert(warrantyTypes).values(data).returning();
      return result[0].id;
    } catch (error) {
      console.error('Error creating warranty type:', error);
      throw error;
    }
  }

  async createWarranty(data: WarrantyInput): Promise<number> {
    try {
      const result = await db.insert(productWarranties).values(data).returning();
      return result[0].id;
    } catch (error) {
      console.error('Error creating warranty:', error);
      throw error;
    }
  }

  async createSaleWarranty(data: SaleWarrantyInput): Promise<number> {
    try {
      const saleItemResult = await db
        .select({
          saleId: salesItems.saleId,
          productId: salesItems.productId,
          variantId: salesItems.variantId,
          customerId: sales.customerId,
          customerEmail: customers.email,
          customerPhone: customers.phone
        })
        .from(salesItems)
        .leftJoin(sales, eq(sales.id, salesItems.saleId))
        .leftJoin(customers, eq(customers.id, sales.customerId))
        .where(eq(salesItems.id, data.saleItemId))
        .limit(1);

      const saleItem = saleItemResult[0];
      if (!saleItem) {
        throw new Error('Sale item not found');
      }

      const warrantyResult = await db
        .select({
          durationMonths: productWarranties.durationMonths
        })
        .from(productWarranties)
        .where(eq(productWarranties.id, data.warrantyId))
        .limit(1);

      const warranty = warrantyResult[0];
      if (!warranty) {
        throw new Error('Warranty not found');
      }

      const endDate = new Date();
      endDate.setMonth(endDate.getMonth() + (warranty.durationMonths || 12));

      const result = await db.insert(saleWarranties).values({
        saleId: saleItem.saleId,
        productId: saleItem.productId,
        variantId: saleItem.variantId,
        warrantyTypeId: data.warrantyId,
        saleItemId: data.saleItemId,
        startDate: new Date(),
        endDate,
        status: 'active'
      }).returning();

      // Send warranty activation notification
      await this.sendWarrantyNotification(
        result[0].id,
        saleItem.customerEmail || '',
        saleItem.customerPhone || '',
        'activation'
      );

      return result[0].id;
    } catch (error) {
      console.error('Error creating sale warranty:', error);
      throw error;
    }
  }

  async createWarrantyClaim(data: any, userId: number): Promise<number> {
    try {
      const result = await db.insert(warrantyClaims).values({
        ...data,
        createdBy: userId,
        status: 'pending'
      }).returning();

      // Send claim notification
      const saleWarrantyResult = await db
        .select({
          customerId: sales.customerId,
          customerEmail: customers.email,
          customerPhone: customers.phone
        })
        .from(saleWarranties)
        .leftJoin(salesItems, eq(salesItems.id, saleWarranties.saleItemId))
        .leftJoin(sales, eq(sales.id, salesItems.saleId))
        .leftJoin(customers, eq(customers.id, sales.customerId))
        .where(eq(saleWarranties.id, data.saleWarrantyId))
        .limit(1);

      const saleWarranty = saleWarrantyResult[0];
      if (saleWarranty) {
        await this.sendWarrantyNotification(
          data.saleWarrantyId,
          saleWarranty.customerEmail || '',
          saleWarranty.customerPhone || '',
          'claim'
        );
      }

      return result[0].id;
    } catch (error) {
      console.error('Error creating warranty claim:', error);
      throw error;
    }
  }

  async getWarrantyTypes(): Promise<any[]> {
    try {
      return await db
        .select({
          id: warrantyTypes.id,
          name: warrantyTypes.name,
          description: warrantyTypes.description,
          durationMonths: warrantyTypes.durationMonths,
          termsAndConditions: warrantyTypes.termsAndConditions,
          isActive: warrantyTypes.isActive
        })
        .from(warrantyTypes);
    } catch (error) {
      console.error('Error getting warranty types:', error);
      throw error;
    }
  }

  async getWarranties(productId: number): Promise<any[]> {
    try {
      return await db
        .select({
          id: productWarranties.id,
          warrantyType: warrantyTypes.name,
          durationMonths: productWarranties.durationMonths,
          termsAndConditions: productWarranties.termsAndConditions,
          isActive: productWarranties.isActive
        })
        .from(productWarranties)
        .leftJoin(warrantyTypes, eq(warrantyTypes.id, productWarranties.warrantyTypeId))
        .where(eq(productWarranties.productId, productId));
    } catch (error) {
      console.error('Error getting warranties:', error);
      throw error;
    }
  }

  async getSaleWarranties(
    storeId: number,
    status?: string,
    startDate?: string,
    endDate?: string,
    limit = 10,
    page = 1
  ): Promise<any[]> {
    try {
      const whereConditions = [eq(sales.storeId, storeId)];

      if (status) {
        whereConditions.push(eq(saleWarranties.status, status));
      }

      if (startDate && endDate) {
        whereConditions.push(
          sql`${saleWarranties.startDate} BETWEEN ${startDate} AND ${endDate}`
        );
      }

      return await db
        .select({
          id: saleWarranties.id,
          saleItem: salesItems.id,
          product: products.name,
          warrantyType: warrantyTypes.name,
          startDate: saleWarranties.startDate,
          endDate: saleWarranties.endDate,
          status: saleWarranties.status,
          claimCount: saleWarranties.claimCount,
          maxClaims: saleWarranties.maxClaims,
          customer: sql<string>`CONCAT(customers.first_name, ' ', customers.last_name)`,
          customerEmail: customers.email,
          customerPhone: customers.phone
        })
        .from(saleWarranties)
        .leftJoin(salesItems, eq(salesItems.id, saleWarranties.saleItemId))
        .leftJoin(sales, eq(sales.id, salesItems.saleId))
        .leftJoin(customers, eq(customers.id, sales.customerId))
        .leftJoin(products, eq(products.id, salesItems.productId))
        .leftJoin(productWarranties, eq(productWarranties.id, saleWarranties.warrantyId))
        .leftJoin(warrantyTypes, eq(warrantyTypes.id, productWarranties.warrantyTypeId))
        .where(and(...whereConditions))
        .orderBy(sql`sale_warranties.startDate DESC`)
        .limit(limit)
        .offset((page - 1) * limit);
    } catch (error) {
      console.error('Error getting sale warranties:', error);
      throw error;
    }
  }

  async getExpiringWarranties(days: number): Promise<any[]> {
    try {
      const date = new Date();
      date.setDate(date.getDate() + days);

      return await db
        .select({
          id: saleWarranties.id,
          saleItem: salesItems.id,
          product: products.name,
          warrantyType: warrantyTypes.name,
          endDate: saleWarranties.endDate,
          customer: sql<string>`CONCAT(customers.first_name, ' ', customers.last_name)`,
          customerEmail: customers.email,
          customerPhone: customers.phone
        })
        .from(saleWarranties)
        .leftJoin(salesItems, eq(salesItems.id, saleWarranties.saleItemId))
        .leftJoin(sales, eq(sales.id, salesItems.saleId))
        .leftJoin(customers, eq(customers.id, sales.customerId))
        .leftJoin(products, eq(products.id, salesItems.productId))
        .leftJoin(productWarranties, eq(productWarranties.id, saleWarranties.warrantyId))
        .leftJoin(warrantyTypes, eq(warrantyTypes.id, productWarranties.warrantyTypeId))
        .where(
          and(
            eq(saleWarranties.status, 'active'),
            sql`${saleWarranties.endDate} <= ${date}`
          )
        );
    } catch (error) {
      console.error('Error getting expiring warranties:', error);
      throw error;
    }
  }

  private async sendWarrantyNotification(
    saleWarrantyId: number,
    email: string,
    phone: string,
    type: 'activation' | 'claim' | 'expiry_reminder'
  ): Promise<void> {
    try {
      const saleWarrantyResult = await db
        .select({
          product: products.name,
          endDate: saleWarranties.endDate,
          customerId: sales.customerId
        })
        .from(saleWarranties)
        .leftJoin(salesItems, eq(salesItems.id, saleWarranties.saleItemId))
        .leftJoin(sales, eq(sales.id, salesItems.saleId))
        .leftJoin(products, eq(products.id, salesItems.productId))
        .where(eq(saleWarranties.id, saleWarrantyId))
        .limit(1);

      const saleWarranty = saleWarrantyResult[0];
      if (!saleWarranty) {
        throw new Error('Sale warranty not found');
      }

      // Send email notification
      await sendEmail({
        to: email,
        subject: type === 'expiry_reminder'
          ? 'Warranty Expiration Reminder'
          : 'Warranty Information',
        text: `Warranty ${type} for product: ${saleWarranty.product}. End date: ${saleWarranty.endDate}`
      });

      // Send SMS notification
      await SMSService.sendSMS({
        to: phone,
        message: `Warranty ${type} for product: ${saleWarranty.product}. End date: ${saleWarranty.endDate}`
      });

      await db.insert(warrantyNotifications).values({
        saleWarrantyId,
        customerId: saleWarranty.customerId || 1,
        type,
        message: `Warranty ${type} for product: ${saleWarranty.product}. End date: ${saleWarranty.endDate}`,
        status: 'sent'
      });

      await db.insert(warrantyNotifications).values({
        saleWarrantyId,
        customerId: saleWarranty.customerId || 1,
        type,
        message: `Warranty ${type} for product: ${saleWarranty.product}. End date: ${saleWarranty.endDate}`,
        status: 'sent'
      });
    } catch (error) {
      console.error('Error sending warranty notification:', error);
      throw error;
    }
  }
}

export const warrantyService = new WarrantyService();
