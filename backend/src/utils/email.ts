// Email utility functions
export interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
}

export class EmailService {
  static async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      // TODO: Implement actual email sending logic
      // This could use nodemailer, SendGrid, AWS SES, etc.
      console.log('Email would be sent:', options);
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  static async sendWarrantyNotification(
    customerEmail: string,
    productName: string,
    warrantyEndDate: Date,
    type: 'expiring' | 'expired'
  ): Promise<boolean> {
    const subject = type === 'expiring' 
      ? `Your warranty for ${productName} is expiring soon`
      : `Your warranty for ${productName} has expired`;
    
    const message = type === 'expiring'
      ? `Your warranty for ${productName} will expire on ${warrantyEndDate.toDateString()}. Please contact us if you need assistance.`
      : `Your warranty for ${productName} expired on ${warrantyEndDate.toDateString()}. Extended warranty options may be available.`;

    return this.sendEmail({
      to: customerEmail,
      subject,
      text: message,
      html: `<p>${message}</p>`
    });
  }

  static async sendCreditReminder(
    customerEmail: string,
    amount: number,
    dueDate: Date
  ): Promise<boolean> {
    const subject = `Payment Reminder - Amount Due: $${amount}`;
    const message = `You have an outstanding balance of $${amount} due on ${dueDate.toDateString()}. Please make your payment to avoid late fees.`;

    return this.sendEmail({
      to: customerEmail,
      subject,
      text: message,
      html: `<p>${message}</p>`
    });
  }
}

// Export convenience functions for backward compatibility
export const sendEmail = EmailService.sendEmail.bind(EmailService);
