// Font utilities for PDF generation
export const FONTS = {
  HELVETICA: 'Helvetica',
  HELVETICA_BOLD: 'Helvetica-Bold',
  TIMES_ROMAN: 'Times-Roman',
  TIMES_BOLD: 'Times-Bold',
  COURIER: 'Courier',
  COURIER_BOLD: 'Courier-Bold'
} as const;

export type FontName = typeof FONTS[keyof typeof FONTS];

export const DEFAULT_FONT = FONTS.HELVETICA;
export const DEFAULT_FONT_SIZE = 12;
export const HEADER_FONT_SIZE = 16;
export const TITLE_FONT_SIZE = 20;

// Export convenience function for backward compatibility
export const getFont = (fontName?: FontName): FontName => {
  return fontName || DEFAULT_FONT;
};
