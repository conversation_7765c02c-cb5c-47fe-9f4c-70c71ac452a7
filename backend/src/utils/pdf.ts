import { PDFDocument, StandardFonts } from 'pdf-lib';

export interface ReceiptData {
  title: string;
  store: number;
  items: Array<{
    product: {
      name: string;
      sku: string;
      retailPrice: number;
    };
    quantity: number;
    unitPrice: number;
    subtotal: number;
  }>;
  total: number;
  discount: number;
  paymentMethod: string;
  date: Date;
}

export const generatePDF = async (data: ReceiptData): Promise<Uint8Array> => {
  const pdfDoc = await PDFDocument.create();
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const page = pdfDoc.addPage([600, 800]);

  const drawText = (text: string, x: number, y: number, size: number = 12) => {
    page.drawText(text, {
      x,
      y,
      font,
      size,
    });
  };

  // Header
  drawText(data.title, 50, 750, 18);
  drawText(new Date().toLocaleDateString(), 400, 750, 12);

  // Store info
  drawText('Store:', 50, 730, 12);
  drawText(data.store.toString(), 100, 730, 12);

  // Items table
  const tableStart = 700;
  const rowHeight = 20;
  const headerY = tableStart;
  const headers = ['Product', 'Quantity', 'Unit Price', 'Subtotal'];

  // Draw table headers
  headers.forEach((header, i) => {
    drawText(header, 50 + (i * 120), headerY, 12);
  });

  // Draw table rows
  let currentY = headerY - rowHeight;
  data.items.forEach(item => {
    drawText(item.product.name, 50, currentY, 12);
    drawText(item.quantity.toString(), 170, currentY, 12);
    drawText(item.unitPrice.toFixed(2), 290, currentY, 12);
    drawText(item.subtotal.toFixed(2), 410, currentY, 12);
    currentY -= rowHeight;
  });

  // Totals
  drawText('Total:', 290, currentY - 20, 12);
  drawText(data.total.toFixed(2), 410, currentY - 20, 12);
  drawText('Discount:', 290, currentY - 40, 12);
  drawText(data.discount.toFixed(2), 410, currentY - 40, 12);
  drawText('Payment Method:', 290, currentY - 60, 12);
  drawText(data.paymentMethod, 410, currentY - 60, 12);

  return pdfDoc.save();
};
