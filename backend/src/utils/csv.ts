import { parse } from 'csv-parse/sync';
import { stringify } from 'csv-stringify/sync';

export const generateCSV = (data: any[], fields: string[]): string => {
  const headers = fields.map(field => field.split('.').pop());
  const csvData = data.map(row => {
    return fields.map(field => {
      const parts = field.split('.');
      let value = row;
      for (const part of parts) {
        if (value && typeof value === 'object') {
          value = value[part];
        }
      }
      return value || '';
    });
  });

  return stringify(csvData, {
    header: true,
    columns: headers.filter((h): h is string => h !== undefined)
  });
};
