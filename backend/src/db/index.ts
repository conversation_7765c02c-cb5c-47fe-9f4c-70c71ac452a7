import { drizzle } from 'drizzle-orm/postgres-js';
import postgres = require('postgres');
import * as schema from './schema';

// Get database URL from environment variable
const dbUrl = process.env.DATABASE_URL;
if (!dbUrl) {
  throw new Error('DATABASE_URL environment variable is required');
}

// Create database connection
const pg = postgres(dbUrl);

// Create Drizzle instance
export const db = drizzle(pg, { schema });

// Export all schema definitions
export * from './schema';
