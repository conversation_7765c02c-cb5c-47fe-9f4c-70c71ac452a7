import { pgTable, serial, text, timestamp, integer, boolean, varchar, jsonb, decimal, date } from 'drizzle-orm/pg-core';

// User roles
export const UserRole = {
  ADMIN: 'admin',
  STORE_OWNER: 'store_owner',
  SALES_REP: 'sales_rep'
} as const;

export type UserRole = typeof UserRole[keyof typeof UserRole];

export const InventoryCostingMethod = {
  FIFO: 'fifo',
  LIFO: 'lifo',
  AVERAGE: 'average'
} as const;

export type InventoryCostingMethod = typeof InventoryCostingMethod[keyof typeof InventoryCostingMethod];

export const ExpenseCategory = {
  RENT: 'rent',
  UTILITIES: 'utilities',
  SALARIES: 'salaries',
  MAINTENANCE: 'maintenance',
  MARKETING: 'marketing',
  SUPPLIES: 'supplies',
  INSURANCE: 'insurance',
  OTHER: 'other'
} as const;

export type ExpenseCategory = typeof ExpenseCategory[keyof typeof ExpenseCategory];

// Core tables
export const stores = pgTable('stores', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  address: varchar('address', { length: 255 }).notNull(),
  city: varchar('city', { length: 100 }).notNull(),
  phoneNumber: varchar('phone_number', { length: 20 }).notNull(),
  email: varchar('email', { length: 255 }),
  openingHours: varchar('opening_hours', { length: 100 }),
  inventoryCostingMethod: varchar('inventory_costing_method', { length: 10 }).default('fifo'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  deletedAt: timestamp('deleted_at'),
});

export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: text('email').notNull().unique(),
  password: text('password').notNull(),
  firstName: text('first_name').notNull(),
  lastName: text('last_name').notNull(),
  role: varchar('role', { length: 20 }).notNull().default(UserRole.SALES_REP),
  storeId: integer('store_id').references(() => stores.id),
  isActive: boolean('is_active').default(true),
  lastLogin: timestamp('last_login'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
});

// Sessions table for authentication
export const sessions = pgTable('sessions', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id).notNull(),
  token: text('token').notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
});

// Function to check if user has permission
export const checkUserPermission = (role: UserRole, permission: keyof typeof USER_PERMISSIONS[UserRole]) => {
  return USER_PERMISSIONS[role][permission];
};

export const productCategories = pgTable('product_categories', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  parentCategoryId: integer('parent_category_id').references(() => productCategories.id),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const products = pgTable('products', {
  id: serial('id').primaryKey(),
  categoryId: integer('category_id').references(() => productCategories.id),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  brand: varchar('brand', { length: 100 }),
  model: varchar('model', { length: 100 }),
  purchasePrice: decimal('purchase_price', { precision: 10, scale: 2 }).notNull(),
  retailPrice: decimal('retail_price', { precision: 10, scale: 2 }).notNull(),
  warrantyMonths: integer('warranty_months'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  deletedAt: timestamp('deleted_at'),
});

export const productAttributes = pgTable('product_attributes', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 50 }).notNull(), // e.g., 'color', 'storage', 'RAM'
  description: text('description'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const variantAttributes = pgTable('variant_attributes', {
  id: serial('id').primaryKey(),
  variantId: integer('variant_id').references(() => productVariants.id),
  attributeId: integer('attribute_id').references(() => productAttributes.id),
  value: varchar('value', { length: 100 }).notNull(),
  sortOrder: integer('sort_order').default(0),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const saleWarranties = pgTable('sale_warranties', {
  id: serial('id').primaryKey(),
  saleItemId: integer('sale_item_id').references(() => salesItems.id),
  warrantyId: integer('warranty_id').references(() => productWarranties.id),
  startDate: timestamp('start_date').defaultNow(),
  endDate: timestamp('end_date'),
  status: varchar('status', { length: 20 }).default('active'), // active, expired, claimed, cancelled
  claimCount: integer('claim_count').default(0),
  maxClaims: integer('max_claims'),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const warrantyClaims = pgTable('warranty_claims', {
  id: serial('id').primaryKey(),
  saleWarrantyId: integer('sale_warranty_id').references(() => saleWarranties.id),
  claimDate: timestamp('claim_date').defaultNow(),
  issueDescription: text('issue_description').notNull(),
  serviceType: varchar('service_type', { length: 50 }).notNull(), // repair, replacement, refund
  status: varchar('status', { length: 20 }).default('pending'), // pending, approved, rejected, completed
  cost: decimal('cost', { precision: 10, scale: 2 }),
  serviceProvider: text('service_provider'),
  notes: text('notes'),
  createdBy: integer('created_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const warrantyNotifications = pgTable('warranty_notifications', {
  id: serial('id').primaryKey(),
  saleWarrantyId: integer('sale_warranty_id').references(() => saleWarranties.id),
  type: varchar('type', { length: 20 }).notNull(), // expiry_reminder, claim_reminder
  sentDate: timestamp('sent_date').defaultNow(),
  status: varchar('status', { length: 20 }).default('sent'), // sent, failed
  channel: varchar('channel', { length: 20 }).notNull(), // email, sms
  response: text('response'),
  createdAt: timestamp('created_at').defaultNow(),
});

export const productVariants = pgTable('product_variants', {
  id: serial('id').primaryKey(),
  productId: integer('product_id').references(() => products.id),
  sku: varchar('sku', { length: 50 }).notNull(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  basePrice: decimal('base_price', { precision: 10, scale: 2 }).notNull(),
  costPrice: decimal('cost_price', { precision: 10, scale: 2 }).notNull(),
  weight: decimal('weight', { precision: 10, scale: 2 }),
  length: decimal('length', { precision: 10, scale: 2 }),
  width: decimal('width', { precision: 10, scale: 2 }),
  height: decimal('height', { precision: 10, scale: 2 }),
  isDefault: boolean('is_default').default(false),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const attributeValues = pgTable('attribute_values', {
  id: serial('id').primaryKey(),
  attributeId: integer('attribute_id').references(() => productAttributes.id),
  value: varchar('value', { length: 100 }).notNull(), // e.g., 'red', '128GB', '8GB'
  sortOrder: integer('sort_order').default(0),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const inventory = pgTable('inventory', {
  id: serial('id').primaryKey(),
  storeId: integer('store_id').references(() => stores.id),
  variantId: integer('variant_id').references(() => productVariants.id),
  quantity: integer('quantity').notNull(),
  threshold: integer('threshold').default(5),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const salesItems = pgTable('sales_items', {
  id: serial('id').primaryKey(),
  saleId: integer('sale_id').references(() => sales.id),
  variantId: integer('variant_id').references(() => productVariants.id),
  quantity: integer('quantity').notNull(),
  unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
});



export const inventoryTransactions = pgTable('inventory_transactions', {
  id: serial('id').primaryKey(),
  storeId: integer('store_id').references(() => stores.id),
  variantId: integer('variant_id').references(() => productVariants.id),
  transactionType: varchar('transaction_type', { length: 20 }).notNull(), // purchase, sale, adjustment
  quantity: integer('quantity').notNull(),
  unitCost: decimal('unit_cost', { precision: 10, scale: 2 }).notNull(),
  totalCost: decimal('total_cost', { precision: 10, scale: 2 }).notNull(),
  referenceNumber: varchar('reference_number', { length: 100 }),
  notes: text('notes'),
  createdBy: integer('created_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});



export const transfers = pgTable('transfers', {
  id: serial('id').primaryKey(),
  fromStoreId: integer('from_store_id').references(() => stores.id),
  toStoreId: integer('to_store_id').references(() => stores.id),
  productId: integer('product_id').references(() => products.id),
  quantity: integer('quantity'),
  status: varchar('status', { length: 20 }).default('pending'),
  requestedBy: integer('requested_by').references(() => users.id),
  approvedBy: integer('approved_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const customerPurchases = pgTable('customer_purchases', {
  id: serial('id').primaryKey(),
  customerId: integer('customer_id').references(() => customers.id),
  saleId: integer('sale_id').references(() => sales.id),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  pointsEarned: integer('points_earned').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
});

export const returns = pgTable('returns', {
  id: serial('id').primaryKey(),
  saleId: integer('sale_id').references(() => sales.id),
  returnDate: timestamp('return_date').defaultNow(),
  reason: text('reason'),
  status: varchar('status', { length: 20 }).default('pending'), // pending, approved, rejected
  refundAmount: decimal('refund_amount', { precision: 10, scale: 2 }),
  refundMethod: varchar('refund_method', { length: 50 }), // cash, credit, store_credit
  processedBy: integer('processed_by').references(() => users.id),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const returnItems = pgTable('return_items', {
  id: serial('id').primaryKey(),
  returnId: integer('return_id').references(() => returns.id),
  saleItemId: integer('sale_item_id').references(() => salesItems.id),
  quantity: integer('quantity').notNull(),
  unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
  refundAmount: decimal('refund_amount', { precision: 10, scale: 2 }).notNull(),
  condition: varchar('condition', { length: 50 }).notNull(), // new, used, damaged
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
});

export const creditSales = pgTable('credit_sales', {
  id: serial('id').primaryKey(),
  customerId: integer('customer_id').references(() => customers.id),
  storeId: integer('store_id').references(() => stores.id).notNull(),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }).notNull(),
  dueDate: timestamp('due_date').notNull(),
  status: varchar('status', { length: 20 }).default('pending'),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const creditSaleItems = pgTable('credit_sale_items', {
  id: serial('id').primaryKey(),
  creditSaleId: integer('credit_sale_id').references(() => creditSales.id),
  productId: integer('product_id').references(() => products.id),
  quantity: integer('quantity').notNull(),
  unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
  subtotal: decimal('subtotal', { precision: 10, scale: 2 }).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
});

export const notifications = pgTable('notifications', {
  id: serial('id').primaryKey(),
  creditSaleId: integer('credit_sale_id').references(() => creditSales.id),
  customerId: integer('customer_id').references(() => customers.id),
  type: varchar('type', { length: 20 }).notNull(),
  message: text('message').notNull(),
  status: varchar('status', { length: 20 }).default('pending'),
  sentAt: timestamp('sent_at'),
  sentBy: integer('sent_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const suppliers = pgTable('suppliers', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  phone: varchar('phone', { length: 20 }),
  email: varchar('email', { length: 100 }),
  address: text('address'),
  notes: text('notes'),
  lastPurchaseDate: timestamp('last_purchase_date'),
  totalSpent: decimal('total_spent', { precision: 10, scale: 2 }).default(0),
  totalPurchases: integer('total_purchases').default(0),
  email: varchar('email', { length: 100 }),
  address: text('address'),
  contactPerson: varchar('contact_person', { length: 100 }),
  notes: text('notes'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const purchaseOrders = pgTable('purchase_orders', {
  id: serial('id').primaryKey(),
  supplierId: integer('supplier_id').references(() => suppliers.id),
  storeId: integer('store_id').references(() => stores.id),
  orderNumber: varchar('order_number', { length: 50 }).notNull(),
  orderDate: timestamp('order_date').defaultNow(),
  deliveryDate: timestamp('delivery_date'),
  status: varchar('status', { length: 20 }).default('pending'),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }).default(0),
  notes: text('notes'),
  createdBy: integer('created_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const purchaseOrderItems = pgTable('purchase_order_items', {
  id: serial('id').primaryKey(),
  purchaseOrderId: integer('purchase_order_id').references(() => purchaseOrders.id),
  productId: integer('product_id').references(() => products.id),
  quantity: integer('quantity').notNull(),
  unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
  subtotal: decimal('subtotal', { precision: 10, scale: 2 }).notNull(),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
});



export const purchasePayments = pgTable('purchase_payments', {
  id: serial('id').primaryKey(),
  purchaseOrderId: integer('purchase_order_id').references(() => purchaseOrders.id),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  paymentMethod: varchar('payment_method', { length: 50 }).notNull(),
  paymentReference: varchar('payment_reference', { length: 100 }),
  notes: text('notes'),
  paidBy: integer('paid_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
});

export const refunds = pgTable('refunds', {
  id: serial('id').primaryKey(),
  returnId: integer('return_id').references(() => returns.id),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  method: varchar('method', { length: 50 }).notNull(), // cash, credit, store_credit
  status: varchar('status', { length: 20 }).default('pending'), // pending, completed, failed
  transactionId: varchar('transaction_id', { length: 100 }),
  processedBy: integer('processed_by').references(() => users.id),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const expenseCategories = pgTable('expense_categories', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 50 }).notNull(),
  description: text('description'),
  type: varchar('type', { length: 50 }).notNull(), // fixed, variable, recurring
  isRecurring: boolean('is_recurring').default(false),
  recurringPeriod: varchar('recurring_period', { length: 20 }), // monthly, yearly, etc.
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const expenses = pgTable('expenses', {
  id: serial('id').primaryKey(),
  storeId: integer('store_id').references(() => stores.id),
  categoryId: integer('category_id').references(() => expenseCategories.id),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  date: date('date').notNull(),
  description: text('description'),
  paymentMethod: varchar('payment_method', { length: 50 }).notNull(), // cash, bank_transfer, etc.
  referenceNumber: varchar('reference_number', { length: 100 }),
  status: varchar('status', { length: 20 }).default('paid'), // paid, pending, overdue
  createdBy: integer('created_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const creditPayments = pgTable('credit_payments', {
  id: serial('id').primaryKey(),
  creditSaleId: integer('credit_sale_id').references(() => creditSales.id),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  paymentMethod: varchar('payment_method', { length: 50 }).notNull(),
  paymentReference: varchar('payment_reference', { length: 100 }),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
});

export const cashDrawers = pgTable('cash_drawers', {
  id: serial('id').primaryKey(),
  storeId: integer('store_id').references(() => stores.id),
  status: varchar('status', { length: 20 }).default('closed'), // open, closed, reconciling
  openingBalance: decimal('opening_balance', { precision: 10, scale: 2 }).notNull(),
  closingBalance: decimal('closing_balance', { precision: 10, scale: 2 }),
  actualBalance: decimal('actual_balance', { precision: 10, scale: 2 }),
  variance: decimal('variance', { precision: 10, scale: 2 }),
  notes: text('notes'),
  openedBy: integer('opened_by').references(() => users.id),
  closedBy: integer('closed_by').references(() => users.id),
  openedAt: timestamp('opened_at'),
  closedAt: timestamp('closed_at'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const cashDrawerTransactions = pgTable('cash_drawer_transactions', {
  id: serial('id').primaryKey(),
  drawerId: integer('drawer_id').references(() => cashDrawers.id),
  transactionType: varchar('transaction_type', { length: 20 }).notNull(), // sale, return, deposit, withdrawal, adjustment
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  referenceNumber: varchar('reference_number', { length: 100 }),
  notes: text('notes'),
  createdBy: integer('created_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
});

export const cashDrawerReconciliations = pgTable('cash_drawer_reconciliations', {
  id: serial('id').primaryKey(),
  drawerId: integer('drawer_id').references(() => cashDrawers.id),
  expectedBalance: decimal('expected_balance', { precision: 10, scale: 2 }).notNull(),
  actualBalance: decimal('actual_balance', { precision: 10, scale: 2 }).notNull(),
  variance: decimal('variance', { precision: 10, scale: 2 }).notNull(),
  notes: text('notes'),
  reconciledBy: integer('reconciled_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
});

export const sales = pgTable('sales', {
  id: serial('id').primaryKey(),
  storeId: integer('store_id').references(() => stores.id),
  customerId: integer('customer_id').references(() => customers.id),
  saleDate: timestamp('sale_date').defaultNow(),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }).notNull(),
  discount: decimal('discount', { precision: 10, scale: 2 }).default(0),
  tax: decimal('tax', { precision: 10, scale: 2 }).default(0),
  paymentMethod: varchar('payment_method', { length: 50 }).notNull(),
  status: varchar('status', { length: 20 }).default('pending'),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  deletedAt: timestamp('deleted_at'),
});

export const salesItems = pgTable('sales_items', {
  id: serial('id').primaryKey(),
  saleId: integer('sale_id').references(() => sales.id),
  productId: integer('product_id').references(() => products.id),
  variantId: integer('variant_id').references(() => productVariants.id),
  quantity: integer('quantity').notNull(),
  unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
  discount: decimal('discount', { precision: 10, scale: 2 }).default(0),
  tax: decimal('tax', { precision: 10, scale: 2 }).default(0),
  warrantyId: integer('warranty_id').references(() => productWarranties.id),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
});

export const customerTypes = pgTable('customer_types', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 50 }).notNull(),
  description: text('description'),
  discountRate: decimal('discount_rate', { precision: 5, scale: 2 }).notNull(),
  minSpend: decimal('min_spend', { precision: 10, scale: 2 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const loyaltyTiers = pgTable('loyalty_tiers', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 50 }).notNull(),
  pointsRequired: integer('points_required').notNull(),
  discountRate: decimal('discount_rate', { precision: 5, scale: 2 }).notNull(),
  benefits: text('benefits'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const customers = pgTable('customers', {
  id: serial('id').primaryKey(),
  firstName: text('first_name').notNull(),
  lastName: text('last_name'),
  email: text('email').unique(),
  phoneNumber: text('phone_number').notNull(),
  address: jsonb('address'),
  totalPurchases: integer('total_purchases').default(0),
  totalSpent: integer('total_spent').default(0),
  lastPurchase: timestamp('last_purchase'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
});



export const purchases = pgTable('purchases', {
  id: serial('id').primaryKey(),
  supplierId: integer('supplier_id').references(() => suppliers.id).notNull(),
  storeId: integer('store_id').references(() => stores.id).notNull(),
  purchaseOrderNumber: text('purchase_order_number').notNull().unique(),
  items: jsonb('items').notNull(),
  totalAmount: integer('total_amount').notNull(),
  paymentTerms: text('payment_terms').notNull(),
  dueDate: timestamp('due_date').notNull(),
  status: text('status').notNull().default('pending'),
  paymentStatus: text('payment_status').notNull().default('unpaid'),
  paymentHistory: jsonb('payment_history'),
  createdAt: timestamp('created_at').defaultNow()
});
