import { pgTable, serial, text, timestamp, integer, boolean, varchar, decimal, json } from 'drizzle-orm/pg-core';

// User roles
export const UserRole = {
  ADMIN: 'admin',
  STORE_OWNER: 'store_owner',
  SALES_REP: 'sales_rep'
} as const;

export type UserRole = typeof UserRole[keyof typeof UserRole];

// Inventory costing methods
export const InventoryCostingMethod = {
  FIFO: 'fifo',
  LIFO: 'lifo',
  AVERAGE: 'average'
} as const;

export type InventoryCostingMethod = typeof InventoryCostingMethod[keyof typeof InventoryCostingMethod];

// Cash drawer status
export const CashDrawerStatus = {
  OPEN: 'open',
  CLOSED: 'closed',
  RECONCILED: 'reconciled'
} as const;

export type CashDrawerStatus = typeof CashDrawerStatus[keyof typeof CashDrawerStatus];

// Core tables
export const stores = pgTable('stores', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  address: varchar('address', { length: 255 }).notNull(),
  city: varchar('city', { length: 100 }).notNull(),
  phoneNumber: varchar('phone_number', { length: 20 }).notNull(),
  email: varchar('email', { length: 255 }),
  openingHours: varchar('opening_hours', { length: 100 }),
  inventoryCostingMethod: varchar('inventory_costing_method', { length: 10 }).default('fifo'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  deletedAt: timestamp('deleted_at'),
});

export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: text('email').notNull().unique(),
  password: text('password').notNull(),
  firstName: text('first_name').notNull(),
  lastName: text('last_name').notNull(),
  role: varchar('role', { length: 20 }).notNull().default(UserRole.SALES_REP),
  storeId: integer('store_id').references(() => stores.id),
  isActive: boolean('is_active').default(true),
  lastLogin: timestamp('last_login'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
});

// Sessions table for authentication
export const sessions = pgTable('sessions', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id).notNull(),
  token: text('token').notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
});

// Product categories
export const productCategories = pgTable('product_categories', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  parentCategoryId: integer('parent_category_id'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Products
export const products = pgTable('products', {
  id: serial('id').primaryKey(),
  categoryId: integer('category_id').references(() => productCategories.id),
  name: varchar('name', { length: 255 }).notNull(),
  sku: varchar('sku', { length: 100 }).unique(),
  description: text('description'),
  brand: varchar('brand', { length: 100 }),
  model: varchar('model', { length: 100 }),
  purchasePrice: decimal('purchase_price', { precision: 10, scale: 2 }).notNull(),
  retailPrice: decimal('retail_price', { precision: 10, scale: 2 }).notNull(),
  warrantyMonths: integer('warranty_months'),
  warranty: text('warranty'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  deletedAt: timestamp('deleted_at'),
});

// Basic inventory table
export const inventory = pgTable('inventory', {
  id: serial('id').primaryKey(),
  storeId: integer('store_id').references(() => stores.id).notNull(),
  productId: integer('product_id').references(() => products.id).notNull(),
  variantId: integer('variant_id').references(() => productVariants.id),
  quantity: integer('quantity').notNull().default(0),
  threshold: integer('threshold').default(5),
  unitCost: decimal('unit_cost', { precision: 10, scale: 2 }),
  totalCost: decimal('total_cost', { precision: 10, scale: 2 }),
  lastUpdated: timestamp('last_updated').defaultNow(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Sales table
export const sales = pgTable('sales', {
  id: serial('id').primaryKey(),
  storeId: integer('store_id').references(() => stores.id).notNull(),
  userId: integer('user_id').references(() => users.id).notNull(),
  customerId: integer('customer_id').references(() => customers.id),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }).notNull(),
  paymentMethod: varchar('payment_method', { length: 50 }).notNull(),
  status: varchar('status', { length: 20 }).default('completed'),
  saleDate: timestamp('sale_date').defaultNow(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Sales items table
export const salesItems = pgTable('sales_items', {
  id: serial('id').primaryKey(),
  saleId: integer('sale_id').references(() => sales.id).notNull(),
  productId: integer('product_id').references(() => products.id).notNull(),
  variantId: integer('variant_id').references(() => productVariants.id),
  quantity: integer('quantity').notNull(),
  unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
  subtotal: decimal('subtotal', { precision: 10, scale: 2 }).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Customers table
export const customers = pgTable('customers', {
  id: serial('id').primaryKey(),
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  email: varchar('email', { length: 255 }),
  phone: varchar('phone', { length: 20 }),
  address: text('address'),
  city: varchar('city', { length: 100 }),
  customerTypeId: integer('customer_type_id'),
  loyaltyTierId: integer('loyalty_tier_id'),
  preferredStoreId: integer('preferred_store_id').references(() => stores.id),
  totalSpent: decimal('total_spent', { precision: 10, scale: 2 }).default('0'),
  totalPurchases: integer('total_purchases').default(0),
  lastPurchaseDate: timestamp('last_purchase_date'),
  points: integer('points').default(0),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const customerTypes = pgTable('customer_types', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  discountPercentage: decimal('discount_percentage', { precision: 5, scale: 2 }).default('0'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const loyaltyTiers = pgTable('loyalty_tiers', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  pointsRequired: integer('points_required').notNull(),
  benefits: text('benefits'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const customerPurchases = pgTable('customer_purchases', {
  id: serial('id').primaryKey(),
  customerId: integer('customer_id').references(() => customers.id).notNull(),
  saleId: integer('sale_id').references(() => sales.id).notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  pointsEarned: integer('points_earned').default(0),
  createdAt: timestamp('created_at').defaultNow(),
});

// Product attributes table
export const productAttributes = pgTable('product_attributes', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(), // 'text', 'number', 'select', etc.
  isRequired: boolean('is_required').default(false),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Attribute values table
export const attributeValues = pgTable('attribute_values', {
  id: serial('id').primaryKey(),
  attributeId: integer('attribute_id').references(() => productAttributes.id).notNull(),
  value: varchar('value', { length: 255 }).notNull(),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Product variants table
export const productVariants = pgTable('product_variants', {
  id: serial('id').primaryKey(),
  productId: integer('product_id').references(() => products.id).notNull(),
  sku: varchar('sku', { length: 100 }).unique(),
  name: varchar('name', { length: 255 }),
  description: text('description'),
  purchasePrice: decimal('purchase_price', { precision: 10, scale: 2 }),
  retailPrice: decimal('retail_price', { precision: 10, scale: 2 }),
  basePrice: decimal('base_price', { precision: 10, scale: 2 }),
  costPrice: decimal('cost_price', { precision: 10, scale: 2 }),
  weight: decimal('weight', { precision: 8, scale: 3 }),
  length: decimal('length', { precision: 8, scale: 2 }),
  width: decimal('width', { precision: 8, scale: 2 }),
  height: decimal('height', { precision: 8, scale: 2 }),
  threshold: integer('threshold').default(5),
  isDefault: boolean('is_default').default(false),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Variant attributes table (junction table)
export const variantAttributes = pgTable('variant_attributes', {
  id: serial('id').primaryKey(),
  variantId: integer('variant_id').references(() => productVariants.id).notNull(),
  attributeId: integer('attribute_id').references(() => productAttributes.id).notNull(),
  valueId: integer('value_id').references(() => attributeValues.id),
  customValue: varchar('custom_value', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Returns table
export const returns = pgTable('returns', {
  id: serial('id').primaryKey(),
  saleId: integer('sale_id').references(() => sales.id),
  customerId: integer('customer_id').references(() => customers.id),
  storeId: integer('store_id').references(() => stores.id).notNull(),
  userId: integer('user_id').references(() => users.id).notNull(),
  reason: text('reason'),
  status: varchar('status', { length: 20 }).default('pending'),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }).notNull(),
  refundAmount: decimal('refund_amount', { precision: 10, scale: 2 }),
  refundMethod: varchar('refund_method', { length: 50 }),
  processedBy: integer('processed_by').references(() => users.id),
  returnDate: timestamp('return_date').defaultNow(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Return items table
export const returnItems = pgTable('return_items', {
  id: serial('id').primaryKey(),
  returnId: integer('return_id').references(() => returns.id).notNull(),
  productId: integer('product_id').references(() => products.id).notNull(),
  variantId: integer('variant_id').references(() => productVariants.id),
  quantity: integer('quantity').notNull(),
  unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
  subtotal: decimal('subtotal', { precision: 10, scale: 2 }).notNull(),
  reason: text('reason'),
  condition: varchar('condition', { length: 50 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Credit sales table
export const creditSales = pgTable('credit_sales', {
  id: serial('id').primaryKey(),
  saleId: integer('sale_id').references(() => sales.id),
  customerId: integer('customer_id').references(() => customers.id).notNull(),
  storeId: integer('store_id').references(() => stores.id).notNull(),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }).notNull(),
  paidAmount: decimal('paid_amount', { precision: 10, scale: 2 }).default('0'),
  remainingAmount: decimal('remaining_amount', { precision: 10, scale: 2 }).notNull(),
  dueDate: timestamp('due_date'),
  status: varchar('status', { length: 20 }).default('pending'),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Credit sale items table
export const creditSaleItems = pgTable('credit_sale_items', {
  id: serial('id').primaryKey(),
  creditSaleId: integer('credit_sale_id').references(() => creditSales.id).notNull(),
  productId: integer('product_id').references(() => products.id).notNull(),
  variantId: integer('variant_id').references(() => productVariants.id),
  quantity: integer('quantity').notNull(),
  unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
  subtotal: decimal('subtotal', { precision: 10, scale: 2 }).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Credit payments table
export const creditPayments = pgTable('credit_payments', {
  id: serial('id').primaryKey(),
  creditSaleId: integer('credit_sale_id').references(() => creditSales.id).notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  paymentMethod: varchar('payment_method', { length: 50 }).notNull(),
  paymentDate: timestamp('payment_date').defaultNow(),
  paymentReference: varchar('payment_reference', { length: 100 }),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Warranty types table
export const warrantyTypes = pgTable('warranty_types', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  durationMonths: integer('duration_months').notNull(),
  termsAndConditions: text('terms_and_conditions'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Product warranties table
export const productWarranties = pgTable('product_warranties', {
  id: serial('id').primaryKey(),
  productId: integer('product_id').references(() => products.id).notNull(),
  warrantyTypeId: integer('warranty_type_id').references(() => warrantyTypes.id).notNull(),
  durationMonths: integer('duration_months'),
  termsAndConditions: text('terms_and_conditions'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Sale warranties table
export const saleWarranties = pgTable('sale_warranties', {
  id: serial('id').primaryKey(),
  saleId: integer('sale_id').references(() => sales.id).notNull(),
  productId: integer('product_id').references(() => products.id).notNull(),
  variantId: integer('variant_id').references(() => productVariants.id),
  warrantyTypeId: integer('warranty_type_id').references(() => warrantyTypes.id).notNull(),
  saleItemId: integer('sale_item_id').references(() => salesItems.id),
  warrantyId: integer('warranty_id').references(() => warrantyTypes.id),
  startDate: timestamp('start_date').defaultNow(),
  endDate: timestamp('end_date').notNull(),
  status: varchar('status', { length: 20 }).default('active'),
  claimCount: integer('claim_count').default(0),
  maxClaims: integer('max_claims').default(1),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Warranty claims table
export const warrantyClaims = pgTable('warranty_claims', {
  id: serial('id').primaryKey(),
  saleWarrantyId: integer('sale_warranty_id').references(() => saleWarranties.id).notNull(),
  customerId: integer('customer_id').references(() => customers.id).notNull(),
  description: text('description').notNull(),
  status: varchar('status', { length: 20 }).default('pending'),
  claimDate: timestamp('claim_date').defaultNow(),
  resolutionDate: timestamp('resolution_date'),
  resolutionNotes: text('resolution_notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Warranty notifications table
export const warrantyNotifications = pgTable('warranty_notifications', {
  id: serial('id').primaryKey(),
  saleWarrantyId: integer('sale_warranty_id').references(() => saleWarranties.id).notNull(),
  customerId: integer('customer_id').references(() => customers.id).notNull(),
  type: varchar('type', { length: 50 }).notNull(), // 'expiring', 'expired', 'reminder'
  message: text('message').notNull(),
  sentDate: timestamp('sent_date'),
  status: varchar('status', { length: 20 }).default('pending'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Notifications table
export const notifications = pgTable('notifications', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id),
  customerId: integer('customer_id').references(() => customers.id),
  storeId: integer('store_id').references(() => stores.id),
  creditSaleId: integer('credit_sale_id').references(() => creditSales.id),
  saleWarrantyId: integer('sale_warranty_id').references(() => saleWarranties.id),
  type: varchar('type', { length: 50 }).notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  message: text('message').notNull(),
  channel: varchar('channel', { length: 20 }).default('email'),
  isRead: boolean('is_read').default(false),
  status: varchar('status', { length: 20 }).default('pending'),
  priority: varchar('priority', { length: 20 }).default('normal'),
  sentAt: timestamp('sent_at'),
  sentDate: timestamp('sent_date'),
  data: json('data'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Refunds table
export const refunds = pgTable('refunds', {
  id: serial('id').primaryKey(),
  returnId: integer('return_id').references(() => returns.id),
  saleId: integer('sale_id').references(() => sales.id),
  customerId: integer('customer_id').references(() => customers.id).notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  method: varchar('method', { length: 50 }).notNull(),
  status: varchar('status', { length: 20 }).default('pending'),
  processedDate: timestamp('processed_date'),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Additional tables for advanced features
export const expenses = pgTable('expenses', {
  id: serial('id').primaryKey(),
  storeId: integer('store_id').references(() => stores.id).notNull(),
  categoryId: integer('category_id'),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  description: text('description'),
  paymentMethod: varchar('payment_method', { length: 50 }),
  referenceNumber: varchar('reference_number', { length: 100 }),
  status: varchar('status', { length: 20 }).default('pending'),
  date: timestamp('date').defaultNow(),
  createdBy: integer('created_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const expenseCategories = pgTable('expense_categories', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  type: varchar('type', { length: 50 }).default('general'),
  isRecurring: boolean('is_recurring').default(false),
  recurringPeriod: varchar('recurring_period', { length: 20 }),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const inventoryTransactions = pgTable('inventory_transactions', {
  id: serial('id').primaryKey(),
  storeId: integer('store_id').references(() => stores.id).notNull(),
  productId: integer('product_id').references(() => products.id).notNull(),
  variantId: integer('variant_id').references(() => productVariants.id),
  transactionType: varchar('transaction_type', { length: 50 }).notNull(),
  quantity: integer('quantity').notNull(),
  unitCost: decimal('unit_cost', { precision: 10, scale: 2 }),
  totalCost: decimal('total_cost', { precision: 10, scale: 2 }),
  referenceNumber: varchar('reference_number', { length: 100 }),
  notes: text('notes'),
  createdBy: integer('created_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
});

export const transfers = pgTable('transfers', {
  id: serial('id').primaryKey(),
  fromStoreId: integer('from_store_id').references(() => stores.id).notNull(),
  toStoreId: integer('to_store_id').references(() => stores.id).notNull(),
  originStoreId: integer('origin_store_id').references(() => stores.id).notNull(),
  destinationStoreId: integer('destination_store_id').references(() => stores.id).notNull(),
  status: varchar('status', { length: 20 }).default('pending'),
  notes: text('notes'),
  createdBy: integer('created_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Cash drawer management tables
export const cashDrawers = pgTable('cash_drawers', {
  id: serial('id').primaryKey(),
  storeId: integer('store_id').references(() => stores.id).notNull(),
  name: varchar('name', { length: 100 }).notNull(),
  status: varchar('status', { length: 20 }).default(CashDrawerStatus.CLOSED),
  openingBalance: decimal('opening_balance', { precision: 10, scale: 2 }).default('0.00'),
  actualBalance: decimal('actual_balance', { precision: 10, scale: 2 }).default('0.00'),
  closingBalance: decimal('closing_balance', { precision: 10, scale: 2 }),
  variance: decimal('variance', { precision: 10, scale: 2 }).default('0.00'),
  openedBy: integer('opened_by').references(() => users.id),
  closedBy: integer('closed_by').references(() => users.id),
  openedAt: timestamp('opened_at'),
  closedAt: timestamp('closed_at'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const cashDrawerTransactions = pgTable('cash_drawer_transactions', {
  id: serial('id').primaryKey(),
  cashDrawerId: integer('cash_drawer_id').references(() => cashDrawers.id).notNull(),
  drawerId: integer('drawer_id').references(() => cashDrawers.id).notNull(),
  transactionType: varchar('transaction_type', { length: 50 }).notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  referenceNumber: varchar('reference_number', { length: 100 }),
  notes: text('notes'),
  createdBy: integer('created_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow(),
});

export const cashDrawerReconciliations = pgTable('cash_drawer_reconciliations', {
  id: serial('id').primaryKey(),
  cashDrawerId: integer('cash_drawer_id').references(() => cashDrawers.id).notNull(),
  drawerId: integer('drawer_id').references(() => cashDrawers.id).notNull(),
  expectedAmount: decimal('expected_amount', { precision: 10, scale: 2 }).notNull(),
  actualAmount: decimal('actual_amount', { precision: 10, scale: 2 }).notNull(),
  variance: decimal('variance', { precision: 10, scale: 2 }).notNull(),
  notes: text('notes'),
  reconciledBy: integer('reconciled_by').references(() => users.id),
  reconciledAt: timestamp('reconciled_at').defaultNow(),
  createdAt: timestamp('created_at').defaultNow(),
});
