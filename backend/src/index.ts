import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import session from 'express-session';
import passport from 'passport';
import { drizzle } from 'drizzle-orm/postgres-js';
import { Pool } from 'pg';
import { eq } from 'drizzle-orm';
import { users } from './db/schema';

dotenv.config();

// Initialize database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

const db = drizzle(pool);

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Session configuration
app.use(
  session({
    secret: process.env.SESSION_SECRET || 'your-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: 1000 * 60 * 60 * 24 // 24 hours
    }
  })
);

// Passport configuration
app.use(passport.initialize());
app.use(passport.session());

// Passport local strategy
import { Strategy as LocalStrategy } from 'passport-local';

passport.use(new LocalStrategy(
  { usernameField: 'email' },
  async (email, password, done) => {
    try {
      const user = await db.select().from(users).where(eq(users.email, email)).get();
      if (!user) {
        return done(null, false, { message: 'Invalid email or password' });
      }
      // Add password comparison logic here
      return done(null, user);
    } catch (error) {
      return done(error);
    }
  }
));

passport.serializeUser((user, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id, done) => {
  try {
    const user = await db.select().from(users).where(eq(users.id, id)).get();
    done(null, user);
  } catch (error) {
    done(error);
  }
});

// Routes
import authRoutes from './routes/api/auth';
import storeRoutes from './routes/api/stores';
import productRoutes from './routes/api/products';
import inventoryRoutes from './routes/api/inventory';
// Note: sales, customers, suppliers, purchases routes need to be created
// import saleRoutes from './routes/api/sales';
// import customerRoutes from './routes/api/customers';
// import supplierRoutes from './routes/api/suppliers';
// import purchaseRoutes from './routes/api/purchases';

app.use('/api/auth', authRoutes);
app.use('/api/stores', storeRoutes);
app.use('/api/products', productRoutes);
app.use('/api/inventory', inventoryRoutes);
// app.use('/api/sales', saleRoutes);
// app.use('/api/customers', customerRoutes);
// app.use('/api/suppliers', supplierRoutes);
// app.use('/api/purchases', purchaseRoutes);

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Start server
const PORT = process.env.PORT || 8080;
app.listen(PORT, () => {
  console.log(`Server running in ${process.env.NODE_ENV || 'development'} mode on port ${PORT}`);
});
