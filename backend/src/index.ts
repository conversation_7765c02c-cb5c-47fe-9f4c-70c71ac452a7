import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import session from 'express-session';
import passport from 'passport';
import { eq } from 'drizzle-orm';
import { db } from './db';
import { users, UserRole } from './db/schema';

dotenv.config();

// Initialize database tables
async function initializeDatabase() {
  try {
    // Test database connection
    await db.select().from(users).limit(1);
    console.log('✅ Database connected successfully');
  } catch (error) {
    console.log('⚠️ Database connection failed, but continuing...', error);
  }
}

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Session configuration
app.use(
  session({
    secret: process.env.SESSION_SECRET || 'your-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: 1000 * 60 * 60 * 24 // 24 hours
    }
  })
);

// Passport configuration
app.use(passport.initialize());
app.use(passport.session());

// Passport local strategy
import { Strategy as LocalStrategy } from 'passport-local';

passport.use(new LocalStrategy(
  { usernameField: 'email' },
  async (email, password, done) => {
    try {
      const userResult = await db.select().from(users).where(eq(users.email, email)).limit(1);
      const user = userResult[0];
      if (!user) {
        return done(null, false, { message: 'Invalid email or password' });
      }
      // Add password comparison logic here
      return done(null, { ...user, role: user.role as UserRole });
    } catch (error) {
      return done(error);
    }
  }
));

passport.serializeUser((user: any, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id: unknown, done) => {
  try {
    const userResult = await db.select().from(users).where(eq(users.id, id as number)).limit(1);
    const user = userResult[0];
    done(null, user ? { ...user, role: user.role as UserRole } : null);
  } catch (error) {
    done(error);
  }
});

// Routes
import authRoutes from './routes/api/auth';
import storeRoutes from './routes/api/stores';
import productRoutes from './routes/api/products';
import inventoryRoutes from './routes/api/inventory';
// Note: sales, customers, suppliers, purchases routes need to be created
// import saleRoutes from './routes/api/sales';
// import customerRoutes from './routes/api/customers';
// import supplierRoutes from './routes/api/suppliers';
// import purchaseRoutes from './routes/api/purchases';

app.use('/api/auth', authRoutes);
app.use('/api/stores', storeRoutes);
app.use('/api/products', productRoutes);
app.use('/api/inventory', inventoryRoutes);
// app.use('/api/sales', saleRoutes);
// app.use('/api/customers', customerRoutes);
// app.use('/api/suppliers', supplierRoutes);
// app.use('/api/purchases', purchaseRoutes);

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Start server
const PORT = process.env.PORT || 8080;
app.listen(PORT, async () => {
  console.log(`Server running in ${process.env.NODE_ENV || 'development'} mode on port ${PORT}`);
  await initializeDatabase();
});
