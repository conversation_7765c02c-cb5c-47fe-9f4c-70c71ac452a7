import jwt from 'jsonwebtoken';
import { db } from '../db/index';
import { eq } from 'drizzle-orm';
import { UserRole, users } from '../db/schema';
import express from 'express';
import { Request, Response, NextFunction } from 'express';

export const generateToken = (userId: number, role: UserRole, storeId: number | null) => {
  return jwt.sign({ userId, role, storeId }, process.env.JWT_SECRET || 'your-secret-key', {
    expiresIn: '24h'
  });
};

export const verifyToken = (token: string): { userId: number; role: UserRole; storeId: number | null; } | null => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET) as {
      userId: number;
      role: UserRole;
      storeId: number | null;
    };
    return decoded;
  } catch (error) {
    return null;
  }
};

export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];

    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    const userResult = await db
      .select()
      .from(users)
      .where(eq(users.id, decoded.userId))
      .limit(1);

    const user = userResult[0];

    if (!user || !user.isActive) {
      return res.status(401).json({ error: 'User not found or inactive' });
    }

    // Update last login
    await db
      .update(users)
      .set({ lastLogin: new Date() })
      .where(eq(users.id, decoded.userId));

    req.user = {
      ...user,
      role: user.role as UserRole,
      storeId: decoded.storeId
    };
    next();
  } catch (error) {
    console.error('Auth error:', error);
    res.status(500).json({ error: 'Authentication error' });
  }
};

export const roleMiddleware = (requiredRole: UserRole) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = req.user;

      if (!user) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      if (user.role !== UserRole.ADMIN && user.role !== requiredRole) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      // For store-specific operations, check if user has access to the store
      if (requiredRole === UserRole.STORE_OWNER || requiredRole === UserRole.SALES_REP) {
        const storeId = req.params.storeId || req.body.storeId;
        if (storeId && user.storeId !== parseInt(storeId)) {
          return res.status(403).json({ error: 'Access denied to this store' });
        }
      }

      next();
    } catch (error) {
      console.error('Role check error:', error);
      res.status(500).json({ error: 'Role check failed' });
    }
  };
};
