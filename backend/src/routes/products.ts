import express, { Request, Response } from 'express';
import { db } from '../db';
import { products, stores, inventory } from '../db/schema';
import { eq, and } from 'drizzle-orm';
import { sql } from 'drizzle-orm';

interface Product {
  id: number;
  name: string;
  categoryId: number | null;
  sku: string | null;
  brand: string | null;
  purchasePrice: string;
  retailPrice: string;
  warranty: string | null;
  description: string | null;
  model: string | null;
  warrantyMonths: number | null;
  createdAt: Date | null;
  updatedAt: Date | null;
  deletedAt: Date | null;
  isActive: boolean | null;
}

interface Store {
  id: number;
  name: string;
  address: string;
  city: string;
  phoneNumber: string;
  email: string;
  openingHours: string;
  managerId: number;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

interface Inventory {
  id: number;
  productId: number;
  storeId: number;
  quantity: number;
  threshold: number;
  lastUpdated: Date;
  history: Array<{
    type: string;
    quantity: number;
    date: Date;
    note?: string;
    createdBy?: number;
  }>;
}

const router = express.Router();

// Get all products
router.get('/', async (req: Request, res: Response) => {
  try {
    const productsData = await db.select().from(products);
    res.json(productsData as Product[]);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Get product by ID
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const productId = Number(req.params.id);
    if (isNaN(productId)) {
      return res.status(400).json({ error: 'Invalid product ID' });
    }

    const productResult = await db
      .select()
      .from(products)
      .where(eq(products.id, productId))
      .limit(1);

    const product = productResult[0];
    
    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }
    
    // Get inventory levels for all stores
    const inventoryLevels = await db
      .select({
        storeId: inventory.storeId,
        storeName: stores.name,
        quantity: inventory.quantity
      })
      .from(inventory)
      .leftJoin(stores, eq(inventory.storeId, stores.id))
      .where(eq(inventory.productId, product.id));

    res.json({ ...product, inventory: inventoryLevels });
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ error: 'Failed to fetch product' });
  }
});

// Create new product
router.post('/', async (req: Request, res: Response) => {
  try {
    const { name, category, sku, brand, purchasePrice, retailPrice, warranty } = req.body;
    
    if (!name || !category || !sku || !brand || purchasePrice === undefined || retailPrice === undefined || !warranty) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const result = await db
      .insert(products)
      .values({
        name,
        categoryId: category,
        sku,
        brand,
        purchasePrice: purchasePrice.toString(),
        retailPrice: retailPrice.toString(),
        warranty
      })
      .returning();
    
    res.status(201).json(result[0] as Product);
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({ error: 'Failed to create product' });
  }
});

// Update product
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const productId = Number(req.params.id);
    if (isNaN(productId)) {
      return res.status(400).json({ error: 'Invalid product ID' });
    }

    const { name, category, sku, brand, purchasePrice, retailPrice, warranty } = req.body;
    
    if (!name || !category || !sku || !brand || purchasePrice === undefined || retailPrice === undefined || !warranty) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const result = await db
      .update(products)
      .set({
        name,
        categoryId: category,
        sku,
        brand,
        purchasePrice: purchasePrice.toString(),
        retailPrice: retailPrice.toString(),
        warranty,
        updatedAt: new Date()
      })
      .where(eq(products.id, productId))
      .returning();
    
    if (!result[0]) {
      return res.status(404).json({ error: 'Product not found' });
    }
    
    res.json(result[0] as Product);
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({ error: 'Failed to update product' });
  }
});

// Delete product
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const productId = Number(req.params.id);
    if (isNaN(productId)) {
      return res.status(400).json({ error: 'Invalid product ID' });
    }

    // First delete inventory records
    await db.delete(inventory).where(eq(inventory.productId, productId));
    
    // Then delete the product
    const result = await db.delete(products).where(eq(products.id, productId));
    
    if (result === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }
    
    res.json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ error: 'Failed to delete product' });
  }
});

export default router;
