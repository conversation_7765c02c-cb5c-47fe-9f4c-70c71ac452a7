import express, { Request, Response } from 'express';
import { db } from '../../db';
import { stores } from '../../db/schema';
import { eq, and } from 'drizzle-orm';
import { UserRole } from '../../db/schema';

const router = express.Router();

// Get all stores (only admins can see all stores)
router.get('/', async (req: Request, res: Response) => {
  try {
    const auth = req.headers.authorization;
    if (!auth) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // In a real app, you would parse the token and get user info
    // For now, we'll just check if the user is an admin
    const isAdmin = auth === 'admin';

    const storesList = await db
      .select()
      .from(stores)
      .where(eq(stores.isActive, true))
      .all();

    res.json(storesList);
  } catch (error) {
    console.error('Error fetching stores:', error);
    res.status(500).json({ error: 'Failed to fetch stores' });
  }
});

// Get store by ID
router.get('/:id', async (req: Request, res: Response) => {
  const { id } = req.params;
  
  try {
    const store = await db
      .select()
      .from(stores)
      .where(eq(stores.id, parseInt(id)))
      .get();

    if (!store) {
      return res.status(404).json({ error: 'Store not found' });
    }

    res.json(store);
  } catch (error) {
    console.error('Error fetching store:', error);
    res.status(500).json({ error: 'Failed to fetch store' });
  }
});

// Create new store
router.post('/', async (req: Request, res: Response) => {
  try {
    const { name, address, city, phoneNumber, email, openingHours } = req.body;

    const store = await db
      .insert(stores)
      .values({
        name,
        address,
        city,
        phoneNumber,
        email,
        openingHours: JSON.stringify(openingHours)
      })
      .returning()
      .get();

    res.status(201).json(store);
  } catch (error) {
    console.error('Error creating store:', error);
    res.status(500).json({ error: 'Failed to create store' });
  }
});

// Update store
router.put('/:id', async (req: Request, res: Response) => {
  const { id } = req.params;
  
  try {
    const { name, address, city, phoneNumber, email, openingHours } = req.body;

    const store = await db
      .update(stores)
      .set({
        name,
        address,
        city,
        phoneNumber,
        email,
        openingHours: JSON.stringify(openingHours),
        updatedAt: new Date()
      })
      .where(eq(stores.id, parseInt(id)))
      .returning()
      .get();

    if (!store) {
      return res.status(404).json({ error: 'Store not found' });
    }

    res.json(store);
  } catch (error) {
    console.error('Error updating store:', error);
    res.status(500).json({ error: 'Failed to update store' });
  }
});

// Delete store (soft delete)
router.delete('/:id', async (req: Request, res: Response) => {
  const { id } = req.params;
  
  try {
    const store = await db
      .update(stores)
      .set({
        isActive: false,
        updatedAt: new Date()
      })
      .where(eq(stores.id, parseInt(id)))
      .returning()
      .get();

    if (!store) {
      return res.status(404).json({ error: 'Store not found' });
    }

    res.json(store);
  } catch (error) {
    console.error('Error deleting store:', error);
    res.status(500).json({ error: 'Failed to delete store' });
  }
});

export default router;
