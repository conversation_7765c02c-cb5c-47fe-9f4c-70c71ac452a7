import express, { Request, Response } from 'express';
import { productService } from '../../services';
// import { productVariantService } from '../../services'; // TODO: Fix compilation errors
import { authMiddleware } from '../../middleware/auth';
import { UserRole } from '../../db/schema';

interface AuthenticatedRequest extends Request {
  user: {
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    storeId: number | null;
    isActive: boolean;
  };
}

const router = express.Router();

// Get all products
router.get('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { category, status, limit = 10, page = 1 } = req.query;

    const products = await productService.getProducts(
      user.storeId,
      category as string,
      status as string,
      parseInt(limit as string),
      parseInt(page as string)
    );

    res.json(products);
  } catch (error) {
    console.error('Error getting products:', error);
    res.status(500).json({ error: 'Failed to get products' });
  }
});

// Get product by ID
router.get('/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { id } = req.params;

    const product = await productService.getProduct(
      user.storeId,
      parseInt(id)
    );

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json(product);
  } catch (error) {
    console.error('Error getting product:', error);
    res.status(500).json({ error: 'Failed to get product' });
  }
});

// Create product
router.post('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const productId = await productService.createProduct(req.body, user.id);
    res.json({ id: productId });
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({ error: 'Failed to create product' });
  }
});

// Update product
router.put('/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { id } = req.params;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await productService.updateProduct(parseInt(id), req.body);
    res.json({ message: 'Product updated successfully' });
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({ error: 'Failed to update product' });
  }
});

// Delete product
router.delete('/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { id } = req.params;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await productService.deleteProduct(parseInt(id));
    res.json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ error: 'Failed to delete product' });
  }
});

// Get product variants
// TODO: Re-enable when productVariantService is fixed
/*
router.get('/:productId/variants', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const variants = await productVariantService.getVariants(parseInt(productId));
    res.json(variants);
  } catch (error) {
    console.error('Error getting variants:', error);
    res.status(500).json({ error: 'Failed to get variants' });
  }
});
*/

// Create product variant
// TODO: Re-enable when productVariantService is fixed
/*
router.post('/:productId/variants', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { productId } = req.params;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const variantId = await productVariantService.createVariant({
      ...req.body,
      productId: parseInt(productId)
    }, user.id);

    res.json({ id: variantId });
  } catch (error) {
    console.error('Error creating variant:', error);
    res.status(500).json({ error: 'Failed to create variant' });
  }
});
*/

// Add variant attribute
// TODO: Re-enable when productVariantService is fixed
/*
router.post('/:productId/variants/:variantId/attributes', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { productId, variantId } = req.params;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const attributeId = await productVariantService.addVariantAttribute({
      variantId: parseInt(variantId),
      ...req.body
    });

    res.json({ id: attributeId });
  } catch (error) {
    console.error('Error adding variant attribute:', error);
    res.status(500).json({ error: 'Failed to add variant attribute' });
  }
});
*/

// TODO: Re-enable all variant routes when productVariantService is fixed
/*
// Get variant attributes
router.get('/:productId/variants/:variantId/attributes', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { variantId } = req.params;
    const attributes = await productVariantService.getVariantAttributes(parseInt(variantId));
    res.json(attributes);
  } catch (error) {
    console.error('Error getting variant attributes:', error);
    res.status(500).json({ error: 'Failed to get variant attributes' });
  }
});

// Update product variant
router.put('/:productId/variants/:variantId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { variantId } = req.params;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await productVariantService.updateVariant(parseInt(variantId), req.body);
    res.json({ message: 'Variant updated successfully' });
  } catch (error) {
    console.error('Error updating variant:', error);
    res.status(500).json({ error: 'Failed to update variant' });
  }
});

// Delete product variant
router.delete('/:productId/variants/:variantId', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { user } = req;
    const { variantId } = req.params;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await productVariantService.deleteVariant(parseInt(variantId));
    res.json({ message: 'Variant deleted successfully' });
  } catch (error) {
    console.error('Error deleting variant:', error);
    res.status(500).json({ error: 'Failed to delete variant' });
  }
});

// Update variant stock
router.put('/:productId/variants/:variantId/stock', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { user } = req;
    const { productId, variantId } = req.params;
    const { quantity } = req.body;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await productVariantService.updateVariantStock(
      parseInt(variantId),
      req.user.storeId,
      parseInt(quantity)
    );
    res.json({ message: 'Variant stock updated successfully' });
  } catch (error) {
    console.error('Error updating variant stock:', error);
    res.status(500).json({ error: 'Failed to update variant stock' });
  }
});

// Set default variant
router.post('/:productId/variants/:variantId/default', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { user } = req;
    const { productId, variantId } = req.params;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await productVariantService.setDefaultVariant(
      parseInt(productId),
      parseInt(variantId)
    );
    res.json({ message: 'Default variant set successfully' });
  } catch (error) {
    console.error('Error setting default variant:', error);
    res.status(500).json({ error: 'Failed to set default variant' });
  }
});

// Get available variants for a store
router.get('/:productId/available-variants', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { productId } = req.params;
    const variants = await productVariantService.getAvailableVariants(
      parseInt(productId),
      req.user.storeId
    );
    res.json(variants);
  } catch (error) {
    console.error('Error getting available variants:', error);
    res.status(500).json({ error: 'Failed to get available variants' });
  }
});

// Get variant stock
router.get('/:productId/variants/:variantId/stock', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { productId, variantId } = req.params;
    const stock = await productVariantService.getVariantStock(
      parseInt(variantId),
      req.user.storeId
    );
    res.json({ stock });
  } catch (error) {
    console.error('Error getting variant stock:', error);
    res.status(500).json({ error: 'Failed to get variant stock' });
  }
});

// Check stock threshold
router.get('/:productId/check-stock-threshold', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { productId } = req.params;
    const lowStockVariants = await productVariantService.checkStockThreshold(
      parseInt(productId),
      req.user.storeId
    );
    res.json(lowStockVariants);
  } catch (error) {
    console.error('Error checking stock threshold:', error);
    res.status(500).json({ error: 'Failed to check stock threshold' });
  }
});

// Update variant stock
router.put('/:productId/variants/:variantId/stock', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { variantId } = req.params;
    const { quantity } = req.body;

    await productVariantService.updateVariantStock(
      parseInt(variantId),
      user.storeId,
      quantity
    );

    res.json({ message: 'Variant stock updated successfully' });
  } catch (error) {
    console.error('Error updating variant stock:', error);
    res.status(500).json({ error: 'Failed to update variant stock' });
  }
});

// Get available variants for a product
router.get('/:productId/available-variants', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { productId } = req.params;

    const availableVariants = await productVariantService.getAvailableVariants(
      parseInt(productId),
      user.storeId
    );

    res.json(availableVariants);
  } catch (error) {
    console.error('Error getting available variants:', error);
    res.status(500).json({ error: 'Failed to get available variants' });
  }
});
*/

export default router;
