import express, { Request, Response } from 'express';
import { productVariantService } from '../../services/productVariantService';
import { authMiddleware } from '../../middleware/auth';
import { UserRole } from '../../db/schema';

const router = express.Router();

// Get variants for a product
router.get('/:productId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const variants = await productVariantService.getVariants(parseInt(productId));
    res.json(variants);
  } catch (error) {
    console.error('Error getting variants:', error);
    res.status(500).json({ error: 'Failed to get variants' });
  }
});

// Get default variant for a product
router.get('/:productId/default', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const variant = await productVariantService.getDefaultVariant(parseInt(productId));
    res.json(variant);
  } catch (error) {
    console.error('Error getting default variant:', error);
    res.status(500).json({ error: 'Failed to get default variant' });
  }
});

// Create variant
router.post('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const variantId = await productVariantService.createVariant(req.body);
    res.json({ id: variantId });
  } catch (error) {
    console.error('Error creating variant:', error);
    res.status(500).json({ error: 'Failed to create variant' });
  }
});

// Update variant
router.put('/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await productVariantService.updateVariant(parseInt(id), req.body);
    res.json({ message: 'Variant updated successfully' });
  } catch (error) {
    console.error('Error updating variant:', error);
    res.status(500).json({ error: 'Failed to update variant' });
  }
});

// Delete variant
router.delete('/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await productVariantService.deleteVariant(parseInt(id));
    res.json({ message: 'Variant deleted successfully' });
  } catch (error) {
    console.error('Error deleting variant:', error);
    res.status(500).json({ error: 'Failed to delete variant' });
  }
});

// Set default variant
router.post('/:productId/default', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const { variantId } = req.body;
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await productVariantService.setDefaultVariant(parseInt(productId), parseInt(variantId));
    res.json({ message: 'Default variant set successfully' });
  } catch (error) {
    console.error('Error setting default variant:', error);
    res.status(500).json({ error: 'Failed to set default variant' });
  }
});

// Get variant stock
router.get('/:variantId/stock/:storeId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { variantId, storeId } = req.params;
    const stock = await productVariantService.getVariantStock(
      parseInt(variantId),
      parseInt(storeId)
    );
    res.json({ stock });
  } catch (error) {
    console.error('Error getting variant stock:', error);
    res.status(500).json({ error: 'Failed to get variant stock' });
  }
});

// Update variant stock
router.put('/:variantId/stock/:storeId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { variantId, storeId } = req.params;
    const { quantity, productId } = req.body;
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await productVariantService.updateVariantStock(
      parseInt(variantId),
      parseInt(storeId),
      parseInt(quantity),
      parseInt(productId)
    );
    res.json({ message: 'Variant stock updated successfully' });
  } catch (error) {
    console.error('Error updating variant stock:', error);
    res.status(500).json({ error: 'Failed to update variant stock' });
  }
});

export default router;
