import express, { Request, Response } from 'express';
import { db } from '../../../db';
import {
  sales,
  products,
  stores,
  creditSales,
  creditSaleItems,
  inventory,
  UserRole,
  salesItems
} from '../../../db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { authMiddleware } from '../../../middleware/auth';

const router = express.Router();

// Admin Dashboard
router.get('/admin/sales', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { startDate, endDate } = req.query;

    const salesData = await db
      .select({
        date: sql<string>`DATE(${sales.createdAt})`,
        amount: sql<number>`SUM(${sales.totalAmount})`
      })
      .from(sales)
      .where(
        sql`${sales.createdAt} BETWEEN ${startDate} AND ${endDate}`
      )
      .groupBy(sql`DATE(${sales.createdAt})`)
      .orderBy(sql`DATE(${sales.createdAt})`);

    res.json(salesData);
  } catch (error) {
    console.error('Error fetching sales data:', error);
    res.status(500).json({ error: 'Failed to fetch sales data' });
  }
});

router.get('/admin/low-stock', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const threshold = 5; // configurable threshold

    const lowStock = await db
      .select({
        productId: products.id,
        productName: products.name,
        quantity: inventory.quantity,
        threshold: inventory.threshold,
        storeId: inventory.storeId
      })
      .from(products)
      .leftJoin(inventory, eq(inventory.productId, products.id))
      .where(
        sql`${inventory.quantity} <= ${threshold}`
      );

    res.json(lowStock);
  } catch (error) {
    console.error('Error fetching low stock items:', error);
    res.status(500).json({ error: 'Failed to fetch low stock items' });
  }
});

router.get('/admin/top-shops', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { startDate, endDate } = req.query;

    const topShops = await db
      .select({
        storeId: stores.id,
        storeName: stores.name,
        salesAmount: sql<number>`SUM(${sales.totalAmount})`
      })
      .from(sales)
      .leftJoin(stores, eq(stores.id, sales.storeId))
      .where(
        sql`${sales.createdAt} BETWEEN ${startDate} AND ${endDate}`
      )
      .groupBy(stores.id, stores.name)
      .orderBy(sql`SUM(${sales.totalAmount}) DESC`)
      .limit(5);

    res.json(topShops);
  } catch (error) {
    console.error('Error fetching top shops:', error);
    res.status(500).json({ error: 'Failed to fetch top shops' });
  }
});

// Store Owner Dashboard
router.get('/store/:storeId/performance', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { storeId } = req.params;
    const { startDate, endDate } = req.query;

    const performance = await db
      .select({
        date: sql<string>`DATE(${sales.createdAt})`,
        sales: sql<number>`SUM(${sales.totalAmount})`,
        profit: sql<number>`SUM(${sales.totalAmount})`
      })
      .from(sales)
      .where(
        and(
          eq(sales.storeId, parseInt(storeId)),
          sql`${sales.createdAt} BETWEEN ${startDate} AND ${endDate}`
        )
      )
      .groupBy(sql`DATE(${sales.createdAt})`)
      .orderBy(sql`DATE(${sales.createdAt})`);

    res.json(performance);
  } catch (error) {
    console.error('Error fetching shop performance:', error);
    res.status(500).json({ error: 'Failed to fetch shop performance' });
  }
});

router.get('/store/:storeId/bnpl', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { storeId } = req.params;

    const bnplData = await db
      .select({
        id: creditSales.id,
        customerId: creditSales.customerId,
        amount: creditSales.totalAmount,
        dueDate: creditSales.dueDate,
        status: creditSales.status
      })
      .from(creditSales)
      .where(
        eq(creditSales.storeId, parseInt(storeId))
      );

    res.json(bnplData);
  } catch (error) {
    console.error('Error fetching BNPL data:', error);
    res.status(500).json({ error: 'Failed to fetch BNPL data' });
  }
});

router.get('/store/:storeId/inventory', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { storeId } = req.params;

    const inventoryData = await db
      .select({
        productId: products.id,
        productName: products.name,
        quantity: inventory.quantity,
        threshold: inventory.threshold
      })
      .from(products)
      .leftJoin(inventory, eq(inventory.productId, products.id))
      .where(
        eq(inventory.storeId, parseInt(storeId))
      );

    res.json(inventoryData);
  } catch (error) {
    console.error('Error fetching inventory:', error);
    res.status(500).json({ error: 'Failed to fetch inventory' });
  }
});

// Sales Rep Dashboard
router.get('/sales-rep/:userId/daily-sales', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { startDate, endDate } = req.query;

    const salesData = await db
      .select({
        date: sql<string>`DATE(${sales.createdAt})`,
        amount: sales.totalAmount,
        isBnpl: sql<boolean>`EXISTS (
          SELECT 1 FROM credit_sales WHERE credit_sales.sale_id = sales.id
        )`
      })
      .from(sales)
      .where(
        and(
          eq(sales.userId, parseInt(userId)),
          sql`${sales.createdAt} BETWEEN ${startDate} AND ${endDate}`
        )
      );

    res.json(salesData);
  } catch (error) {
    console.error('Error fetching daily sales:', error);
    res.status(500).json({ error: 'Failed to fetch daily sales' });
  }
});

router.get('/sales-rep/:userId/customers', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    const customers = await db
      .select({
        id: creditSales.customerId,
        name: sql<string>`customers.name`,
        phone: sql<string>`customers.phone`,
        lastPurchase: sql<string>`MAX(${sales.createdAt})`
      })
      .from(creditSales)
      .leftJoin(sales, eq(sales.id, creditSales.saleId))
      .leftJoin(sql`customers`, sql`customers.id = ${creditSales.customerId}`)
      .where(
        eq(creditSales.storeId, parseInt(userId))
      )
      .groupBy(creditSales.customerId);

    res.json(customers);
  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({ error: 'Failed to fetch customers' });
  }
});

router.get('/store/:storeId/top-products', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { storeId } = req.params;
    const { startDate, endDate } = req.query;

    const topProducts = await db
      .select({
        productId: products.id,
        productName: products.name,
        salesCount: sql<number>`COUNT(*)`
      })
      .from(sales)
      .leftJoin(salesItems, eq(salesItems.saleId, sales.id))
      .leftJoin(products, eq(products.id, salesItems.productId))
      .where(
        and(
          eq(sales.storeId, parseInt(storeId)),
          sql`${sales.createdAt} BETWEEN ${startDate} AND ${endDate}`
        )
      )
      .groupBy(products.id, products.name)
      .orderBy(sql`COUNT(*) DESC`)
      .limit(5);

    res.json(topProducts);
  } catch (error) {
    console.error('Error fetching top products:', error);
    res.status(500).json({ error: 'Failed to fetch top products' });
  }
});

export default router;
