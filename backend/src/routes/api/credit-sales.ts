import express, { Request, Response } from 'express';
import { db } from '../db';
import { creditSales, creditSaleItems, creditPayments, products, stores, customers, inventory } from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { authMiddleware } from '../middleware/auth';
import { UserRole } from '../db/schema';

const router = express.Router();

// Create credit sale
router.post('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { customerId, items, dueDate, notes } = req.body;

    // Validate items and calculate total
    let totalAmount = 0;
    for (const item of items) {
      const product = await db
        .select({
          id: products.id,
          retailPrice: products.retailPrice,
          storeQuantity: sql`inventory.quantity`
        })
        .from(products)
        .leftJoin(inventory, eq(inventory.productId, products.id))
        .where(
          and(
            eq(products.id, item.productId),
            eq(inventory.storeId, user.storeId)
          )
        )
        .get();

      if (!product || product.storeQuantity < item.quantity) {
        return res.status(400).json({ error: 'Insufficient inventory for product' });
      }

      totalAmount += product.retailPrice * item.quantity;
    }

    // Create credit sale in transaction
    const result = await db.transaction(async (tx) => {
      // Create credit sale
      const creditSale = await tx.insert(creditSales).values({
        customerId,
        storeId: user.storeId,
        totalAmount,
        dueDate: new Date(dueDate),
        notes,
        status: 'pending'
      }).returning().then(r => r[0]);

      // Create credit sale items
      const itemsResult = await tx.insert(creditSaleItems).values(
        items.map(item => ({
          creditSaleId: creditSale.id,
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice || 0,
          subtotal: item.quantity * (item.unitPrice || 0)
        }))
      );

      // Update inventory
      for (const item of items) {
        await tx.update(inventory)
          .set({
            quantity: sql`${inventory.quantity} - ${item.quantity}`
          })
          .where(
            and(
              eq(inventory.storeId, user.storeId),
              eq(inventory.productId, item.productId)
            )
          );
      }

      return { creditSale, itemsResult };
    });

    res.json(result.creditSale);
  } catch (error) {
    console.error('Create credit sale error:', error);
    res.status(500).json({ error: 'Failed to create credit sale' });
  }
});

// Record payment
router.post('/:id/payment', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { amount, paymentMethod, paymentReference, notes } = req.body;
    const { user } = req as any;

    // Get credit sale
    const creditSale = await db
      .select()
      .from(creditSales)
      .where(eq(creditSales.id, parseInt(id)))
      .get();

    if (!creditSale) {
      return res.status(404).json({ error: 'Credit sale not found' });
    }

    // Check if user has permission
    if (user.role !== UserRole.ADMIN && user.storeId !== creditSale.storeId) {
      return res.status(403).json({ error: 'Unauthorized access' });
    }

    // Record payment
    const payment = await db.insert(creditPayments).values({
      creditSaleId: parseInt(id),
      amount,
      paymentMethod,
      paymentReference,
      notes
    }).returning().then(r => r[0]);

    // Calculate remaining balance
    const totalPayments = await db
      .select({ total: sql<number>`SUM(${creditPayments.amount})` })
      .from(creditPayments)
      .where(eq(creditPayments.creditSaleId, parseInt(id)))
      .get();

    const remainingBalance = creditSale.totalAmount - totalPayments.total;

    // Update credit sale status if fully paid
    if (remainingBalance <= 0) {
      await db.update(creditSales)
        .set({
          status: 'paid',
          updatedAt: new Date()
        })
        .where(eq(creditSales.id, parseInt(id)));
    }

    res.json({
      payment,
      remainingBalance,
      status: remainingBalance <= 0 ? 'paid' : 'pending'
    });
  } catch (error) {
    console.error('Record payment error:', error);
    res.status(500).json({ error: 'Failed to record payment' });
  }
});

// Get credit sales for customer
router.get('/customer/:customerId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { customerId } = req.params;
    const { status, page = 1, limit = 10 } = req.query;
    const { user } = req as any;

    let query = db
      .select({
        creditSale: creditSales,
        store: stores,
        customer: customers,
        items: sql`(
          SELECT array_agg(json_build_object(
            'product_id', credit_sale_items.product_id,
            'quantity', credit_sale_items.quantity,
            'unit_price', credit_sale_items.unit_price,
            'subtotal', credit_sale_items.subtotal
          ))
          FROM credit_sale_items
          WHERE credit_sale_items.credit_sale_id = credit_sales.id
        )`
      })
      .from(creditSales)
      .leftJoin(stores, eq(stores.id, creditSales.storeId))
      .leftJoin(customers, eq(customers.id, creditSales.customerId))
      .where(eq(creditSales.customerId, parseInt(customerId)))
      .orderBy(sql`credit_sales.created_at DESC`);

    // Filter by status if provided
    if (status) {
      query = query.where(eq(creditSales.status, status.toString()));
    }

    // Apply store filter for non-admin users
    if (user.role !== UserRole.ADMIN) {
      query = query.where(eq(creditSales.storeId, user.storeId));
    }

    const [sales, countResult] = await Promise.all([
      query
        .limit(Number(limit))
        .offset((Number(page) - 1) * Number(limit)),
      db.select({ count: sql`count(*)` }).from(creditSales)
    ]);

    const count = countResult[0];

    res.json({
      sales,
      total: count.count,
      page: Number(page),
      limit: Number(limit)
    });
  } catch (error) {
    console.error('Get customer credit sales error:', error);
    res.status(500).json({ error: 'Failed to get credit sales' });
  }
});

// Get payment history for credit sale
router.get('/:id/payments', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { user } = req as any;

    const payments = await db
      .select({
        payment: creditPayments,
        creditSale: creditSales
      })
      .from(creditPayments)
      .leftJoin(creditSales, eq(creditSales.id, creditPayments.creditSaleId))
      .where(eq(creditPayments.creditSaleId, parseInt(id)))
      .orderBy(sql`credit_payments.created_at DESC`);

    res.json(payments);
  } catch (error) {
    console.error('Get payment history error:', error);
    res.status(500).json({ error: 'Failed to get payment history' });
  }
});

// Get customer balance
router.get('/balance/:customerId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { customerId } = req.params;
    const { user } = req as any;

    const [totalOwed, totalPaid] = await Promise.all([
      db.select({ total: sql<number>`SUM(${creditSales.totalAmount})` })
        .from(creditSales)
        .where(
          and(
            eq(creditSales.customerId, parseInt(customerId)),
            eq(creditSales.status, 'pending')
          )
        )
        .limit(1),

      db.select({ total: sql<number>`SUM(${creditPayments.amount})` })
        .from(creditPayments)
        .leftJoin(creditSales, eq(creditSales.id, creditPayments.creditSaleId))
        .where(eq(creditSales.customerId, parseInt(customerId)))
        .limit(1)
    ]);

    const totalOwedAmount = totalOwed[0]?.total || 0;
    const totalPaidAmount = totalPaid[0]?.total || 0;
    const balance = totalOwedAmount - totalPaidAmount;

    res.json({
      customerId,
      totalOwed: totalOwedAmount,
      totalPaid: totalPaidAmount,
      balance,
      status: balance <= 0 ? 'paid' : 'pending'
    });
  } catch (error) {
    console.error('Get customer balance error:', error);
    res.status(500).json({ error: 'Failed to get customer balance' });
  }
});

export default router;
