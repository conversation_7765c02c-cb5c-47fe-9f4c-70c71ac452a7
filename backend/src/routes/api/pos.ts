import express, { Request, Response } from 'express';
import { db } from '../../db';
import { products, stores, inventory, sales, salesItems } from '../../db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { UserRole } from '../../db/schema';
import { authMiddleware } from '../../middleware/auth';

const router = express.Router();

// Get product by name or barcode
router.get('/search', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { query } = req.query;
    const { user } = req as any;

    if (!query) {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const products = await db
      .select({
        id: products.id,
        name: products.name,
        sku: products.sku,
        brand: products.brand,
        retailPrice: products.retailPrice,
        currency: products.currency,
        inventory: sql<number>`(
          SELECT COALESCE(SUM(${inventory.quantity}), 0)
          FROM ${inventory}
          WHERE ${inventory.productId} = ${products.id}
          AND ${inventory.storeId} = ${user.storeId}
        )`
      })
      .from(products)
      .where(
        and(
          products.isActive,
          sql`LOWER(${products.name}) LIKE LOWER(${sql.literal(`%${query}%`)}
            OR LOWER(${products.sku}) LIKE LOWER(${sql.literal(`%${query}%`)}
          )
        )
      )
      .limit(10);

    res.json(products);
  } catch (error) {
    console.error('Search products error:', error);
    res.status(500).json({ error: 'Failed to search products' });
  }
});

// Get product details by ID
router.get('/:productId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const { user } = req as any;

    const product = await db
      .select({
        product: products,
        inventory: inventory
      })
      .from(products)
      .leftJoin(inventory, and(
        eq(inventory.productId, products.id),
        eq(inventory.storeId, user.storeId)
      ))
      .where(eq(products.id, parseInt(productId)))
      .get();

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json({
      ...product.product,
      inventory: product.inventory?.quantity || 0
    });
  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({ error: 'Failed to get product details' });
  }
});

// Create sale
router.post('/sale', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { items, paymentMethod, discountAmount, notes } = req.body;
    const { user } = req as any;

    if (!items || !items.length) {
      return res.status(400).json({ error: 'Items are required' });
    }

    const transaction = await db.transaction(async (tx) => {
      // Calculate total amount
      let totalAmount = 0;
      for (const item of items) {
        totalAmount += item.quantity * item.unitPrice;
      }

      // Create sale
      const sale = await tx
        .insert(sales)
        .values({
          storeId: user.storeId,
          userId: user.id,
          totalAmount,
          discountAmount: discountAmount || 0,
          paymentMethod,
          paymentStatus: 'completed',
          notes
        })
        .returning()
        .get();

      // Create sale items and update inventory
      for (const item of items) {
        // Check inventory
        const inventoryItem = await tx
          .select()
          .from(inventory)
          .where(and(
            eq(inventory.productId, item.productId),
            eq(inventory.storeId, user.storeId)
          ))
          .get();

        if (!inventoryItem || inventoryItem.quantity < item.quantity) {
          throw new Error(`Insufficient inventory for product ${item.productId}`);
        }

        // Create sale item
        await tx
          .insert(salesItems)
          .values({
            saleId: sale.id,
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            subtotal: item.quantity * item.unitPrice
          });

        // Update inventory
        await tx
          .update(inventory)
          .set({
            quantity: sql`${inventory.quantity} - ${item.quantity}`,
            lastUpdated: new Date()
          })
          .where(and(
            eq(inventory.productId, item.productId),
            eq(inventory.storeId, user.storeId)
          ));
      }

      return sale;
    });

    res.json(transaction);
  } catch (error) {
    console.error('Create sale error:', error);
    res.status(500).json({ error: error.message || 'Failed to create sale' });
  }
});

// Get recent sales
router.get('/sales', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const limit = parseInt(req.query.limit as string) || 10;
    const page = parseInt(req.query.page as string) || 1;

    const sales = await db
      .select({
        sale: sales,
        items: sql`(
          SELECT COUNT(*)
          FROM ${salesItems}
          WHERE ${salesItems.saleId} = ${sales.id}
        )`
      })
      .from(sales)
      .where(eq(sales.storeId, user.storeId))
      .orderBy(sql`${sales.createdAt}.desc`)
      .limit(limit)
      .offset((page - 1) * limit);

    const count = await db
      .select({
        count: sql<number>`COUNT(*)`
      })
      .from(sales)
      .where(eq(sales.storeId, user.storeId))
      .get();

    res.json({
      sales,
      total: count?.count || 0,
      page,
      limit
    });
  } catch (error) {
    console.error('Get sales error:', error);
    res.status(500).json({ error: 'Failed to get sales' });
  }
});

export default router;
