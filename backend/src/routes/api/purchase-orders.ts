import express, { Request, Response } from 'express';
import { db } from '../../db';
import {
  purchaseOrders,
  purchaseOrderItems,
  purchasePayments,
  products,
  stores,
  suppliers,
  inventory,
  UserRole
} from '../../db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { authMiddleware } from '../../middleware/auth';

const router = express.Router();

// Create purchase order
router.post('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { supplierId, storeId, orderNumber, deliveryDate, items, notes } = req.body;

    // Calculate total amount
    let totalAmount = 0;
    for (const item of items) {
      totalAmount += item.quantity * item.unitPrice;
    }

    // Create purchase order in transaction
    const result = await db.transaction(async (tx) => {
      // Create purchase order
      const order = await tx.insert(purchaseOrders).values({
        supplierId,
        storeId,
        orderNumber,
        expectedDate: deliveryDate ? new Date(deliveryDate) : null,
        totalAmount: totalAmount.toString(),
        notes,
        createdBy: user.id
      }).returning().then(r => r[0]);

      // Create purchase order items
      const itemsResult = await tx.insert(purchaseOrderItems).values(
        items.map(item => ({
          purchaseOrderId: order.id,
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          subtotal: item.quantity * item.unitPrice,
          notes: item.notes
        }))
      );

      return { order, itemsResult };
    });

    res.status(201).json(result.order);
  } catch (error) {
    console.error('Error creating purchase order:', error);
    res.status(500).json({ error: 'Failed to create purchase order' });
  }
});

// Get purchase order by ID
router.get('/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const purchaseOrderResult = await db
      .select({
        order: purchaseOrders,
        supplier: suppliers,
        store: stores,
        items: sql`(
          SELECT array_agg(json_build_object(
            'id', purchase_order_items.id,
            'product_id', purchase_order_items.product_id,
            'product_name', products.name,
            'quantity', purchase_order_items.quantity,
            'unit_price', purchase_order_items.unit_price,
            'subtotal', purchase_order_items.subtotal,
            'notes', purchase_order_items.notes
          ))
          FROM purchase_order_items
          LEFT JOIN products ON products.id = purchase_order_items.product_id
          WHERE purchase_order_items.purchase_order_id = ${id}
        )`,
        payments: sql`(
          SELECT array_agg(json_build_object(
            'id', purchase_payments.id,
            'amount', purchase_payments.amount,
            'payment_method', purchase_payments.payment_method,
            'payment_reference', purchase_payments.payment_reference,
            'paid_by', purchase_payments.paid_by,
            'created_at', purchase_payments.created_at
          ))
          FROM purchase_payments
          WHERE purchase_payments.purchase_order_id = ${id}
        )`
      })
      .from(purchaseOrders)
      .leftJoin(suppliers, eq(suppliers.id, purchaseOrders.supplierId))
      .leftJoin(stores, eq(stores.id, purchaseOrders.storeId))
      .where(eq(purchaseOrders.id, parseInt(id)))
      .limit(1);

    const purchaseOrder = purchaseOrderResult[0];

    if (!purchaseOrder) {
      return res.status(404).json({ error: 'Purchase order not found' });
    }

    res.json(purchaseOrder);
  } catch (error) {
    console.error('Error getting purchase order:', error);
    res.status(500).json({ error: 'Failed to get purchase order' });
  }
});

// Get purchase orders by supplier
router.get('/supplier/:supplierId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { supplierId } = req.params;
    const { status, page = 1, limit = 10 } = req.query;

    const whereConditions = [eq(purchaseOrders.supplierId, parseInt(supplierId))];

    if (status) {
      whereConditions.push(eq(purchaseOrders.status, status.toString()));
    }

    const query = db
      .select({
        order: purchaseOrders,
        store: stores,
        totalPaid: sql<number>`COALESCE(SUM(${purchasePayments.amount}), 0)`
      })
      .from(purchaseOrders)
      .leftJoin(stores, eq(stores.id, purchaseOrders.storeId))
      .leftJoin(purchasePayments, eq(purchasePayments.purchaseOrderId, purchaseOrders.id))
      .where(and(...whereConditions))
      .orderBy(sql`purchase_orders.created_at DESC`);

    const [orders, countResult] = await Promise.all([
      query
        .limit(Number(limit))
        .offset((Number(page) - 1) * Number(limit)),
      db.select({ count: sql`count(*)` }).from(purchaseOrders)
    ]);

    const count = countResult[0];

    res.json({
      orders,
      total: count.count,
      page: Number(page),
      limit: Number(limit)
    });
  } catch (error) {
    console.error('Error getting supplier orders:', error);
    res.status(500).json({ error: 'Failed to get supplier orders' });
  }
});

// Update purchase order status
router.put('/:id/status', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const order = await db.update(purchaseOrders)
      .set({
        status,
        updatedAt: new Date()
      })
      .where(eq(purchaseOrders.id, parseInt(id)))
      .returning()
      .then(r => r[0]);

    if (!order) {
      return res.status(404).json({ error: 'Purchase order not found' });
    }

    // If status is 'received', update inventory
    if (status === 'received') {
      const items = await db
        .select({
          productId: purchaseOrderItems.productId,
          quantity: purchaseOrderItems.quantity
        })
        .from(purchaseOrderItems)
        .where(eq(purchaseOrderItems.purchaseOrderId, parseInt(id)));

      await db.transaction(async (tx) => {
        for (const item of items) {
          await tx.update(inventory)
            .set({
              quantity: sql`${inventory.quantity} + ${item.quantity}`
            })
            .where(
              and(
                eq(inventory.productId, item.productId),
                eq(inventory.storeId, order.storeId)
              )
            );
        }
      });
    }

    res.json(order);
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({ error: 'Failed to update order status' });
  }
});

// Record payment
router.post('/:id/payment', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { user } = req as any;
    const { amount, paymentMethod, paymentReference, notes } = req.body;

    const payment = await db.insert(purchasePayments).values({
      purchaseOrderId: parseInt(id),
      amount: amount.toString(),
      paymentMethod,
      referenceNumber: paymentReference,
      notes,
      createdBy: user.id
    }).returning().then(r => r[0]);

    // Update order status if fully paid
    const orderResult = await db
      .select({
        totalAmount: purchaseOrders.totalAmount,
        totalPaid: sql<number>`COALESCE(SUM(${purchasePayments.amount}), 0)`
      })
      .from(purchaseOrders)
      .leftJoin(purchasePayments, eq(purchasePayments.purchaseOrderId, purchaseOrders.id))
      .where(eq(purchaseOrders.id, parseInt(id)))
      .limit(1);

    const order = orderResult[0];

    if (order.totalPaid >= Number(order.totalAmount)) {
      await db.update(purchaseOrders)
        .set({
          status: 'paid',
          updatedAt: new Date()
        })
        .where(eq(purchaseOrders.id, parseInt(id)));
    }

    res.json(payment);
  } catch (error) {
    console.error('Error recording payment:', error);
    res.status(500).json({ error: 'Failed to record payment' });
  }
});

export default router;
