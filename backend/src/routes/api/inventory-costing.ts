import express, { Request, Response } from 'express';
import { inventoryCostingService } from '../../services/inventoryCostingService';
import { authMiddleware } from '../../middleware/auth';
import { UserRole, stores } from '../../db/schema';
import { db } from '../../db';
import { eq } from 'drizzle-orm';

const router = express.Router();

// Inventory Transactions
router.post('/transactions', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const transactionId = await inventoryCostingService.recordTransaction(req.body, user.id);
    res.json({ id: transactionId });
  } catch (error) {
    console.error('Error recording transaction:', error);
    res.status(500).json({ error: 'Failed to record transaction' });
  }
});

// Get Inventory Cost
router.get('/cost/:variantId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { variantId } = req.params;
    const { quantity } = req.query;

    const cost = await inventoryCostingService.getInventoryCost(
      user.storeId,
      parseInt(variantId),
      parseInt(quantity as string),
      user.store.inventoryCostingMethod as any
    );

    res.json({ cost });
  } catch (error) {
    console.error('Error getting inventory cost:', error);
    res.status(500).json({ error: 'Failed to get inventory cost' });
  }
});

// Get COGS
router.get('/cogs/:variantId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { variantId } = req.params;
    const { quantity } = req.query;

    const cogs = await inventoryCostingService.getCOGS(
      user.storeId,
      parseInt(variantId),
      parseInt(quantity as string),
      new Date()
    );

    res.json({ cogs });
  } catch (error) {
    console.error('Error getting COGS:', error);
    res.status(500).json({ error: 'Failed to get COGS' });
  }
});

// Inventory Valuation
router.get('/valuation', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { variantId, startDate, endDate } = req.query;

    const valuation = await inventoryCostingService.getInventoryValuation(
      user.storeId,
      variantId ? parseInt(variantId as string) : undefined,
      startDate as string,
      endDate as string
    );

    res.json(valuation);
  } catch (error) {
    console.error('Error getting inventory valuation:', error);
    res.status(500).json({ error: 'Failed to get inventory valuation' });
  }
});

// Inventory History
router.get('/history/:variantId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { variantId } = req.params;
    const { startDate, endDate } = req.query;

    const history = await inventoryCostingService.getInventoryHistory(
      user.storeId,
      parseInt(variantId),
      startDate as string,
      endDate as string
    );

    res.json(history);
  } catch (error) {
    console.error('Error getting inventory history:', error);
    res.status(500).json({ error: 'Failed to get inventory history' });
  }
});

// Update Store Costing Method
router.put('/store/:storeId/costing-method', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { storeId } = req.params;
    const { costingMethod } = req.body;

    if (user.role !== UserRole.ADMIN) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await db.update(stores)
      .set({
        inventoryCostingMethod: costingMethod
      })
      .where(eq(stores.id, parseInt(storeId)));

    res.json({ message: 'Costing method updated successfully' });
  } catch (error) {
    console.error('Error updating costing method:', error);
    res.status(500).json({ error: 'Failed to update costing method' });
  }
});

export default router;
