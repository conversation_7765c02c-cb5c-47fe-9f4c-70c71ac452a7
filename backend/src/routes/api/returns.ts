import express, { Request, Response } from 'express';
import { returnService } from '../../services/returnService';
import { authMiddleware } from '../../middleware/auth';
import { UserRole, refunds, users } from '../../db/schema';
import { db } from '../../db';
import { eq, sql } from 'drizzle-orm';

const router = express.Router();

// Create return
router.post('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const returnId = await returnService.createReturn(req.body);
    res.json({ id: returnId });
  } catch (error) {
    console.error('Error creating return:', error);
    res.status(500).json({ error: 'Failed to create return' });
  }
});

// Approve return
router.post('/:id/approve', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await returnService.approveReturn(parseInt(id), user.id);
    res.json({ message: 'Return approved successfully' });
  } catch (error) {
    console.error('Error approving return:', error);
    res.status(500).json({ error: 'Failed to approve return' });
  }
});

// Reject return
router.post('/:id/reject', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { user } = req as any;
    const { reason } = req.body;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await returnService.rejectReturn(parseInt(id), user.id, reason);
    res.json({ message: 'Return rejected successfully' });
  } catch (error) {
    console.error('Error rejecting return:', error);
    res.status(500).json({ error: 'Failed to reject return' });
  }
});

// Get returns
router.get('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { status, startDate, endDate, limit = 10, page = 1 } = req.query;

    const returns = await returnService.getReturns(
      user.storeId,
      status,
      startDate,
      endDate,
      parseInt(limit),
      parseInt(page)
    );

    res.json(returns);
  } catch (error) {
    console.error('Error getting returns:', error);
    res.status(500).json({ error: 'Failed to get returns' });
  }
});

// Get return details
router.get('/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const returnDetails = await returnService.getReturnDetails(parseInt(id));
    res.json(returnDetails);
  } catch (error) {
    console.error('Error getting return details:', error);
    res.status(500).json({ error: 'Failed to get return details' });
  }
});

// Get refunds for a return
router.get('/:id/refunds', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const refunds = await db
      .select({
        id: refunds.id,
        amount: refunds.amount,
        method: refunds.method,
        status: refunds.status,
        transactionId: refunds.transactionId,
        processedBy: sql<string>`CONCAT(users.first_name, ' ', users.last_name)`,
        createdAt: refunds.createdAt
      })
      .from(refunds)
      .leftJoin(users, eq(users.id, refunds.processedBy))
      .where(eq(refunds.returnId, parseInt(id)));

    res.json(refunds);
  } catch (error) {
    console.error('Error getting refunds:', error);
    res.status(500).json({ error: 'Failed to get refunds' });
  }
});

export default router;
