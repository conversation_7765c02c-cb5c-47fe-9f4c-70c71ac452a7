import express, { Request, Response } from 'express';
import { expenseService } from '../../services/expenseService';
import { authMiddleware } from '../../middleware/auth';
import { UserRole } from '../../db/schema';

const router = express.Router();

// Expense Categories
router.post('/categories', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const categoryId = await expenseService.createExpenseCategory(req.body);
    res.json({ id: categoryId });
  } catch (error) {
    console.error('Error creating expense category:', error);
    res.status(500).json({ error: 'Failed to create expense category' });
  }
});

router.get('/categories', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const categories = await expenseService.getExpenseCategories(user.storeId);
    res.json(categories);
  } catch (error) {
    console.error('Error getting expense categories:', error);
    res.status(500).json({ error: 'Failed to get expense categories' });
  }
});

// Expenses
router.post('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const expenseId = await expenseService.createExpense(req.body, user.storeId, user.id);
    res.json({ id: expenseId });
  } catch (error) {
    console.error('Error creating expense:', error);
    res.status(500).json({ error: 'Failed to create expense' });
  }
});

router.get('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { categoryId, startDate, endDate, status, limit = 10, page = 1 } = req.query;

    const expenses = await expenseService.getExpenses(
      user.storeId,
      parseInt(categoryId as string),
      startDate as string,
      endDate as string,
      status as string,
      parseInt(limit as string),
      parseInt(page as string)
    );

    res.json(expenses);
  } catch (error) {
    console.error('Error getting expenses:', error);
    res.status(500).json({ error: 'Failed to get expenses' });
  }
});

// Financial Reports
router.get('/summary', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Start and end dates are required' });
    }

    const summary = await expenseService.getFinancialSummary(
      user.storeId,
      startDate as string,
      endDate as string
    );

    res.json(summary);
  } catch (error) {
    console.error('Error getting financial summary:', error);
    res.status(500).json({ error: 'Failed to get financial summary' });
  }
});

export default router;
