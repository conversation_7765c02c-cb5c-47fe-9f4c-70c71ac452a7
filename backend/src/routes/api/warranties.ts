import express, { Request, Response } from 'express';
import { warrantyService } from '../../services/warrantyService';
import { authMiddleware } from '../../middleware/auth';
import { UserRole } from '../../db/schema';

const router = express.Router();

// Warranty Types
router.post('/types', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const typeId = await warrantyService.createWarrantyType(req.body);
    res.json({ id: typeId });
  } catch (error) {
    console.error('Error creating warranty type:', error);
    res.status(500).json({ error: 'Failed to create warranty type' });
  }
});

router.get('/types', authMiddleware, async (req: Request, res: Response) => {
  try {
    const types = await warrantyService.getWarrantyTypes();
    res.json(types);
  } catch (error) {
    console.error('Error getting warranty types:', error);
    res.status(500).json({ error: 'Failed to get warranty types' });
  }
});

// Product Warranties
router.post('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const warrantyId = await warrantyService.createWarranty(req.body);
    res.json({ id: warrantyId });
  } catch (error) {
    console.error('Error creating warranty:', error);
    res.status(500).json({ error: 'Failed to create warranty' });
  }
});

router.get('/:productId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const warranties = await warrantyService.getWarranties(parseInt(productId));
    res.json(warranties);
  } catch (error) {
    console.error('Error getting warranties:', error);
    res.status(500).json({ error: 'Failed to get warranties' });
  }
});

// Sale Warranties
router.post('/sale', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const warrantyId = await warrantyService.createSaleWarranty(req.body);
    res.json({ id: warrantyId });
  } catch (error) {
    console.error('Error creating sale warranty:', error);
    res.status(500).json({ error: 'Failed to create sale warranty' });
  }
});

router.get('/sale', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { status, startDate, endDate, limit = 10, page = 1 } = req.query;

    const warranties = await warrantyService.getSaleWarranties(
      user.storeId,
      status as string,
      startDate as string,
      endDate as string,
      parseInt(limit as string),
      parseInt(page as string)
    );

    res.json(warranties);
  } catch (error) {
    console.error('Error getting sale warranties:', error);
    res.status(500).json({ error: 'Failed to get sale warranties' });
  }
});

// Warranty Claims
router.post('/claim', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const claimId = await warrantyService.createWarrantyClaim(req.body, user.id);
    res.json({ id: claimId });
  } catch (error) {
    console.error('Error creating warranty claim:', error);
    res.status(500).json({ error: 'Failed to create warranty claim' });
  }
});

// Expiring Warranties
router.get('/expiring', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { days = 30 } = req.query;
    const expiringWarranties = await warrantyService.getExpiringWarranties(parseInt(days as string));
    res.json(expiringWarranties);
  } catch (error) {
    console.error('Error getting expiring warranties:', error);
    res.status(500).json({ error: 'Failed to get expiring warranties' });
  }
});

export default router;
