import express, { Request, Response } from 'express';
import { db } from '../db';
import { creditSales, customers, creditPayments, notifications } from '../db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { authMiddleware } from '../middleware/auth';
import { UserRole } from '../db/schema';
import { reminderService } from '../services/reminderService';

const router = express.Router();

// Get overdue customers
router.get('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const overdueCustomers = await reminderService.getOverdueCustomers();
    res.json(overdueCustomers);
  } catch (error) {
    console.error('Error getting overdue customers:', error);
    res.status(500).json({ error: 'Failed to get overdue customers' });
  }
});

// Get payment history for customer
router.get('/:customerId/history', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { customerId } = req.params;
    const history = await reminderService.getPaymentHistory(parseInt(customerId));
    res.json(history);
  } catch (error) {
    console.error('Error getting payment history:', error);
    res.status(500).json({ error: 'Failed to get payment history' });
  }
});

// Send reminder SMS
router.post('/:customerId/reminder', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { customerId } = req.params;
    const { message } = req.body;
    
    // Only store managers can send reminders
    const { user } = req as any;
    if (user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const reminderDataResult = await db
      .select({
        creditSale: creditSales,
        customer: customers,
        totalPaid: sql<number>`COALESCE(SUM(${creditPayments.amount}), 0)`
      })
      .from(creditSales)
      .leftJoin(customers, eq(customers.id, creditSales.customerId))
      .leftJoin(creditPayments, eq(creditPayments.creditSaleId, creditSales.id))
      .where(eq(customers.id, parseInt(customerId)))
      .limit(1);

    await reminderService.createReminder(
      reminderDataResult[0],
      message
    );

    res.json({ success: true });
  } catch (error) {
    console.error('Error sending reminder:', error);
    res.status(500).json({ error: 'Failed to send reminder' });
  }
});

export default router;
