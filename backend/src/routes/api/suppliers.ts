import express, { Request, Response } from 'express';
import { db } from '../../db';
import { suppliers, UserRole } from '../../db/schema';
import { eq } from 'drizzle-orm';
import { authMiddleware } from '../../middleware/auth';

const router = express.Router();

// Get all suppliers
router.get('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const suppliersData = await db
      .select()
      .from(suppliers)
      .where(eq(suppliers.isActive, true));
    res.json(suppliersData);
  } catch (error) {
    console.error('Error getting suppliers:', error);
    res.status(500).json({ error: 'Failed to get suppliers' });
  }
});

// Get supplier by ID
router.get('/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const supplierResult = await db
      .select()
      .from(suppliers)
      .where(eq(suppliers.id, parseInt(id)))
      .limit(1);

    const supplier = supplierResult[0];
    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }
    res.json(supplier);
  } catch (error) {
    console.error('Error getting supplier:', error);
    res.status(500).json({ error: 'Failed to get supplier' });
  }
});

// Create supplier
router.post('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { name, phone, email, address, contactPerson, notes } = req.body;

    const supplier = await db.insert(suppliers).values({
      name,
      phone,
      email,
      address,
      contactPerson
    }).returning().then(r => r[0]);

    res.status(201).json(supplier);
  } catch (error) {
    console.error('Error creating supplier:', error);
    res.status(500).json({ error: 'Failed to create supplier' });
  }
});

// Update supplier
router.put('/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, phone, email, address, contactPerson, notes, isActive } = req.body;

    const supplier = await db.update(suppliers)
      .set({
        name,
        phone,
        email,
        address,
        contactPerson,
        isActive,
        updatedAt: new Date()
      })
      .where(eq(suppliers.id, parseInt(id)))
      .returning()
      .then(r => r[0]);

    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    res.json(supplier);
  } catch (error) {
    console.error('Error updating supplier:', error);
    res.status(500).json({ error: 'Failed to update supplier' });
  }
});

// Delete supplier (soft delete)
router.delete('/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const supplier = await db.update(suppliers)
      .set({
        isActive: false,
        updatedAt: new Date()
      })
      .where(eq(suppliers.id, parseInt(id)))
      .returning()
      .then(r => r[0]);

    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    res.json({ message: 'Supplier deleted successfully' });
  } catch (error) {
    console.error('Error deleting supplier:', error);
    res.status(500).json({ error: 'Failed to delete supplier' });
  }
});

export default router;
