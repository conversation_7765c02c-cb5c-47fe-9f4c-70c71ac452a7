import express, { Request, Response } from 'express';
import { notificationService } from '../../services/notificationService';
import { authMiddleware } from '../../middleware/auth';
import { UserRole } from '../../db/schema';

const router = express.Router();

// Get notifications
router.get('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { type, status, limit = 10, page = 1 } = req.query;

    const notifications = await notificationService.getNotifications(
      user.id,
      user.storeId,
      type,
      status,
      parseInt(limit),
      parseInt(page)
    );

    res.json(notifications);
  } catch (error) {
    console.error('Error fetching notifications:', error);
    res.status(500).json({ error: 'Failed to fetch notifications' });
  }
});

// Mark notification as read
router.post('/:id/read', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await notificationService.markAsRead(parseInt(id));
    res.json({ message: 'Notification marked as read' });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({ error: 'Failed to mark notification as read' });
  }
});

// Dismiss notification
router.post('/:id/dismiss', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await notificationService.dismissNotification(parseInt(id));
    res.json({ message: 'Notification dismissed' });
  } catch (error) {
    console.error('Error dismissing notification:', error);
    res.status(500).json({ error: 'Failed to dismiss notification' });
  }
});

// Resolve notification (admin/store owner only)
router.post('/:id/resolve', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await notificationService.resolveNotification(parseInt(id), user.id);
    res.json({ message: 'Notification resolved' });
  } catch (error) {
    console.error('Error resolving notification:', error);
    res.status(500).json({ error: 'Failed to resolve notification' });
  }
});

// Get notification count
router.get('/count', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { type } = req.query;

    const notifications = await notificationService.getNotifications(
      user.id,
      user.storeId,
      type,
      undefined,
      1000,
      1
    );

    res.json({ count: notifications.length });
  } catch (error) {
    console.error('Error getting notification count:', error);
    res.status(500).json({ error: 'Failed to get notification count' });
  }
});

export default router;
