import express, { Request, Response } from 'express';
import { db } from '../../db';
import { transfers, stores, products, inventory, UserRole } from '../../db/schema';
import { eq, and, sql, or } from 'drizzle-orm';
import { authMiddleware } from '../../middleware/auth';

const router = express.Router();

// Create transfer request
router.post('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { fromStoreId, toStoreId, productId, quantity } = req.body;

    // Check if user has permission for this store
    if (user.role !== UserRole.ADMIN && user.storeId !== fromStoreId) {
      return res.status(403).json({ error: 'Unauthorized store access' });
    }

    // Check if source store has enough inventory
    const inventoryResult = await db
      .select({ quantity: inventory.quantity })
      .from(inventory)
      .where(
        and(
          eq(inventory.storeId, fromStoreId),
          eq(inventory.productId, productId)
        )
      )
      .limit(1);

    const currentInventory = inventoryResult[0];

    if (!currentInventory || currentInventory.quantity < quantity) {
      return res.status(400).json({ error: 'Insufficient inventory' });
    }

    // Create transfer request
    const transfer = await db.insert(transfers).values({
      fromStoreId,
      toStoreId,
      originStoreId: fromStoreId,
      destinationStoreId: toStoreId,
      createdBy: user.id,
      status: 'pending'
    }).returning().then(r => r[0]);

    res.json(transfer);
  } catch (error) {
    console.error('Create transfer error:', error);
    res.status(500).json({ error: 'Failed to create transfer request' });
  }
});

// Get transfer requests
router.get('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { status, page = 1, limit = 10 } = req.query;

    // Build where conditions first
    const whereConditions = [];

    // Filter by status if provided
    if (status) {
      whereConditions.push(eq(transfers.status, status.toString()));
    }

    // Admin can see all transfers, others can only see their store's transfers
    if (user.role !== UserRole.ADMIN) {
      whereConditions.push(
        or(
          eq(transfers.fromStoreId, user.storeId),
          eq(transfers.toStoreId, user.storeId)
        )
      );
    }

    // Build the complete query with conditions
    const baseQuery = db
      .select({
        id: transfers.id,
        fromStoreId: transfers.fromStoreId,
        toStoreId: transfers.toStoreId,
        status: transfers.status,
        notes: transfers.notes,
        createdBy: transfers.createdBy,
        createdAt: transfers.createdAt
      })
      .from(transfers);

    const finalQuery = whereConditions.length > 0
      ? baseQuery.where(and(...whereConditions))
      : baseQuery;

    const [transfersResult, countResult] = await Promise.all([
      finalQuery
        .limit(Number(limit))
        .offset((Number(page) - 1) * Number(limit)),
      db.select({ count: sql`count(*)` }).from(transfers)
    ]);

    const count = countResult[0];

    res.json({
      transfers: transfersResult,
      total: count.count,
      page: Number(page),
      limit: Number(limit)
    });
  } catch (error) {
    console.error('Get transfers error:', error);
    res.status(500).json({ error: 'Failed to get transfers' });
  }
});

// Approve transfer
router.put('/:id/approve', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { id } = req.params;

    // Only admins or store owners can approve transfers
    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized to approve transfers' });
    }

    const transferResult = await db
      .select()
      .from(transfers)
      .where(eq(transfers.id, parseInt(id)))
      .limit(1);

    const transfer = transferResult[0];

    if (!transfer || transfer.status !== 'pending') {
      return res.status(400).json({ error: 'Invalid transfer request' });
    }

    // Update transfer status (inventory logic removed since transfers table doesn't support product-level transfers)
    const result = await db.update(transfers)
      .set({
        status: 'approved',
        updatedAt: new Date()
      })
      .where(eq(transfers.id, parseInt(id)))
      .returning()
      .then(r => r[0]);

    res.json(result);
  } catch (error) {
    console.error('Approve transfer error:', error);
    res.status(500).json({ error: 'Failed to approve transfer' });
  }
});

// Get transfer history for a product
router.get('/history/:productId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const { storeId } = req.query;

    // Note: transfers table doesn't support product-level transfers, returning empty for now
    const transfersData: any[] = [];

    res.json(transfersData);
  } catch (error) {
    console.error('Get transfer history error:', error);
    res.status(500).json({ error: 'Failed to get transfer history' });
  }
});

export default router;
