import express, { Request, Response } from 'express';
import { db } from '../../db';
import { sales, salesItems, products, stores } from '../../db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { UserRole } from '../../db/schema';
import { authMiddleware } from '../../middleware/auth';
import { generatePDF } from '../../utils/pdf';
import { generateCSV } from '../../utils/csv';

const router = express.Router();

// Generate receipt PDF
router.get('/receipt/:saleId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { saleId } = req.params;
    const { user } = req as any;

    const sale = await db
      .select({
        sale: sales,
        items: {
          product: products,
          quantity: salesItems.quantity,
          unitPrice: salesItems.unitPrice,
          subtotal: salesItems.subtotal
        }
      })
      .from(sales)
      .leftJoin(salesItems, eq(salesItems.saleId, sales.id))
      .leftJoin(products, eq(products.id, salesItems.productId))
      .where(
        and(
          eq(sales.id, parseInt(saleId)),
          eq(sales.storeId, user.storeId)
        )
      )
      .get();

    if (!sale) {
      return res.status(404).json({ error: 'Sale not found' });
    }

    const pdf = await generatePDF({
      title: 'Sales Receipt',
      store: sale.sale.storeId,
      items: sale.items,
      total: sale.sale.totalAmount,
      discount: sale.sale.discountAmount,
      paymentMethod: sale.sale.paymentMethod,
      date: sale.sale.createdAt
    });

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=receipt_${saleId}.pdf`);
    res.send(pdf);
  } catch (error) {
    console.error('Generate receipt error:', error);
    res.status(500).json({ error: 'Failed to generate receipt' });
  }
});

// Get sales summary
router.get('/summary', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { period = 'day', startDate, endDate } = req.query;

    let dateCondition = sql`1=1`;
    if (startDate && endDate) {
      dateCondition = sql`
        ${sales.createdAt} >= ${startDate} AND
        ${sales.createdAt} <= ${endDate}
      `;
    } else {
      const now = new Date();
      switch (period) {
        case 'week':
          dateCondition = sql`
            ${sales.createdAt} >= ${new Date(now.setDate(now.getDate() - 7))} AND
            ${sales.createdAt} <= ${now}
          `;
          break;
        case 'month':
          dateCondition = sql`
            ${sales.createdAt} >= ${new Date(now.setMonth(now.getMonth() - 1))} AND
            ${sales.createdAt} <= ${now}
          `;
          break;
        default:
          dateCondition = sql`
            ${sales.createdAt} >= ${new Date(now.setHours(0, 0, 0, 0))} AND
            ${sales.createdAt} <= ${now}
          `;
      }
    }

    const summary = await db
      .select({
        totalSales: sql<number>`COUNT(*)`,
        totalRevenue: sql<number>`SUM(${sales.totalAmount})`,
        totalDiscounts: sql<number>`SUM(${sales.discountAmount})`,
        averageSale: sql<number>`AVG(${sales.totalAmount})`,
        topProducts: sql<[]>`
          SELECT ${products.name},
            SUM(${salesItems.quantity}) as total_sold,
            SUM(${salesItems.subtotal}) as total_revenue
          FROM ${salesItems}
          JOIN ${products} ON ${products.id} = ${salesItems.productId}
          JOIN ${sales} ON ${sales.id} = ${salesItems.saleId}
          WHERE ${dateCondition}
          GROUP BY ${products.name}
          ORDER BY total_sold DESC
          LIMIT 5
        `
      })
      .from(sales)
      .where(
        and(
          eq(sales.storeId, user.storeId),
          dateCondition
        )
      )
      .get();

    res.json(summary);
  } catch (error) {
    console.error('Get sales summary error:', error);
    res.status(500).json({ error: 'Failed to get sales summary' });
  }
});

// Export sales to CSV
router.get('/export', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { startDate, endDate } = req.query;

    const sales = await db
      .select({
        sale: sales,
        items: {
          product: products,
          quantity: salesItems.quantity,
          unitPrice: salesItems.unitPrice,
          subtotal: salesItems.subtotal
        }
      })
      .from(sales)
      .leftJoin(salesItems, eq(salesItems.saleId, sales.id))
      .leftJoin(products, eq(products.id, salesItems.productId))
      .where(eq(sales.storeId, user.storeId))
      .get();

    const csv = generateCSV(sales, [
      'id',
      'createdAt',
      'totalAmount',
      'discountAmount',
      'paymentMethod',
      'items.product.name',
      'items.quantity',
      'items.unitPrice',
      'items.subtotal'
    ]);

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=sales_report.csv');
    res.send(csv);
  } catch (error) {
    console.error('Export sales error:', error);
    res.status(500).json({ error: 'Failed to export sales' });
  }
});

export default router;
