import express, { Request, Response } from 'express';
import { db } from '../../db';
import { sales, salesItems, products, stores } from '../../db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { UserRole } from '../../db/schema';
import { authMiddleware } from '../../middleware/auth';
import { generatePDF } from '../../utils/pdf';
import { generateCSV } from '../../utils/csv';

const router = express.Router();

// Generate receipt PDF
router.get('/receipt/:saleId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { saleId } = req.params;
    const { user } = req as any;

    const saleResult = await db
      .select({
        id: sales.id,
        storeId: sales.storeId,
        totalAmount: sales.totalAmount,
        paymentMethod: sales.paymentMethod,
        createdAt: sales.createdAt,
        productName: products.name,
        quantity: salesItems.quantity,
        unitPrice: salesItems.unitPrice,
        subtotal: salesItems.subtotal
      })
      .from(sales)
      .leftJoin(salesItems, eq(salesItems.saleId, sales.id))
      .leftJoin(products, eq(products.id, salesItems.productId))
      .where(
        and(
          eq(sales.id, parseInt(saleId)),
          eq(sales.storeId, user.storeId)
        )
      )
      .limit(1);

    const sale = saleResult[0];

    if (!sale) {
      return res.status(404).json({ error: 'Sale not found' });
    }

    // Group items by sale (since we have flattened data)
    const items = saleResult.map(row => ({
      product: {
        name: row.productName || 'Unknown Product',
        sku: 'N/A',
        retailPrice: Number(row.unitPrice) || 0
      },
      quantity: row.quantity || 0,
      unitPrice: Number(row.unitPrice) || 0,
      subtotal: Number(row.subtotal) || 0
    }));

    const pdf = await generatePDF({
      title: 'Sales Receipt',
      store: sale.storeId || 0,
      items: items,
      total: Number(sale.totalAmount) || 0,
      discount: 0, // No discount field in schema
      paymentMethod: sale.paymentMethod || 'cash',
      date: sale.createdAt || new Date()
    });

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=receipt_${saleId}.pdf`);
    res.send(pdf);
  } catch (error) {
    console.error('Generate receipt error:', error);
    res.status(500).json({ error: 'Failed to generate receipt' });
  }
});

// Get sales summary
router.get('/summary', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { period = 'day', startDate, endDate } = req.query;

    let dateCondition = sql`1=1`;
    if (startDate && endDate) {
      dateCondition = sql`
        ${sales.createdAt} >= ${startDate} AND
        ${sales.createdAt} <= ${endDate}
      `;
    } else {
      const now = new Date();
      switch (period) {
        case 'week':
          dateCondition = sql`
            ${sales.createdAt} >= ${new Date(now.setDate(now.getDate() - 7))} AND
            ${sales.createdAt} <= ${now}
          `;
          break;
        case 'month':
          dateCondition = sql`
            ${sales.createdAt} >= ${new Date(now.setMonth(now.getMonth() - 1))} AND
            ${sales.createdAt} <= ${now}
          `;
          break;
        default:
          dateCondition = sql`
            ${sales.createdAt} >= ${new Date(now.setHours(0, 0, 0, 0))} AND
            ${sales.createdAt} <= ${now}
          `;
      }
    }

    const summaryResult = await db
      .select({
        totalSales: sql<number>`COUNT(*)`,
        totalRevenue: sql<number>`SUM(${sales.totalAmount})`,
        totalDiscounts: sql<number>`0`, // No discount field in schema
        averageSale: sql<number>`AVG(${sales.totalAmount})`,
        topProducts: sql<[]>`
          SELECT ${products.name},
            SUM(${salesItems.quantity}) as total_sold,
            SUM(${salesItems.subtotal}) as total_revenue
          FROM ${salesItems}
          JOIN ${products} ON ${products.id} = ${salesItems.productId}
          JOIN ${sales} ON ${sales.id} = ${salesItems.saleId}
          WHERE ${dateCondition}
          GROUP BY ${products.name}
          ORDER BY total_sold DESC
          LIMIT 5
        `
      })
      .from(sales)
      .where(
        and(
          eq(sales.storeId, user.storeId),
          dateCondition
        )
      )
      .limit(1);

    const summary = summaryResult[0];

    res.json(summary);
  } catch (error) {
    console.error('Get sales summary error:', error);
    res.status(500).json({ error: 'Failed to get sales summary' });
  }
});

// Export sales to CSV
router.get('/export', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { startDate, endDate } = req.query;

    const salesData = await db
      .select({
        id: sales.id,
        createdAt: sales.createdAt,
        totalAmount: sales.totalAmount,
        paymentMethod: sales.paymentMethod,
        productName: products.name,
        quantity: salesItems.quantity,
        unitPrice: salesItems.unitPrice,
        subtotal: salesItems.subtotal
      })
      .from(sales)
      .leftJoin(salesItems, eq(salesItems.saleId, sales.id))
      .leftJoin(products, eq(products.id, salesItems.productId))
      .where(eq(sales.storeId, user.storeId));

    const csv = generateCSV(salesData, [
      'id',
      'createdAt',
      'totalAmount',
      'paymentMethod',
      'productName',
      'quantity',
      'unitPrice',
      'subtotal'
    ]);

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=sales_report.csv');
    res.send(csv);
  } catch (error) {
    console.error('Export sales error:', error);
    res.status(500).json({ error: 'Failed to export sales' });
  }
});

export default router;
