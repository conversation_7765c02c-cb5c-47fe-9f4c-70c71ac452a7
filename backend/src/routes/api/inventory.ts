import express, { Request, Response } from 'express';
import { db } from '../../db';
import { inventory, products, stores } from '../../db/schema';
import { eq, and, sql } from 'drizzle-orm';

const router = express.Router();

// Get inventory for all stores
router.get('/', async (req: Request, res: Response) => {
  try {
    const inventoryItems = await db
      .select({
        product: products,
        store: stores,
        inventory: inventory
      })
      .from(inventory)
      .innerJoin(products, eq(products.id, inventory.productId))
      .innerJoin(stores, eq(stores.id, inventory.storeId))
      .where(eq(products.isActive, true));

    res.json(inventoryItems);
  } catch (error) {
    console.error('Error fetching inventory:', error);
    res.status(500).json({ error: 'Failed to fetch inventory' });
  }
});

// Get inventory for specific store
router.get('/store/:storeId', async (req: Request, res: Response) => {
  const { storeId } = req.params;
  
  try {
    const inventoryItems = await db
      .select({
        product: products,
        store: stores,
        inventory: inventory
      })
      .from(inventory)
      .innerJoin(products, eq(products.id, inventory.productId))
      .innerJoin(stores, eq(stores.id, inventory.storeId))
      .where(and(
        eq(inventory.storeId, parseInt(storeId)),
        eq(products.isActive, true)
      ))
      .all();

    res.json(inventoryItems);
  } catch (error) {
    console.error('Error fetching store inventory:', error);
    res.status(500).json({ error: 'Failed to fetch store inventory' });
  }
});

// Get inventory for specific product
router.get('/product/:productId', async (req: Request, res: Response) => {
  const { productId } = req.params;
  
  try {
    const inventoryItems = await db
      .select({
        product: products,
        store: stores,
        inventory: inventory
      })
      .from(inventory)
      .innerJoin(products, eq(products.id, inventory.productId))
      .innerJoin(stores, eq(stores.id, inventory.storeId))
      .where(and(
        eq(inventory.productId, parseInt(productId)),
        eq(products.isActive, true)
      ))
      .all();

    res.json(inventoryItems);
  } catch (error) {
    console.error('Error fetching product inventory:', error);
    res.status(500).json({ error: 'Failed to fetch product inventory' });
  }
});

// Update inventory quantity
router.put('/:id', async (req: Request, res: Response) => {
  const { id } = req.params;
  const { quantity, note } = req.body;
  
  try {
    const inventoryItem = await db
      .update(inventory)
      .set({
        quantity,
        lastUpdated: new Date(),
        history: sql`array_append(history, json_build_object(
          'type', 'adjustment',
          'quantity', ${quantity},
          'date', now(),
          'note', ${note || null}
        ))::jsonb[]`
      })
      .where(eq(inventory.id, parseInt(id)))
      .returning()
      .get();

    if (!inventoryItem) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }

    res.json(inventoryItem);
  } catch (error) {
    console.error('Error updating inventory:', error);
    res.status(500).json({ error: 'Failed to update inventory' });
  }
});

// Get low stock alerts
router.get('/low-stock', async (req: Request, res: Response) => {
  try {
    const lowStockItems = await db
      .select({
        product: products,
        store: stores,
        inventory: inventory
      })
      .from(inventory)
      .innerJoin(products, eq(products.id, inventory.productId))
      .innerJoin(stores, eq(stores.id, inventory.storeId))
      .where(and(
        eq(products.isActive, true),
        sql`inventory.quantity < inventory.threshold`
      ))
      .all();

    res.json(lowStockItems);
  } catch (error) {
    console.error('Error fetching low stock items:', error);
    res.status(500).json({ error: 'Failed to fetch low stock items' });
  }
});

export default router;
