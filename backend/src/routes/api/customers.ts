import express, { Request, Response } from 'express';
import { customerService } from '../../services/customerService';
import { authMiddleware } from '../../middleware/auth';
import { UserRole } from '../../db/schema';

const router = express.Router();

// Get customer profile
router.get('/profile/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const profile = await customerService.getCustomerProfile(parseInt(id));
    res.json(profile);
  } catch (error) {
    console.error('Error getting customer profile:', error);
    res.status(500).json({ error: 'Failed to get customer profile' });
  }
});

// Get customer purchase history
router.get('/history/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const history = await customerService.getCustomerHistory(parseInt(id));
    res.json(history);
  } catch (error) {
    console.error('Error getting customer history:', error);
    res.status(500).json({ error: 'Failed to get customer history' });
  }
});

// Update customer profile
router.put('/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { user } = req as any;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await customerService.updateCustomer(parseInt(id), req.body);
    res.json({ message: 'Customer profile updated successfully' });
  } catch (error) {
    console.error('Error updating customer:', error);
    res.status(500).json({ error: 'Failed to update customer' });
  }
});

// Get customer loyalty discount
router.get('/:id/loyalty-discount', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const discount = await customerService.getLoyaltyDiscount(parseInt(id));
    res.json({ discount });
  } catch (error) {
    console.error('Error getting loyalty discount:', error);
    res.status(500).json({ error: 'Failed to get loyalty discount' });
  }
});

// Get customer type discount
router.get('/:id/type-discount', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const discount = await customerService.getCustomerTypeDiscount(parseInt(id));
    res.json({ discount });
  } catch (error) {
    console.error('Error getting customer type discount:', error);
    res.status(500).json({ error: 'Failed to get customer type discount' });
  }
});

// Process customer purchase
router.post('/:id/purchase', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { saleId, amount } = req.body;

    await customerService.processPurchase(parseInt(id), parseInt(saleId), amount);
    res.json({ message: 'Purchase processed successfully' });
  } catch (error) {
    console.error('Error processing purchase:', error);
    res.status(500).json({ error: 'Failed to process purchase' });
  }
});

export default router;
