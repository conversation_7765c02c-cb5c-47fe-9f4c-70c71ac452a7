import express, { Request, Response } from 'express';
import { db } from '../../db';
import { users, stores } from '../../db/schema';
import { eq, and } from 'drizzle-orm';
import { UserRole } from '../../db/schema';

const router = express.Router();

// Get all users (only admins can see all users)
router.get('/', async (req: Request, res: Response) => {
  try {
    const auth = req.headers.authorization;
    if (!auth) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // In a real app, you would parse the token and get user info
    // For now, we'll just check if the user is an admin
    const isAdmin = auth === 'admin';

    const usersList = await db
      .select({
        user: users,
        store: stores
      })
      .from(users)
      .leftJoin(stores, eq(stores.id, users.storeId))
      .where(eq(users.isActive, true));

    res.json(usersList);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Get user by ID
router.get('/:id', async (req: Request, res: Response) => {
  const { id } = req.params;
  
  try {
    const userResult = await db
      .select({
        user: users,
        store: stores
      })
      .from(users)
      .leftJoin(stores, eq(stores.id, users.storeId))
      .where(eq(users.id, parseInt(id)))
      .limit(1);

    const user = userResult[0];

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ error: 'Failed to fetch user' });
  }
});

// Create new user
router.post('/', async (req: Request, res: Response) => {
  try {
    const { email, password, firstName, lastName, role, storeId } = req.body;

    const user = await db
      .insert(users)
      .values({
        email,
        password, // In a real app, you would hash the password
        firstName,
        lastName,
        role,
        storeId: storeId ? parseInt(storeId) : null
      })
      .returning()
      .get();

    res.status(201).json(user);
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
});

// Update user
router.put('/:id', async (req: Request, res: Response) => {
  const { id } = req.params;
  
  try {
    const { email, firstName, lastName, role, storeId } = req.body;

    const user = await db
      .update(users)
      .set({
        email,
        firstName,
        lastName,
        role,
        storeId: storeId ? parseInt(storeId) : null,
        updatedAt: new Date()
      })
      .where(eq(users.id, parseInt(id)))
      .returning()
      .get();

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Delete user (soft delete)
router.delete('/:id', async (req: Request, res: Response) => {
  const { id } = req.params;
  
  try {
    const userResult = await db
      .update(users)
      .set({
        isActive: false,
        updatedAt: new Date()
      })
      .where(eq(users.id, parseInt(id)))
      .returning();

    const user = userResult[0];

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ error: 'Failed to delete user' });
  }
});

export default router;
