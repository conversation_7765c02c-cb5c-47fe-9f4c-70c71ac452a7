import express, { Request, Response } from 'express';
import { backupService } from '../services/backupService';
import { authMiddleware } from '../middleware/auth';
import { UserRole } from '../db/schema';
import { readFileSync } from 'fs';
import { join } from 'path';
import { exec } from 'child_process';

const router = express.Router();

// Backup Routes
router.post('/backup', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    if (user.role !== UserRole.ADMIN) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const backupFile = await backupService.performBackup();
    res.json({ message: 'Backup successful', backupFile });
  } catch (error) {
    console.error('Error performing backup:', error);
    res.status(500).json({ error: 'Failed to perform backup' });
  }
});

router.post('/restore', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { backupFile } = req.body;

    if (user.role !== UserRole.ADMIN) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await backupService.restoreBackup(backupFile);
    res.json({ message: 'Restore successful' });
  } catch (error) {
    console.error('Error restoring backup:', error);
    res.status(500).json({ error: 'Failed to restore backup' });
  }
});

router.get('/backups', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    if (user.role !== UserRole.ADMIN) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const backupPath = backupService.config.backupPath;
    const backups = [];

    // Get list of backup files
    const files = await new Promise<string[]>((resolve) => {
      exec(`find ${backupPath} -type f -name "backup-*.sql"`, (error, stdout) => {
        if (error) {
          resolve([]);
          return;
        }
        resolve(stdout.trim().split('\n'));
      });
    });

    // Parse backup information
    for (const file of files) {
      const filename = file.split('/').pop();
      const timestamp = filename?.replace('backup-', '').replace('.sql', '');
      if (timestamp) {
        backups.push({
          filename,
          path: file,
          timestamp: new Date(timestamp.replace(/-/g, 'T')).toISOString(),
          size: readFileSync(file).byteLength
        });
      }
    }

    res.json(backups);
  } catch (error) {
    console.error('Error listing backups:', error);
    res.status(500).json({ error: 'Failed to list backups' });
  }
});

// Export Routes
router.get('/export/inventory/:storeId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { storeId } = req.params;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const excelData = await backupService.exportInventoryToExcel(parseInt(storeId));
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=inventory_export.xlsx');
    res.send(excelData);
  } catch (error) {
    console.error('Error exporting inventory:', error);
    res.status(500).json({ error: 'Failed to export inventory' });
  }
});

router.get('/export/sales/:storeId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { storeId } = req.params;
    const { startDate, endDate } = req.query;

    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const excelData = await backupService.exportSalesToExcel(
      parseInt(storeId),
      startDate as string,
      endDate as string
    );

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=sales_export.xlsx');
    res.send(excelData);
  } catch (error) {
    console.error('Error exporting sales:', error);
    res.status(500).json({ error: 'Failed to export sales' });
  }
});

export default router;
