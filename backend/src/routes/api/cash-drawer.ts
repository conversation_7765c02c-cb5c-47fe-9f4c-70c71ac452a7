import express, { Request, Response } from 'express';
import { cashDrawerService } from '../../services/cashDrawerService';
import { authMiddleware } from '../../middleware/auth';
import { UserRole } from '../../db/schema';

const router = express.Router();

// Open Cash Drawer
router.post('/open', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    if (user.role !== UserRole.SALES_REP && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const drawerId = await cashDrawerService.openCashDrawer(req.body, user.id);
    res.json({ id: drawerId });
  } catch (error) {
    console.error('Error opening cash drawer:', error);
    res.status(500).json({ error: 'Failed to open cash drawer' });
  }
});

// Record Transaction
router.post('/transaction', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    if (user.role !== UserRole.SALES_REP && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const transactionId = await cashDrawerService.recordTransaction(req.body, user.id);
    res.json({ id: transactionId });
  } catch (error) {
    console.error('Error recording transaction:', error);
    res.status(500).json({ error: 'Failed to record transaction' });
  }
});

// Close Cash Drawer
router.post('/close/:drawerId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { drawerId } = req.params;

    if (user.role !== UserRole.SALES_REP && user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    await cashDrawerService.closeCashDrawer(parseInt(drawerId), user.id);
    res.json({ message: 'Cash drawer closed successfully' });
  } catch (error) {
    console.error('Error closing cash drawer:', error);
    res.status(500).json({ error: 'Failed to close cash drawer' });
  }
});

// Reconcile Cash Drawer
router.post('/reconcile/:drawerId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { drawerId } = req.params;

    if (user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const reconciliationId = await cashDrawerService.reconcileCashDrawer(
      { ...req.body, drawerId: parseInt(drawerId) },
      user.id
    );

    res.json({ id: reconciliationId });
  } catch (error) {
    console.error('Error reconciling cash drawer:', error);
    res.status(500).json({ error: 'Failed to reconcile cash drawer' });
  }
});

// Get Daily Summary
router.get('/summary/:storeId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { user } = req as any;
    const { storeId } = req.params;
    const { date } = req.query;

    if (user.role !== UserRole.STORE_OWNER) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const summary = await cashDrawerService.getDailySummary(
      parseInt(storeId),
      new Date(date as string)
    );

    res.json(summary);
  } catch (error) {
    console.error('Error getting daily summary:', error);
    res.status(500).json({ error: 'Failed to get daily summary' });
  }
});

// Get Transaction History
router.get('/history/:drawerId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { drawerId } = req.params;
    const { startDate, endDate, limit = 10, page = 1 } = req.query;

    const history = await cashDrawerService.getTransactionHistory(
      parseInt(drawerId),
      startDate as string,
      endDate as string,
      parseInt(limit as string),
      parseInt(page as string)
    );

    res.json(history);
  } catch (error) {
    console.error('Error getting transaction history:', error);
    res.status(500).json({ error: 'Failed to get transaction history' });
  }
});

export default router;
