import express, { Request, Response } from 'express';
import { db } from '../db';
import { inventory, products, stores } from '../db/schema';
import { eq, and, lt, sql } from 'drizzle-orm';

interface InventoryItem {
  productId: number;
  productName: string;
  storeId: number;
  storeName: string;
  quantity: number;
  threshold: number;
  lastUpdated: Date;
}

const router = express.Router();

// Get inventory for all stores
router.get('/', async (req: Request, res: Response) => {
  try {
    const inventoryData = await db
      .select({
        productId: inventory.productId,
        productName: products.name,
        storeId: inventory.storeId,
        storeName: stores.name,
        quantity: inventory.quantity,
        threshold: inventory.threshold,
        lastUpdated: inventory.lastUpdated
      })
      .from(inventory)
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(stores, eq(inventory.storeId, stores.id));

    res.json(inventoryData as InventoryItem[]);
  } catch (error) {
    console.error('Error fetching inventory:', error);
    res.status(500).json({ error: 'Failed to fetch inventory' });
  }
});

// Get inventory for specific store
router.get('/store/:storeId', async (req: Request, res: Response) => {
  try {
    const storeId = Number(req.params.storeId);
    if (isNaN(storeId)) {
      return res.status(400).json({ error: 'Invalid store ID' });
    }

    const inventoryData = await db
      .select({
        productId: inventory.productId,
        productName: products.name,
        quantity: inventory.quantity,
        threshold: inventory.threshold,
        lastUpdated: inventory.lastUpdated
      })
      .from(inventory)
      .leftJoin(products, eq(inventory.productId, products.id))
      .where(eq(inventory.storeId, storeId));

    res.json(inventoryData);
  } catch (error) {
    console.error('Error fetching store inventory:', error);
    res.status(500).json({ error: 'Failed to fetch store inventory' });
  }
});

// Get inventory for specific product
router.get('/product/:productId', async (req: Request, res: Response) => {
  try {
    const productId = Number(req.params.productId);
    if (isNaN(productId)) {
      return res.status(400).json({ error: 'Invalid product ID' });
    }

    const inventoryData = await db
      .select({
        storeId: inventory.storeId,
        storeName: stores.name,
        quantity: inventory.quantity,
        threshold: inventory.threshold,
        lastUpdated: inventory.lastUpdated
      })
      .from(inventory)
      .leftJoin(stores, eq(inventory.storeId, stores.id))
      .where(eq(inventory.productId, productId));

    res.json(inventoryData);
  } catch (error) {
    console.error('Error fetching product inventory:', error);
    res.status(500).json({ error: 'Failed to fetch product inventory' });
  }
});

// Update inventory
router.put('/:productId/:storeId', async (req: Request, res: Response) => {
  try {
    const productId = Number(req.params.productId);
    const storeId = Number(req.params.storeId);
    if (isNaN(productId) || isNaN(storeId)) {
      return res.status(400).json({ error: 'Invalid product or store ID' });
    }

    const { quantity, threshold, note } = req.body;
    
    if (quantity === undefined || threshold === undefined) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const result = await db
      .update(inventory)
      .set({
        quantity,
        threshold,
        lastUpdated: new Date()
      })
      .where(and(
        eq(inventory.productId, productId),
        eq(inventory.storeId, storeId)
      ))
      .returning();
    
    if (!result[0]) {
      return res.status(404).json({ error: 'Inventory record not found' });
    }
    
    res.json(result[0]);
  } catch (error) {
    console.error('Error updating inventory:', error);
    res.status(500).json({ error: 'Failed to update inventory' });
  }
});

// Get low stock alerts
router.get('/alerts', async (req: Request, res: Response) => {
  try {
    const lowStock = await db
      .select({
        productId: inventory.productId,
        productName: products.name,
        storeId: inventory.storeId,
        storeName: stores.name,
        quantity: inventory.quantity,
        threshold: inventory.threshold,
        lastUpdated: inventory.lastUpdated
      })
      .from(inventory)
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(stores, eq(inventory.storeId, stores.id))
      .where(lt(inventory.quantity, inventory.threshold));

    res.json(lowStock);
  } catch (error) {
    console.error('Error fetching low stock alerts:', error);
    res.status(500).json({ error: 'Failed to fetch low stock alerts' });
  }
});

export default router;
