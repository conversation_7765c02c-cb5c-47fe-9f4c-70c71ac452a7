{"name": "best-gadget-backend", "version": "1.0.0", "description": "Backend for Best Gadget Management System", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "test": "jest"}, "keywords": ["inventory", "sales", "management", "retail"], "author": "", "license": "ISC", "dependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.23", "@types/express-session": "^1.17.6", "@types/jsonwebtoken": "^9.0.10", "@types/passport": "^1.0.15", "@types/passport-local": "^1.0.37", "@types/pg": "^8.10.1", "@types/uuid": "^9.0.7", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parse": "^5.5.0", "csv-stringify": "^6.4.0", "dotenv": "^16.0.3", "drizzle-orm": "^0.29.5", "express": "^4.21.2", "express-session": "^1.17.3", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-local": "^1.0.0", "pdf-lib": "^1.17.1", "pg": "^8.16.2", "ts-node-dev": "^2.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/jest": "^29.5.11", "@types/node": "^20.11.0", "@types/supertest": "^6.0.0", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}