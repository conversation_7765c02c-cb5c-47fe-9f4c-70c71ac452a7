[{"/Users/<USER>/CascadeProjects/BestGadget/frontend/src/index.tsx": "1", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/App.tsx": "2", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/Dashboard.tsx": "3", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/auth/Register.tsx": "4", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/layouts/AuthLayout.tsx": "5", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/auth/Login.tsx": "6", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/layouts/DashboardLayout.tsx": "7", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/auth/Unauthorized.tsx": "8", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/stores/Stores.tsx": "9", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/products/Products.tsx": "10", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/inventory/Inventory.tsx": "11", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/sales/Sales.tsx": "12", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/customers/Customers.tsx": "13", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/customers/CustomerProfile.tsx": "14", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/suppliers/Suppliers.tsx": "15", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/reports/SalesReports.tsx": "16", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/pos/POS.tsx": "17", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/transfers/Transfers.tsx": "18", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/credit-sales/CreditSales.tsx": "19", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/credit-sales/OverdueCustomers.tsx": "20", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/users/Users.tsx": "21", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/dashboard/StoreOwnerDashboard.tsx": "22", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/dashboard/AdminDashboard.tsx": "23", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/dashboard/SalesRepDashboard.tsx": "24", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/purchase-orders/PurchaseOrders.tsx": "25", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/users/UserForm.tsx": "26", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/auth/ProtectedRoute.tsx": "27", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/notifications/NotificationBell.tsx": "28", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/contexts/AuthContext.tsx": "29", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/products/ProductForm.tsx": "30", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/stores/StoreTable.tsx": "31", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/products/ProductTable.tsx": "32", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/stores/StoreForm.tsx": "33", "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/users/UserTable.tsx": "34"}, {"size": 273, "mtime": 1750951160890, "results": "35", "hashOfConfig": "36"}, {"size": 6570, "mtime": 1751576502808, "results": "37", "hashOfConfig": "36"}, {"size": 3685, "mtime": 1750865461706, "results": "38", "hashOfConfig": "36"}, {"size": 2235, "mtime": 1750951202403, "results": "39", "hashOfConfig": "36"}, {"size": 695, "mtime": 1750951274840, "results": "40", "hashOfConfig": "36"}, {"size": 3843, "mtime": 1751621738359, "results": "41", "hashOfConfig": "36"}, {"size": 5140, "mtime": 1751621716928, "results": "42", "hashOfConfig": "36"}, {"size": 1081, "mtime": 1750867275944, "results": "43", "hashOfConfig": "36"}, {"size": 2102, "mtime": 1751272536482, "results": "44", "hashOfConfig": "36"}, {"size": 2164, "mtime": 1751273520095, "results": "45", "hashOfConfig": "36"}, {"size": 1562, "mtime": 1750951215065, "results": "46", "hashOfConfig": "36"}, {"size": 1555, "mtime": 1750951227628, "results": "47", "hashOfConfig": "36"}, {"size": 1587, "mtime": 1750951244813, "results": "48", "hashOfConfig": "36"}, {"size": 9233, "mtime": 1751620305229, "results": "49", "hashOfConfig": "36"}, {"size": 8496, "mtime": 1751272071041, "results": "50", "hashOfConfig": "36"}, {"size": 6006, "mtime": 1751272493381, "results": "51", "hashOfConfig": "36"}, {"size": 7046, "mtime": 1751576841183, "results": "52", "hashOfConfig": "36"}, {"size": 6263, "mtime": 1751271845359, "results": "53", "hashOfConfig": "36"}, {"size": 6051, "mtime": 1751620526551, "results": "54", "hashOfConfig": "36"}, {"size": 7561, "mtime": 1751620457588, "results": "55", "hashOfConfig": "36"}, {"size": 2116, "mtime": 1751271762973, "results": "56", "hashOfConfig": "36"}, {"size": 5320, "mtime": 1751620214771, "results": "57", "hashOfConfig": "36"}, {"size": 5946, "mtime": 1751620244666, "results": "58", "hashOfConfig": "36"}, {"size": 5246, "mtime": 1751620193860, "results": "59", "hashOfConfig": "36"}, {"size": 13400, "mtime": 1751273370538, "results": "60", "hashOfConfig": "36"}, {"size": 7108, "mtime": 1751636469657, "results": "61", "hashOfConfig": "36"}, {"size": 970, "mtime": 1750867262957, "results": "62", "hashOfConfig": "36"}, {"size": 5250, "mtime": 1751621786215, "results": "63", "hashOfConfig": "36"}, {"size": 2959, "mtime": 1750867212420, "results": "64", "hashOfConfig": "36"}, {"size": 7752, "mtime": 1751621909560, "results": "65", "hashOfConfig": "36"}, {"size": 3923, "mtime": 1750866995024, "results": "66", "hashOfConfig": "36"}, {"size": 6383, "mtime": 1750866229676, "results": "67", "hashOfConfig": "36"}, {"size": 7907, "mtime": 1751643879315, "results": "68", "hashOfConfig": "36"}, {"size": 4042, "mtime": 1751271628065, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1s2halv", {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/index.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/App.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/auth/Register.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/layouts/AuthLayout.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/auth/Login.tsx", ["172", "173"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/layouts/DashboardLayout.tsx", ["174"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/auth/Unauthorized.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/stores/Stores.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/products/Products.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/inventory/Inventory.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/sales/Sales.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/customers/Customers.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/customers/CustomerProfile.tsx", ["175", "176", "177"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/suppliers/Suppliers.tsx", ["178", "179"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/reports/SalesReports.tsx", ["180", "181", "182", "183"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/pos/POS.tsx", ["184"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/transfers/Transfers.tsx", ["185", "186"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/credit-sales/CreditSales.tsx", ["187", "188", "189", "190"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/credit-sales/OverdueCustomers.tsx", ["191", "192"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/users/Users.tsx", ["193"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/dashboard/StoreOwnerDashboard.tsx", ["194", "195"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/dashboard/AdminDashboard.tsx", ["196", "197", "198"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/dashboard/SalesRepDashboard.tsx", ["199", "200", "201", "202", "203", "204"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/pages/purchase-orders/PurchaseOrders.tsx", ["205"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/users/UserForm.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/auth/ProtectedRoute.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/notifications/NotificationBell.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/products/ProductForm.tsx", ["206"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/stores/StoreTable.tsx", ["207"], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/products/ProductTable.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/stores/StoreForm.tsx", [], [], "/Users/<USER>/CascadeProjects/BestGadget/frontend/src/components/users/UserTable.tsx", ["208", "209"], [], {"ruleId": "210", "severity": 1, "message": "211", "line": 1, "column": 10, "nodeType": "212", "messageId": "213", "endLine": 1, "endColumn": 18}, {"ruleId": "210", "severity": 1, "message": "214", "line": 9, "column": 18, "nodeType": "212", "messageId": "213", "endLine": 9, "endColumn": 23}, {"ruleId": "210", "severity": 1, "message": "215", "line": 5, "column": 10, "nodeType": "212", "messageId": "213", "endLine": 5, "endColumn": 18}, {"ruleId": "210", "severity": 1, "message": "216", "line": 4, "column": 10, "nodeType": "212", "messageId": "213", "endLine": 4, "endColumn": 18}, {"ruleId": "217", "severity": 1, "message": "218", "line": 30, "column": 7, "nodeType": "212", "messageId": "219", "endLine": 30, "endColumn": 32}, {"ruleId": "210", "severity": 1, "message": "220", "line": 31, "column": 11, "nodeType": "212", "messageId": "213", "endLine": 31, "endColumn": 15}, {"ruleId": "210", "severity": 1, "message": "216", "line": 4, "column": 10, "nodeType": "212", "messageId": "213", "endLine": 4, "endColumn": 18}, {"ruleId": "210", "severity": 1, "message": "220", "line": 18, "column": 11, "nodeType": "212", "messageId": "213", "endLine": 18, "endColumn": 15}, {"ruleId": "210", "severity": 1, "message": "216", "line": 4, "column": 10, "nodeType": "212", "messageId": "213", "endLine": 4, "endColumn": 18}, {"ruleId": "210", "severity": 1, "message": "220", "line": 18, "column": 11, "nodeType": "212", "messageId": "213", "endLine": 18, "endColumn": 15}, {"ruleId": "221", "severity": 1, "message": "222", "line": 28, "column": 6, "nodeType": "223", "endLine": 28, "endColumn": 34, "suggestions": "224"}, {"ruleId": "210", "severity": 1, "message": "225", "line": 73, "column": 9, "nodeType": "212", "messageId": "213", "endLine": 73, "endColumn": 27}, {"ruleId": "210", "severity": 1, "message": "226", "line": 21, "column": 23, "nodeType": "212", "messageId": "213", "endLine": 21, "endColumn": 37}, {"ruleId": "210", "severity": 1, "message": "227", "line": 13, "column": 17, "nodeType": "212", "messageId": "213", "endLine": 13, "endColumn": 25}, {"ruleId": "221", "severity": 1, "message": "228", "line": 18, "column": 6, "nodeType": "223", "endLine": 18, "endColumn": 33, "suggestions": "229"}, {"ruleId": "210", "severity": 1, "message": "216", "line": 4, "column": 10, "nodeType": "212", "messageId": "213", "endLine": 4, "endColumn": 18}, {"ruleId": "210", "severity": 1, "message": "220", "line": 7, "column": 11, "nodeType": "212", "messageId": "213", "endLine": 7, "endColumn": 15}, {"ruleId": "210", "severity": 1, "message": "227", "line": 13, "column": 17, "nodeType": "212", "messageId": "213", "endLine": 13, "endColumn": 25}, {"ruleId": "221", "severity": 1, "message": "230", "line": 18, "column": 6, "nodeType": "223", "endLine": 18, "endColumn": 33, "suggestions": "231"}, {"ruleId": "210", "severity": 1, "message": "216", "line": 4, "column": 10, "nodeType": "212", "messageId": "213", "endLine": 4, "endColumn": 18}, {"ruleId": "210", "severity": 1, "message": "220", "line": 21, "column": 11, "nodeType": "212", "messageId": "213", "endLine": 21, "endColumn": 15}, {"ruleId": "210", "severity": 1, "message": "216", "line": 5, "column": 38, "nodeType": "212", "messageId": "213", "endLine": 5, "endColumn": 46}, {"ruleId": "210", "severity": 1, "message": "216", "line": 4, "column": 10, "nodeType": "212", "messageId": "213", "endLine": 4, "endColumn": 18}, {"ruleId": "221", "severity": 1, "message": "232", "line": 44, "column": 6, "nodeType": "223", "endLine": 44, "endColumn": 8, "suggestions": "233"}, {"ruleId": "210", "severity": 1, "message": "216", "line": 4, "column": 10, "nodeType": "212", "messageId": "213", "endLine": 4, "endColumn": 18}, {"ruleId": "210", "severity": 1, "message": "234", "line": 12, "column": 3, "nodeType": "212", "messageId": "213", "endLine": 12, "endColumn": 9}, {"ruleId": "210", "severity": 1, "message": "220", "line": 39, "column": 11, "nodeType": "212", "messageId": "213", "endLine": 39, "endColumn": 15}, {"ruleId": "210", "severity": 1, "message": "216", "line": 4, "column": 10, "nodeType": "212", "messageId": "213", "endLine": 4, "endColumn": 18}, {"ruleId": "210", "severity": 1, "message": "235", "line": 14, "column": 3, "nodeType": "212", "messageId": "213", "endLine": 14, "endColumn": 11}, {"ruleId": "210", "severity": 1, "message": "236", "line": 15, "column": 3, "nodeType": "212", "messageId": "213", "endLine": 15, "endColumn": 6}, {"ruleId": "210", "severity": 1, "message": "237", "line": 16, "column": 3, "nodeType": "212", "messageId": "213", "endLine": 16, "endColumn": 7}, {"ruleId": "210", "severity": 1, "message": "238", "line": 19, "column": 7, "nodeType": "212", "messageId": "213", "endLine": 19, "endColumn": 13}, {"ruleId": "221", "severity": 1, "message": "232", "line": 44, "column": 6, "nodeType": "223", "endLine": 44, "endColumn": 8, "suggestions": "239"}, {"ruleId": "210", "severity": 1, "message": "216", "line": 4, "column": 10, "nodeType": "212", "messageId": "213", "endLine": 4, "endColumn": 18}, {"ruleId": "210", "severity": 1, "message": "240", "line": 3, "column": 8, "nodeType": "212", "messageId": "213", "endLine": 3, "endColumn": 13}, {"ruleId": "210", "severity": 1, "message": "241", "line": 2, "column": 33, "nodeType": "212", "messageId": "213", "endLine": 2, "endColumn": 40}, {"ruleId": "210", "severity": 1, "message": "241", "line": 2, "column": 33, "nodeType": "212", "messageId": "213", "endLine": 2, "endColumn": 40}, {"ruleId": "210", "severity": 1, "message": "216", "line": 3, "column": 38, "nodeType": "212", "messageId": "213", "endLine": 3, "endColumn": 46}, "@typescript-eslint/no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'error' is assigned a value but never used.", "'BellIcon' is defined but never used.", "'UserRole' is defined but never used.", "@typescript-eslint/no-redeclare", "'CustomerProfile' is already defined.", "redeclared", "'user' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchSummary'. Either include it or remove the dependency array.", "ArrayExpression", ["242"], "'handlePrintReceipt' is assigned a value but never used.", "'setSearchQuery' is assigned a value but never used.", "'setLimit' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTransfers'. Either include it or remove the dependency array.", ["243"], "React Hook useEffect has a missing dependency: 'fetchSales'. Either include it or remove the dependency array.", ["244"], "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["245"], "'Legend' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'COLORS' is assigned a value but never used.", ["246"], "'axios' is defined but never used.", "'EyeIcon' is defined but never used.", {"desc": "247", "fix": "248"}, {"desc": "249", "fix": "250"}, {"desc": "251", "fix": "252"}, {"desc": "253", "fix": "254"}, {"desc": "253", "fix": "255"}, "Update the dependencies array to be: [period, startDate, endDate, fetchSummary]", {"range": "256", "text": "257"}, "Update the dependencies array to be: [statusFilter, page, limit, fetchTransfers]", {"range": "258", "text": "259"}, "Update the dependencies array to be: [statusFilter, page, limit, fetchSales]", {"range": "260", "text": "261"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "262", "text": "263"}, {"range": "264", "text": "263"}, [803, 831], "[period, startDate, endDate, fetchSummary]", [610, 637], "[statusFilter, page, limit, fetchTransfers]", [600, 627], "[statusFilter, page, limit, fetchSales]", [989, 991], "[fetchDashboardData]", [965, 967]]