{"ast": null, "code": "var getType = function (payload) {\n  return Object.prototype.toString.call(payload).slice(8, -1);\n};\nexport var isUndefined = function (payload) {\n  return typeof payload === 'undefined';\n};\nexport var isNull = function (payload) {\n  return payload === null;\n};\nexport var isPlainObject = function (payload) {\n  if (typeof payload !== 'object' || payload === null) return false;\n  if (payload === Object.prototype) return false;\n  if (Object.getPrototypeOf(payload) === null) return true;\n  return Object.getPrototypeOf(payload) === Object.prototype;\n};\nexport var isEmptyObject = function (payload) {\n  return isPlainObject(payload) && Object.keys(payload).length === 0;\n};\nexport var isArray = function (payload) {\n  return Array.isArray(payload);\n};\nexport var isString = function (payload) {\n  return typeof payload === 'string';\n};\nexport var isNumber = function (payload) {\n  return typeof payload === 'number' && !isNaN(payload);\n};\nexport var isBoolean = function (payload) {\n  return typeof payload === 'boolean';\n};\nexport var isRegExp = function (payload) {\n  return payload instanceof RegExp;\n};\nexport var isMap = function (payload) {\n  return payload instanceof Map;\n};\nexport var isSet = function (payload) {\n  return payload instanceof Set;\n};\nexport var isSymbol = function (payload) {\n  return getType(payload) === 'Symbol';\n};\nexport var isDate = function (payload) {\n  return payload instanceof Date && !isNaN(payload.valueOf());\n};\nexport var isError = function (payload) {\n  return payload instanceof Error;\n};\nexport var isNaNValue = function (payload) {\n  return typeof payload === 'number' && isNaN(payload);\n};\nexport var isPrimitive = function (payload) {\n  return isBoolean(payload) || isNull(payload) || isUndefined(payload) || isNumber(payload) || isString(payload) || isSymbol(payload);\n};\nexport var isBigint = function (payload) {\n  return typeof payload === 'bigint';\n};\nexport var isInfinite = function (payload) {\n  return payload === Infinity || payload === -Infinity;\n};\nexport var isTypedArray = function (payload) {\n  return ArrayBuffer.isView(payload) && !(payload instanceof DataView);\n};\nexport var isURL = function (payload) {\n  return payload instanceof URL;\n};", "map": {"version": 3, "names": ["getType", "payload", "Object", "prototype", "toString", "call", "slice", "isUndefined", "isNull", "isPlainObject", "getPrototypeOf", "isEmptyObject", "keys", "length", "isArray", "Array", "isString", "isNumber", "isNaN", "isBoolean", "isRegExp", "RegExp", "isMap", "Map", "isSet", "Set", "isSymbol", "isDate", "Date", "valueOf", "isError", "Error", "isNaNValue", "isPrimitive", "isBigint", "isInfinite", "Infinity", "isTypedArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DataView", "isURL", "URL"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/superjson/src/is.ts"], "sourcesContent": ["const getType = (payload: any): string =>\n  Object.prototype.toString.call(payload).slice(8, -1);\n\nexport const isUndefined = (payload: any): payload is undefined =>\n  typeof payload === 'undefined';\n\nexport const isNull = (payload: any): payload is null => payload === null;\n\nexport const isPlainObject = (\n  payload: any\n): payload is { [key: string]: any } => {\n  if (typeof payload !== 'object' || payload === null) return false;\n  if (payload === Object.prototype) return false;\n  if (Object.getPrototypeOf(payload) === null) return true;\n\n  return Object.getPrototypeOf(payload) === Object.prototype;\n};\n\nexport const isEmptyObject = (payload: any): payload is {} =>\n  isPlainObject(payload) && Object.keys(payload).length === 0;\n\nexport const isArray = (payload: any): payload is any[] =>\n  Array.isArray(payload);\n\nexport const isString = (payload: any): payload is string =>\n  typeof payload === 'string';\n\nexport const isNumber = (payload: any): payload is number =>\n  typeof payload === 'number' && !isNaN(payload);\n\nexport const isBoolean = (payload: any): payload is boolean =>\n  typeof payload === 'boolean';\n\nexport const isRegExp = (payload: any): payload is RegExp =>\n  payload instanceof RegExp;\n\nexport const isMap = (payload: any): payload is Map<any, any> =>\n  payload instanceof Map;\n\nexport const isSet = (payload: any): payload is Set<any> =>\n  payload instanceof Set;\n\nexport const isSymbol = (payload: any): payload is symbol =>\n  getType(payload) === 'Symbol';\n\nexport const isDate = (payload: any): payload is Date =>\n  payload instanceof Date && !isNaN(payload.valueOf());\n\nexport const isError = (payload: any): payload is Error =>\n  payload instanceof Error;\n\nexport const isNaNValue = (payload: any): payload is typeof NaN =>\n  typeof payload === 'number' && isNaN(payload);\n\nexport const isPrimitive = (\n  payload: any\n): payload is boolean | null | undefined | number | string | symbol =>\n  isBoolean(payload) ||\n  isNull(payload) ||\n  isUndefined(payload) ||\n  isNumber(payload) ||\n  isString(payload) ||\n  isSymbol(payload);\n\nexport const isBigint = (payload: any): payload is bigint =>\n  typeof payload === 'bigint';\n\nexport const isInfinite = (payload: any): payload is number =>\n  payload === Infinity || payload === -Infinity;\n\nexport type TypedArrayConstructor =\n  | Int8ArrayConstructor\n  | Uint8ArrayConstructor\n  | Uint8ClampedArrayConstructor\n  | Int16ArrayConstructor\n  | Uint16ArrayConstructor\n  | Int32ArrayConstructor\n  | Uint32ArrayConstructor\n  | Float32ArrayConstructor\n  | Float64ArrayConstructor;\n\nexport type TypedArray = InstanceType<TypedArrayConstructor>;\n\nexport const isTypedArray = (payload: any): payload is TypedArray =>\n  ArrayBuffer.isView(payload) && !(payload instanceof DataView);\n\nexport const isURL = (payload: any): payload is URL => payload instanceof URL;\n"], "mappings": "AAAA,IAAMA,OAAO,GAAG,SAAAA,CAACC,OAAY;EAC3B,OAAAC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAApD,CAAoD;AAEtD,OAAO,IAAMC,WAAW,GAAG,SAAAA,CAACN,OAAY;EACtC,cAAOA,OAAO,KAAK,WAAW;AAA9B,CAA8B;AAEhC,OAAO,IAAMO,MAAM,GAAG,SAAAA,CAACP,OAAY;EAAsB,OAAAA,OAAO,KAAK,IAAI;AAAhB,CAAgB;AAEzE,OAAO,IAAMQ,aAAa,GAAG,SAAAA,CAC3BR,OAAY;EAEZ,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE,OAAO,KAAK;EACjE,IAAIA,OAAO,KAAKC,MAAM,CAACC,SAAS,EAAE,OAAO,KAAK;EAC9C,IAAID,MAAM,CAACQ,cAAc,CAACT,OAAO,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI;EAExD,OAAOC,MAAM,CAACQ,cAAc,CAACT,OAAO,CAAC,KAAKC,MAAM,CAACC,SAAS;AAC5D,CAAC;AAED,OAAO,IAAMQ,aAAa,GAAG,SAAAA,CAACV,OAAY;EACxC,OAAAQ,aAAa,CAACR,OAAO,CAAC,IAAIC,MAAM,CAACU,IAAI,CAACX,OAAO,CAAC,CAACY,MAAM,KAAK,CAAC;AAA3D,CAA2D;AAE7D,OAAO,IAAMC,OAAO,GAAG,SAAAA,CAACb,OAAY;EAClC,OAAAc,KAAK,CAACD,OAAO,CAACb,OAAO,CAAC;AAAtB,CAAsB;AAExB,OAAO,IAAMe,QAAQ,GAAG,SAAAA,CAACf,OAAY;EACnC,cAAOA,OAAO,KAAK,QAAQ;AAA3B,CAA2B;AAE7B,OAAO,IAAMgB,QAAQ,GAAG,SAAAA,CAAChB,OAAY;EACnC,cAAOA,OAAO,KAAK,QAAQ,IAAI,CAACiB,KAAK,CAACjB,OAAO,CAAC;AAA9C,CAA8C;AAEhD,OAAO,IAAMkB,SAAS,GAAG,SAAAA,CAAClB,OAAY;EACpC,cAAOA,OAAO,KAAK,SAAS;AAA5B,CAA4B;AAE9B,OAAO,IAAMmB,QAAQ,GAAG,SAAAA,CAACnB,OAAY;EACnC,OAAAA,OAAO,YAAYoB,MAAM;AAAzB,CAAyB;AAE3B,OAAO,IAAMC,KAAK,GAAG,SAAAA,CAACrB,OAAY;EAChC,OAAAA,OAAO,YAAYsB,GAAG;AAAtB,CAAsB;AAExB,OAAO,IAAMC,KAAK,GAAG,SAAAA,CAACvB,OAAY;EAChC,OAAAA,OAAO,YAAYwB,GAAG;AAAtB,CAAsB;AAExB,OAAO,IAAMC,QAAQ,GAAG,SAAAA,CAACzB,OAAY;EACnC,OAAAD,OAAO,CAACC,OAAO,CAAC,KAAK,QAAQ;AAA7B,CAA6B;AAE/B,OAAO,IAAM0B,MAAM,GAAG,SAAAA,CAAC1B,OAAY;EACjC,OAAAA,OAAO,YAAY2B,IAAI,IAAI,CAACV,KAAK,CAACjB,OAAO,CAAC4B,OAAO,EAAE,CAAC;AAApD,CAAoD;AAEtD,OAAO,IAAMC,OAAO,GAAG,SAAAA,CAAC7B,OAAY;EAClC,OAAAA,OAAO,YAAY8B,KAAK;AAAxB,CAAwB;AAE1B,OAAO,IAAMC,UAAU,GAAG,SAAAA,CAAC/B,OAAY;EACrC,cAAOA,OAAO,KAAK,QAAQ,IAAIiB,KAAK,CAACjB,OAAO,CAAC;AAA7C,CAA6C;AAE/C,OAAO,IAAMgC,WAAW,GAAG,SAAAA,CACzBhC,OAAY;EAEZ,OAAAkB,SAAS,CAAClB,OAAO,CAAC,IAClBO,MAAM,CAACP,OAAO,CAAC,IACfM,WAAW,CAACN,OAAO,CAAC,IACpBgB,QAAQ,CAAChB,OAAO,CAAC,IACjBe,QAAQ,CAACf,OAAO,CAAC,IACjByB,QAAQ,CAACzB,OAAO,CAAC;AALjB,CAKiB;AAEnB,OAAO,IAAMiC,QAAQ,GAAG,SAAAA,CAACjC,OAAY;EACnC,cAAOA,OAAO,KAAK,QAAQ;AAA3B,CAA2B;AAE7B,OAAO,IAAMkC,UAAU,GAAG,SAAAA,CAAClC,OAAY;EACrC,OAAAA,OAAO,KAAKmC,QAAQ,IAAInC,OAAO,KAAK,CAACmC,QAAQ;AAA7C,CAA6C;AAe/C,OAAO,IAAMC,YAAY,GAAG,SAAAA,CAACpC,OAAY;EACvC,OAAAqC,WAAW,CAACC,MAAM,CAACtC,OAAO,CAAC,IAAI,EAAEA,OAAO,YAAYuC,QAAQ,CAAC;AAA7D,CAA6D;AAE/D,OAAO,IAAMC,KAAK,GAAG,SAAAA,CAACxC,OAAY;EAAqB,OAAAA,OAAO,YAAYyC,GAAG;AAAtB,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}