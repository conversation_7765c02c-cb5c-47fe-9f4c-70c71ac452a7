{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst orderBy = require('./orderBy.js');\nconst flatten = require('../../array/flatten.js');\nconst isIterateeCall = require('../_internal/isIterateeCall.js');\nfunction sortBy(collection) {\n  for (var _len = arguments.length, criteria = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    criteria[_key - 1] = arguments[_key];\n  }\n  const length = criteria.length;\n  if (length > 1 && isIterateeCall.isIterateeCall(collection, criteria[0], criteria[1])) {\n    criteria = [];\n  } else if (length > 2 && isIterateeCall.isIterateeCall(criteria[0], criteria[1], criteria[2])) {\n    criteria = [criteria[0]];\n  }\n  return orderBy.orderBy(collection, flatten.flatten(criteria), ['asc']);\n}\nexports.sortBy = sortBy;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "orderBy", "require", "flatten", "isIterateeCall", "sortBy", "collection", "_len", "arguments", "length", "criteria", "Array", "_key"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/es-toolkit/dist/compat/array/sortBy.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst orderBy = require('./orderBy.js');\nconst flatten = require('../../array/flatten.js');\nconst isIterateeCall = require('../_internal/isIterateeCall.js');\n\nfunction sortBy(collection, ...criteria) {\n    const length = criteria.length;\n    if (length > 1 && isIterateeCall.isIterateeCall(collection, criteria[0], criteria[1])) {\n        criteria = [];\n    }\n    else if (length > 2 && isIterateeCall.isIterateeCall(criteria[0], criteria[1], criteria[2])) {\n        criteria = [criteria[0]];\n    }\n    return orderBy.orderBy(collection, flatten.flatten(criteria), ['asc']);\n}\n\nexports.sortBy = sortBy;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;AACvC,MAAMC,OAAO,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AACjD,MAAME,cAAc,GAAGF,OAAO,CAAC,gCAAgC,CAAC;AAEhE,SAASG,MAAMA,CAACC,UAAU,EAAe;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAVC,QAAQ,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAARF,QAAQ,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EACnC,MAAMH,MAAM,GAAGC,QAAQ,CAACD,MAAM;EAC9B,IAAIA,MAAM,GAAG,CAAC,IAAIL,cAAc,CAACA,cAAc,CAACE,UAAU,EAAEI,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;IACnFA,QAAQ,GAAG,EAAE;EACjB,CAAC,MACI,IAAID,MAAM,GAAG,CAAC,IAAIL,cAAc,CAACA,cAAc,CAACM,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;IACzFA,QAAQ,GAAG,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5B;EACA,OAAOT,OAAO,CAACA,OAAO,CAACK,UAAU,EAAEH,OAAO,CAACA,OAAO,CAACO,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC1E;AAEAb,OAAO,CAACQ,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}