{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from) {\n  for (var i = 0, il = from.length, j = to.length; i < il; i++, j++) to[j] = from[i];\n  return to;\n};\nimport { isBigint, isDate, isInfinite, isMap, isNaNValue, isRegExp, isSet, isUndefined, isSymbol, isArray, isError, isTypedArray, isURL } from './is';\nimport { findArr } from './util';\nfunction simpleTransformation(isApplicable, annotation, transform, untransform) {\n  return {\n    isApplicable: isApplicable,\n    annotation: annotation,\n    transform: transform,\n    untransform: untransform\n  };\n}\nvar simpleRules = [simpleTransformation(isUndefined, 'undefined', function () {\n  return null;\n}, function () {\n  return undefined;\n}), simpleTransformation(isBigint, 'bigint', function (v) {\n  return v.toString();\n}, function (v) {\n  if (typeof BigInt !== 'undefined') {\n    return BigInt(v);\n  }\n  console.error('Please add a BigInt polyfill.');\n  return v;\n}), simpleTransformation(isDate, 'Date', function (v) {\n  return v.toISOString();\n}, function (v) {\n  return new Date(v);\n}), simpleTransformation(isError, 'Error', function (v, superJson) {\n  var baseError = {\n    name: v.name,\n    message: v.message\n  };\n  superJson.allowedErrorProps.forEach(function (prop) {\n    baseError[prop] = v[prop];\n  });\n  return baseError;\n}, function (v, superJson) {\n  var e = new Error(v.message);\n  e.name = v.name;\n  e.stack = v.stack;\n  superJson.allowedErrorProps.forEach(function (prop) {\n    e[prop] = v[prop];\n  });\n  return e;\n}), simpleTransformation(isRegExp, 'regexp', function (v) {\n  return '' + v;\n}, function (regex) {\n  var body = regex.slice(1, regex.lastIndexOf('/'));\n  var flags = regex.slice(regex.lastIndexOf('/') + 1);\n  return new RegExp(body, flags);\n}), simpleTransformation(isSet, 'set',\n// (sets only exist in es6+)\n// eslint-disable-next-line es5/no-es6-methods\nfunction (v) {\n  return __spreadArray([], __read(v.values()));\n}, function (v) {\n  return new Set(v);\n}), simpleTransformation(isMap, 'map', function (v) {\n  return __spreadArray([], __read(v.entries()));\n}, function (v) {\n  return new Map(v);\n}), simpleTransformation(function (v) {\n  return isNaNValue(v) || isInfinite(v);\n}, 'number', function (v) {\n  if (isNaNValue(v)) {\n    return 'NaN';\n  }\n  if (v > 0) {\n    return 'Infinity';\n  } else {\n    return '-Infinity';\n  }\n}, Number), simpleTransformation(function (v) {\n  return v === 0 && 1 / v === -Infinity;\n}, 'number', function () {\n  return '-0';\n}, Number), simpleTransformation(isURL, 'URL', function (v) {\n  return v.toString();\n}, function (v) {\n  return new URL(v);\n})];\nfunction compositeTransformation(isApplicable, annotation, transform, untransform) {\n  return {\n    isApplicable: isApplicable,\n    annotation: annotation,\n    transform: transform,\n    untransform: untransform\n  };\n}\nvar symbolRule = compositeTransformation(function (s, superJson) {\n  if (isSymbol(s)) {\n    var isRegistered = !!superJson.symbolRegistry.getIdentifier(s);\n    return isRegistered;\n  }\n  return false;\n}, function (s, superJson) {\n  var identifier = superJson.symbolRegistry.getIdentifier(s);\n  return ['symbol', identifier];\n}, function (v) {\n  return v.description;\n}, function (_, a, superJson) {\n  var value = superJson.symbolRegistry.getValue(a[1]);\n  if (!value) {\n    throw new Error('Trying to deserialize unknown symbol');\n  }\n  return value;\n});\nvar constructorToName = [Int8Array, Uint8Array, Int16Array, Uint16Array, Int32Array, Uint32Array, Float32Array, Float64Array, Uint8ClampedArray].reduce(function (obj, ctor) {\n  obj[ctor.name] = ctor;\n  return obj;\n}, {});\nvar typedArrayRule = compositeTransformation(isTypedArray, function (v) {\n  return ['typed-array', v.constructor.name];\n}, function (v) {\n  return __spreadArray([], __read(v));\n}, function (v, a) {\n  var ctor = constructorToName[a[1]];\n  if (!ctor) {\n    throw new Error('Trying to deserialize unknown typed array');\n  }\n  return new ctor(v);\n});\nexport function isInstanceOfRegisteredClass(potentialClass, superJson) {\n  if (potentialClass === null || potentialClass === void 0 ? void 0 : potentialClass.constructor) {\n    var isRegistered = !!superJson.classRegistry.getIdentifier(potentialClass.constructor);\n    return isRegistered;\n  }\n  return false;\n}\nvar classRule = compositeTransformation(isInstanceOfRegisteredClass, function (clazz, superJson) {\n  var identifier = superJson.classRegistry.getIdentifier(clazz.constructor);\n  return ['class', identifier];\n}, function (clazz, superJson) {\n  var allowedProps = superJson.classRegistry.getAllowedProps(clazz.constructor);\n  if (!allowedProps) {\n    return __assign({}, clazz);\n  }\n  var result = {};\n  allowedProps.forEach(function (prop) {\n    result[prop] = clazz[prop];\n  });\n  return result;\n}, function (v, a, superJson) {\n  var clazz = superJson.classRegistry.getValue(a[1]);\n  if (!clazz) {\n    throw new Error('Trying to deserialize unknown class - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564');\n  }\n  return Object.assign(Object.create(clazz.prototype), v);\n});\nvar customRule = compositeTransformation(function (value, superJson) {\n  return !!superJson.customTransformerRegistry.findApplicable(value);\n}, function (value, superJson) {\n  var transformer = superJson.customTransformerRegistry.findApplicable(value);\n  return ['custom', transformer.name];\n}, function (value, superJson) {\n  var transformer = superJson.customTransformerRegistry.findApplicable(value);\n  return transformer.serialize(value);\n}, function (v, a, superJson) {\n  var transformer = superJson.customTransformerRegistry.findByName(a[1]);\n  if (!transformer) {\n    throw new Error('Trying to deserialize unknown custom value');\n  }\n  return transformer.deserialize(v);\n});\nvar compositeRules = [classRule, symbolRule, customRule, typedArrayRule];\nexport var transformValue = function (value, superJson) {\n  var applicableCompositeRule = findArr(compositeRules, function (rule) {\n    return rule.isApplicable(value, superJson);\n  });\n  if (applicableCompositeRule) {\n    return {\n      value: applicableCompositeRule.transform(value, superJson),\n      type: applicableCompositeRule.annotation(value, superJson)\n    };\n  }\n  var applicableSimpleRule = findArr(simpleRules, function (rule) {\n    return rule.isApplicable(value, superJson);\n  });\n  if (applicableSimpleRule) {\n    return {\n      value: applicableSimpleRule.transform(value, superJson),\n      type: applicableSimpleRule.annotation\n    };\n  }\n  return undefined;\n};\nvar simpleRulesByAnnotation = {};\nsimpleRules.forEach(function (rule) {\n  simpleRulesByAnnotation[rule.annotation] = rule;\n});\nexport var untransformValue = function (json, type, superJson) {\n  if (isArray(type)) {\n    switch (type[0]) {\n      case 'symbol':\n        return symbolRule.untransform(json, type, superJson);\n      case 'class':\n        return classRule.untransform(json, type, superJson);\n      case 'custom':\n        return customRule.untransform(json, type, superJson);\n      case 'typed-array':\n        return typedArrayRule.untransform(json, type, superJson);\n      default:\n        throw new Error('Unknown transformation: ' + type);\n    }\n  } else {\n    var transformation = simpleRulesByAnnotation[type];\n    if (!transformation) {\n      throw new Error('Unknown transformation: ' + type);\n    }\n    return transformation.untransform(json, superJson);\n  }\n};", "map": {"version": 3, "names": ["isBigint", "isDate", "isInfinite", "isMap", "isNaNValue", "isRegExp", "isSet", "isUndefined", "isSymbol", "isArray", "isError", "isTypedArray", "isURL", "findArr", "simpleTransformation", "isApplicable", "annotation", "transform", "untransform", "simpleRules", "undefined", "v", "toString", "BigInt", "console", "error", "toISOString", "Date", "superJson", "baseError", "name", "message", "allowedErrorProps", "for<PERSON>ach", "prop", "e", "Error", "stack", "regex", "body", "slice", "lastIndexOf", "flags", "RegExp", "__spread<PERSON><PERSON>y", "__read", "values", "Set", "entries", "Map", "Number", "Infinity", "URL", "compositeTransformation", "symbolRule", "s", "isRegistered", "symbolRegistry", "getIdentifier", "identifier", "description", "_", "a", "value", "getValue", "constructorToName", "Int8Array", "Uint8Array", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "Uint8ClampedArray", "reduce", "obj", "ctor", "typedArrayRule", "constructor", "isInstanceOfRegisteredClass", "potentialClass", "classRegistry", "classRule", "clazz", "allowedProps", "getAllowedProps", "__assign", "result", "Object", "assign", "create", "prototype", "customRule", "customTransformerRegistry", "findApplicable", "transformer", "serialize", "findByName", "deserialize", "compositeRules", "transformValue", "applicableCompositeRule", "rule", "type", "applicableSimpleRule", "simpleRulesByAnnotation", "untransformValue", "json", "transformation"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/superjson/src/transformer.ts"], "sourcesContent": ["import {\n  isBigint,\n  isDate,\n  isInfinite,\n  isMap,\n  isNaNValue,\n  isRegExp,\n  isSet,\n  isUndefined,\n  isSymbol,\n  isArray,\n  isError,\n  isTypedArray,\n  TypedArrayConstructor,\n  isURL,\n} from './is';\nimport { findArr } from './util';\nimport SuperJSON from '.';\n\nexport type PrimitiveTypeAnnotation = 'number' | 'undefined' | 'bigint';\n\ntype LeafTypeAnnotation =\n  | PrimitiveTypeAnnotation\n  | 'regexp'\n  | 'Date'\n  | 'Error'\n  | 'URL';\n\ntype TypedArrayAnnotation = ['typed-array', string];\ntype ClassTypeAnnotation = ['class', string];\ntype SymbolTypeAnnotation = ['symbol', string];\ntype CustomTypeAnnotation = ['custom', string];\n\ntype SimpleTypeAnnotation = LeafTypeAnnotation | 'map' | 'set';\n\ntype CompositeTypeAnnotation =\n  | TypedArrayAnnotation\n  | ClassTypeAnnotation\n  | SymbolTypeAnnotation\n  | CustomTypeAnnotation;\n\nexport type TypeAnnotation = SimpleTypeAnnotation | CompositeTypeAnnotation;\n\nfunction simpleTransformation<I, O, A extends SimpleTypeAnnotation>(\n  isApplicable: (v: any, superJson: SuperJSON) => v is I,\n  annotation: A,\n  transform: (v: I, superJson: SuperJSON) => O,\n  untransform: (v: O, superJson: SuperJSON) => I\n) {\n  return {\n    isApplicable,\n    annotation,\n    transform,\n    untransform,\n  };\n}\n\nconst simpleRules = [\n  simpleTransformation(\n    isUndefined,\n    'undefined',\n    () => null,\n    () => undefined\n  ),\n  simpleTransformation(\n    isBigint,\n    'bigint',\n    v => v.toString(),\n    v => {\n      if (typeof BigInt !== 'undefined') {\n        return BigInt(v);\n      }\n\n      console.error('Please add a BigInt polyfill.');\n\n      return v as any;\n    }\n  ),\n  simpleTransformation(\n    isDate,\n    'Date',\n    v => v.toISOString(),\n    v => new Date(v)\n  ),\n\n  simpleTransformation(\n    isError,\n    'Error',\n    (v, superJson) => {\n      const baseError: any = {\n        name: v.name,\n        message: v.message,\n      };\n\n      superJson.allowedErrorProps.forEach(prop => {\n        baseError[prop] = (v as any)[prop];\n      });\n\n      return baseError;\n    },\n    (v, superJson) => {\n      const e = new Error(v.message);\n      e.name = v.name;\n      e.stack = v.stack;\n\n      superJson.allowedErrorProps.forEach(prop => {\n        (e as any)[prop] = v[prop];\n      });\n\n      return e;\n    }\n  ),\n\n  simpleTransformation(\n    isRegExp,\n    'regexp',\n    v => '' + v,\n    regex => {\n      const body = regex.slice(1, regex.lastIndexOf('/'));\n      const flags = regex.slice(regex.lastIndexOf('/') + 1);\n      return new RegExp(body, flags);\n    }\n  ),\n\n  simpleTransformation(\n    isSet,\n    'set',\n    // (sets only exist in es6+)\n    // eslint-disable-next-line es5/no-es6-methods\n    v => [...v.values()],\n    v => new Set(v)\n  ),\n  simpleTransformation(\n    isMap,\n    'map',\n    v => [...v.entries()],\n    v => new Map(v)\n  ),\n\n  simpleTransformation<number, 'NaN' | 'Infinity' | '-Infinity', 'number'>(\n    (v): v is number => isNaNValue(v) || isInfinite(v),\n    'number',\n    v => {\n      if (isNaNValue(v)) {\n        return 'NaN';\n      }\n\n      if (v > 0) {\n        return 'Infinity';\n      } else {\n        return '-Infinity';\n      }\n    },\n    Number\n  ),\n\n  simpleTransformation<number, '-0', 'number'>(\n    (v): v is number => v === 0 && 1 / v === -Infinity,\n    'number',\n    () => {\n      return '-0';\n    },\n    Number\n  ),\n\n  simpleTransformation(\n    isURL,\n    'URL',\n    v => v.toString(),\n    v => new URL(v)\n  ),\n];\n\nfunction compositeTransformation<I, O, A extends CompositeTypeAnnotation>(\n  isApplicable: (v: any, superJson: SuperJSON) => v is I,\n  annotation: (v: I, superJson: SuperJSON) => A,\n  transform: (v: I, superJson: SuperJSON) => O,\n  untransform: (v: O, a: A, superJson: SuperJSON) => I\n) {\n  return {\n    isApplicable,\n    annotation,\n    transform,\n    untransform,\n  };\n}\n\nconst symbolRule = compositeTransformation(\n  (s, superJson): s is Symbol => {\n    if (isSymbol(s)) {\n      const isRegistered = !!superJson.symbolRegistry.getIdentifier(s);\n      return isRegistered;\n    }\n    return false;\n  },\n  (s, superJson) => {\n    const identifier = superJson.symbolRegistry.getIdentifier(s);\n    return ['symbol', identifier!];\n  },\n  v => v.description,\n  (_, a, superJson) => {\n    const value = superJson.symbolRegistry.getValue(a[1]);\n    if (!value) {\n      throw new Error('Trying to deserialize unknown symbol');\n    }\n    return value;\n  }\n);\n\nconst constructorToName = [\n  Int8Array,\n  Uint8Array,\n  Int16Array,\n  Uint16Array,\n  Int32Array,\n  Uint32Array,\n  Float32Array,\n  Float64Array,\n  Uint8ClampedArray,\n].reduce<Record<string, TypedArrayConstructor>>((obj, ctor) => {\n  obj[ctor.name] = ctor;\n  return obj;\n}, {});\n\nconst typedArrayRule = compositeTransformation(\n  isTypedArray,\n  v => ['typed-array', v.constructor.name],\n  v => [...v],\n  (v, a) => {\n    const ctor = constructorToName[a[1]];\n\n    if (!ctor) {\n      throw new Error('Trying to deserialize unknown typed array');\n    }\n\n    return new ctor(v);\n  }\n);\n\nexport function isInstanceOfRegisteredClass(\n  potentialClass: any,\n  superJson: SuperJSON\n): potentialClass is any {\n  if (potentialClass?.constructor) {\n    const isRegistered = !!superJson.classRegistry.getIdentifier(\n      potentialClass.constructor\n    );\n    return isRegistered;\n  }\n  return false;\n}\n\nconst classRule = compositeTransformation(\n  isInstanceOfRegisteredClass,\n  (clazz, superJson) => {\n    const identifier = superJson.classRegistry.getIdentifier(clazz.constructor);\n    return ['class', identifier!];\n  },\n  (clazz, superJson) => {\n    const allowedProps = superJson.classRegistry.getAllowedProps(\n      clazz.constructor\n    );\n    if (!allowedProps) {\n      return { ...clazz };\n    }\n\n    const result: any = {};\n    allowedProps.forEach(prop => {\n      result[prop] = clazz[prop];\n    });\n    return result;\n  },\n  (v, a, superJson) => {\n    const clazz = superJson.classRegistry.getValue(a[1]);\n\n    if (!clazz) {\n      throw new Error(\n        'Trying to deserialize unknown class - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564'\n      );\n    }\n\n    return Object.assign(Object.create(clazz.prototype), v);\n  }\n);\n\nconst customRule = compositeTransformation(\n  (value, superJson): value is any => {\n    return !!superJson.customTransformerRegistry.findApplicable(value);\n  },\n  (value, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findApplicable(\n      value\n    )!;\n    return ['custom', transformer.name];\n  },\n  (value, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findApplicable(\n      value\n    )!;\n    return transformer.serialize(value);\n  },\n  (v, a, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findByName(a[1]);\n    if (!transformer) {\n      throw new Error('Trying to deserialize unknown custom value');\n    }\n    return transformer.deserialize(v);\n  }\n);\n\nconst compositeRules = [classRule, symbolRule, customRule, typedArrayRule];\n\nexport const transformValue = (\n  value: any,\n  superJson: SuperJSON\n): { value: any; type: TypeAnnotation } | undefined => {\n  const applicableCompositeRule = findArr(compositeRules, rule =>\n    rule.isApplicable(value, superJson)\n  );\n  if (applicableCompositeRule) {\n    return {\n      value: applicableCompositeRule.transform(value as never, superJson),\n      type: applicableCompositeRule.annotation(value, superJson),\n    };\n  }\n\n  const applicableSimpleRule = findArr(simpleRules, rule =>\n    rule.isApplicable(value, superJson)\n  );\n\n  if (applicableSimpleRule) {\n    return {\n      value: applicableSimpleRule.transform(value as never, superJson),\n      type: applicableSimpleRule.annotation,\n    };\n  }\n\n  return undefined;\n};\n\nconst simpleRulesByAnnotation: Record<string, typeof simpleRules[0]> = {};\nsimpleRules.forEach(rule => {\n  simpleRulesByAnnotation[rule.annotation] = rule;\n});\n\nexport const untransformValue = (\n  json: any,\n  type: TypeAnnotation,\n  superJson: SuperJSON\n) => {\n  if (isArray(type)) {\n    switch (type[0]) {\n      case 'symbol':\n        return symbolRule.untransform(json, type, superJson);\n      case 'class':\n        return classRule.untransform(json, type, superJson);\n      case 'custom':\n        return customRule.untransform(json, type, superJson);\n      case 'typed-array':\n        return typedArrayRule.untransform(json, type, superJson);\n      default:\n        throw new Error('Unknown transformation: ' + type);\n    }\n  } else {\n    const transformation = simpleRulesByAnnotation[type];\n    if (!transformation) {\n      throw new Error('Unknown transformation: ' + type);\n    }\n\n    return transformation.untransform(json as never, superJson);\n  }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SACEA,QAAQ,EACRC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,YAAY,EAEZC,KAAK,QACA,MAAM;AACb,SAASC,OAAO,QAAQ,QAAQ;AA2BhC,SAASC,oBAAoBA,CAC3BC,YAAsD,EACtDC,UAAa,EACbC,SAA4C,EAC5CC,WAA8C;EAE9C,OAAO;IACLH,YAAY,EAAAA,YAAA;IACZC,UAAU,EAAAA,UAAA;IACVC,SAAS,EAAAA,SAAA;IACTC,WAAW,EAAAA;GACZ;AACH;AAEA,IAAMC,WAAW,GAAG,CAClBL,oBAAoB,CAClBP,WAAW,EACX,WAAW,EACX;EAAM,WAAI;AAAJ,CAAI,EACV;EAAM,OAAAa,SAAS;AAAT,CAAS,CAChB,EACDN,oBAAoB,CAClBd,QAAQ,EACR,QAAQ,EACR,UAAAqB,CAAC;EAAI,OAAAA,CAAC,CAACC,QAAQ,EAAE;AAAZ,CAAY,EACjB,UAAAD,CAAC;EACC,IAAI,OAAOE,MAAM,KAAK,WAAW,EAAE;IACjC,OAAOA,MAAM,CAACF,CAAC,CAAC;;EAGlBG,OAAO,CAACC,KAAK,CAAC,+BAA+B,CAAC;EAE9C,OAAOJ,CAAQ;AACjB,CAAC,CACF,EACDP,oBAAoB,CAClBb,MAAM,EACN,MAAM,EACN,UAAAoB,CAAC;EAAI,OAAAA,CAAC,CAACK,WAAW,EAAE;AAAf,CAAe,EACpB,UAAAL,CAAC;EAAI,WAAIM,IAAI,CAACN,CAAC,CAAC;AAAX,CAAW,CACjB,EAEDP,oBAAoB,CAClBJ,OAAO,EACP,OAAO,EACP,UAACW,CAAC,EAAEO,SAAS;EACX,IAAMC,SAAS,GAAQ;IACrBC,IAAI,EAAET,CAAC,CAACS,IAAI;IACZC,OAAO,EAAEV,CAAC,CAACU;GACZ;EAEDH,SAAS,CAACI,iBAAiB,CAACC,OAAO,CAAC,UAAAC,IAAI;IACtCL,SAAS,CAACK,IAAI,CAAC,GAAIb,CAAS,CAACa,IAAI,CAAC;EACpC,CAAC,CAAC;EAEF,OAAOL,SAAS;AAClB,CAAC,EACD,UAACR,CAAC,EAAEO,SAAS;EACX,IAAMO,CAAC,GAAG,IAAIC,KAAK,CAACf,CAAC,CAACU,OAAO,CAAC;EAC9BI,CAAC,CAACL,IAAI,GAAGT,CAAC,CAACS,IAAI;EACfK,CAAC,CAACE,KAAK,GAAGhB,CAAC,CAACgB,KAAK;EAEjBT,SAAS,CAACI,iBAAiB,CAACC,OAAO,CAAC,UAAAC,IAAI;IACrCC,CAAS,CAACD,IAAI,CAAC,GAAGb,CAAC,CAACa,IAAI,CAAC;EAC5B,CAAC,CAAC;EAEF,OAAOC,CAAC;AACV,CAAC,CACF,EAEDrB,oBAAoB,CAClBT,QAAQ,EACR,QAAQ,EACR,UAAAgB,CAAC;EAAI,SAAE,GAAGA,CAAC;AAAN,CAAM,EACX,UAAAiB,KAAK;EACH,IAAMC,IAAI,GAAGD,KAAK,CAACE,KAAK,CAAC,CAAC,EAAEF,KAAK,CAACG,WAAW,CAAC,GAAG,CAAC,CAAC;EACnD,IAAMC,KAAK,GAAGJ,KAAK,CAACE,KAAK,CAACF,KAAK,CAACG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACrD,OAAO,IAAIE,MAAM,CAACJ,IAAI,EAAEG,KAAK,CAAC;AAChC,CAAC,CACF,EAED5B,oBAAoB,CAClBR,KAAK,EACL,KAAK;AACL;AACA;AACA,UAAAe,CAAC;EAAI,OAAAuB,aAAA,KAAAC,MAAA,CAAIxB,CAAC,CAACyB,MAAM,EAAE;AAAd,CAAe,EACpB,UAAAzB,CAAC;EAAI,WAAI0B,GAAG,CAAC1B,CAAC,CAAC;AAAV,CAAU,CAChB,EACDP,oBAAoB,CAClBX,KAAK,EACL,KAAK,EACL,UAAAkB,CAAC;EAAI,OAAAuB,aAAA,KAAAC,MAAA,CAAIxB,CAAC,CAAC2B,OAAO,EAAE;AAAf,CAAgB,EACrB,UAAA3B,CAAC;EAAI,WAAI4B,GAAG,CAAC5B,CAAC,CAAC;AAAV,CAAU,CAChB,EAEDP,oBAAoB,CAClB,UAACO,CAAC;EAAkB,OAAAjB,UAAU,CAACiB,CAAC,CAAC,IAAInB,UAAU,CAACmB,CAAC,CAAC;AAA9B,CAA8B,EAClD,QAAQ,EACR,UAAAA,CAAC;EACC,IAAIjB,UAAU,CAACiB,CAAC,CAAC,EAAE;IACjB,OAAO,KAAK;;EAGd,IAAIA,CAAC,GAAG,CAAC,EAAE;IACT,OAAO,UAAU;GAClB,MAAM;IACL,OAAO,WAAW;;AAEtB,CAAC,EACD6B,MAAM,CACP,EAEDpC,oBAAoB,CAClB,UAACO,CAAC;EAAkB,OAAAA,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC8B,QAAQ;AAA9B,CAA8B,EAClD,QAAQ,EACR;EACE,OAAO,IAAI;AACb,CAAC,EACDD,MAAM,CACP,EAEDpC,oBAAoB,CAClBF,KAAK,EACL,KAAK,EACL,UAAAS,CAAC;EAAI,OAAAA,CAAC,CAACC,QAAQ,EAAE;AAAZ,CAAY,EACjB,UAAAD,CAAC;EAAI,WAAI+B,GAAG,CAAC/B,CAAC,CAAC;AAAV,CAAU,CAChB,CACF;AAED,SAASgC,uBAAuBA,CAC9BtC,YAAsD,EACtDC,UAA6C,EAC7CC,SAA4C,EAC5CC,WAAoD;EAEpD,OAAO;IACLH,YAAY,EAAAA,YAAA;IACZC,UAAU,EAAAA,UAAA;IACVC,SAAS,EAAAA,SAAA;IACTC,WAAW,EAAAA;GACZ;AACH;AAEA,IAAMoC,UAAU,GAAGD,uBAAuB,CACxC,UAACE,CAAC,EAAE3B,SAAS;EACX,IAAIpB,QAAQ,CAAC+C,CAAC,CAAC,EAAE;IACf,IAAMC,YAAY,GAAG,CAAC,CAAC5B,SAAS,CAAC6B,cAAc,CAACC,aAAa,CAACH,CAAC,CAAC;IAChE,OAAOC,YAAY;;EAErB,OAAO,KAAK;AACd,CAAC,EACD,UAACD,CAAC,EAAE3B,SAAS;EACX,IAAM+B,UAAU,GAAG/B,SAAS,CAAC6B,cAAc,CAACC,aAAa,CAACH,CAAC,CAAC;EAC5D,OAAO,CAAC,QAAQ,EAAEI,UAAW,CAAC;AAChC,CAAC,EACD,UAAAtC,CAAC;EAAI,OAAAA,CAAC,CAACuC,WAAW;AAAb,CAAa,EAClB,UAACC,CAAC,EAAEC,CAAC,EAAElC,SAAS;EACd,IAAMmC,KAAK,GAAGnC,SAAS,CAAC6B,cAAc,CAACO,QAAQ,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD,IAAI,CAACC,KAAK,EAAE;IACV,MAAM,IAAI3B,KAAK,CAAC,sCAAsC,CAAC;;EAEzD,OAAO2B,KAAK;AACd,CAAC,CACF;AAED,IAAME,iBAAiB,GAAG,CACxBC,SAAS,EACTC,UAAU,EACVC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,iBAAiB,CAClB,CAACC,MAAM,CAAwC,UAACC,GAAG,EAAEC,IAAI;EACxDD,GAAG,CAACC,IAAI,CAAC/C,IAAI,CAAC,GAAG+C,IAAI;EACrB,OAAOD,GAAG;AACZ,CAAC,EAAE,EAAE,CAAC;AAEN,IAAME,cAAc,GAAGzB,uBAAuB,CAC5C1C,YAAY,EACZ,UAAAU,CAAC;EAAI,QAAC,aAAa,EAAEA,CAAC,CAAC0D,WAAW,CAACjD,IAAI,CAAC;AAAnC,CAAmC,EACxC,UAAAT,CAAC;EAAI,OAAAuB,aAAA,KAAAC,MAAA,CAAIxB,CAAC;AAAL,CAAM,EACX,UAACA,CAAC,EAAEyC,CAAC;EACH,IAAMe,IAAI,GAAGZ,iBAAiB,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC;EAEpC,IAAI,CAACe,IAAI,EAAE;IACT,MAAM,IAAIzC,KAAK,CAAC,2CAA2C,CAAC;;EAG9D,OAAO,IAAIyC,IAAI,CAACxD,CAAC,CAAC;AACpB,CAAC,CACF;AAED,OAAM,SAAU2D,2BAA2BA,CACzCC,cAAmB,EACnBrD,SAAoB;EAEpB,IAAIqD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEF,WAAW,EAAE;IAC/B,IAAMvB,YAAY,GAAG,CAAC,CAAC5B,SAAS,CAACsD,aAAa,CAACxB,aAAa,CAC1DuB,cAAc,CAACF,WAAW,CAC3B;IACD,OAAOvB,YAAY;;EAErB,OAAO,KAAK;AACd;AAEA,IAAM2B,SAAS,GAAG9B,uBAAuB,CACvC2B,2BAA2B,EAC3B,UAACI,KAAK,EAAExD,SAAS;EACf,IAAM+B,UAAU,GAAG/B,SAAS,CAACsD,aAAa,CAACxB,aAAa,CAAC0B,KAAK,CAACL,WAAW,CAAC;EAC3E,OAAO,CAAC,OAAO,EAAEpB,UAAW,CAAC;AAC/B,CAAC,EACD,UAACyB,KAAK,EAAExD,SAAS;EACf,IAAMyD,YAAY,GAAGzD,SAAS,CAACsD,aAAa,CAACI,eAAe,CAC1DF,KAAK,CAACL,WAAW,CAClB;EACD,IAAI,CAACM,YAAY,EAAE;IACjB,OAAAE,QAAA,KAAYH,KAAK;;EAGnB,IAAMI,MAAM,GAAQ,EAAE;EACtBH,YAAY,CAACpD,OAAO,CAAC,UAAAC,IAAI;IACvBsD,MAAM,CAACtD,IAAI,CAAC,GAAGkD,KAAK,CAAClD,IAAI,CAAC;EAC5B,CAAC,CAAC;EACF,OAAOsD,MAAM;AACf,CAAC,EACD,UAACnE,CAAC,EAAEyC,CAAC,EAAElC,SAAS;EACd,IAAMwD,KAAK,GAAGxD,SAAS,CAACsD,aAAa,CAAClB,QAAQ,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC;EAEpD,IAAI,CAACsB,KAAK,EAAE;IACV,MAAM,IAAIhD,KAAK,CACb,qHAAqH,CACtH;;EAGH,OAAOqD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACE,MAAM,CAACP,KAAK,CAACQ,SAAS,CAAC,EAAEvE,CAAC,CAAC;AACzD,CAAC,CACF;AAED,IAAMwE,UAAU,GAAGxC,uBAAuB,CACxC,UAACU,KAAK,EAAEnC,SAAS;EACf,OAAO,CAAC,CAACA,SAAS,CAACkE,yBAAyB,CAACC,cAAc,CAAChC,KAAK,CAAC;AACpE,CAAC,EACD,UAACA,KAAK,EAAEnC,SAAS;EACf,IAAMoE,WAAW,GAAGpE,SAAS,CAACkE,yBAAyB,CAACC,cAAc,CACpEhC,KAAK,CACL;EACF,OAAO,CAAC,QAAQ,EAAEiC,WAAW,CAAClE,IAAI,CAAC;AACrC,CAAC,EACD,UAACiC,KAAK,EAAEnC,SAAS;EACf,IAAMoE,WAAW,GAAGpE,SAAS,CAACkE,yBAAyB,CAACC,cAAc,CACpEhC,KAAK,CACL;EACF,OAAOiC,WAAW,CAACC,SAAS,CAAClC,KAAK,CAAC;AACrC,CAAC,EACD,UAAC1C,CAAC,EAAEyC,CAAC,EAAElC,SAAS;EACd,IAAMoE,WAAW,GAAGpE,SAAS,CAACkE,yBAAyB,CAACI,UAAU,CAACpC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxE,IAAI,CAACkC,WAAW,EAAE;IAChB,MAAM,IAAI5D,KAAK,CAAC,4CAA4C,CAAC;;EAE/D,OAAO4D,WAAW,CAACG,WAAW,CAAC9E,CAAC,CAAC;AACnC,CAAC,CACF;AAED,IAAM+E,cAAc,GAAG,CAACjB,SAAS,EAAE7B,UAAU,EAAEuC,UAAU,EAAEf,cAAc,CAAC;AAE1E,OAAO,IAAMuB,cAAc,GAAG,SAAAA,CAC5BtC,KAAU,EACVnC,SAAoB;EAEpB,IAAM0E,uBAAuB,GAAGzF,OAAO,CAACuF,cAAc,EAAE,UAAAG,IAAI;IAC1D,OAAAA,IAAI,CAACxF,YAAY,CAACgD,KAAK,EAAEnC,SAAS,CAAC;EAAnC,CAAmC,CACpC;EACD,IAAI0E,uBAAuB,EAAE;IAC3B,OAAO;MACLvC,KAAK,EAAEuC,uBAAuB,CAACrF,SAAS,CAAC8C,KAAc,EAAEnC,SAAS,CAAC;MACnE4E,IAAI,EAAEF,uBAAuB,CAACtF,UAAU,CAAC+C,KAAK,EAAEnC,SAAS;KAC1D;;EAGH,IAAM6E,oBAAoB,GAAG5F,OAAO,CAACM,WAAW,EAAE,UAAAoF,IAAI;IACpD,OAAAA,IAAI,CAACxF,YAAY,CAACgD,KAAK,EAAEnC,SAAS,CAAC;EAAnC,CAAmC,CACpC;EAED,IAAI6E,oBAAoB,EAAE;IACxB,OAAO;MACL1C,KAAK,EAAE0C,oBAAoB,CAACxF,SAAS,CAAC8C,KAAc,EAAEnC,SAAS,CAAC;MAChE4E,IAAI,EAAEC,oBAAoB,CAACzF;KAC5B;;EAGH,OAAOI,SAAS;AAClB,CAAC;AAED,IAAMsF,uBAAuB,GAA0C,EAAE;AACzEvF,WAAW,CAACc,OAAO,CAAC,UAAAsE,IAAI;EACtBG,uBAAuB,CAACH,IAAI,CAACvF,UAAU,CAAC,GAAGuF,IAAI;AACjD,CAAC,CAAC;AAEF,OAAO,IAAMI,gBAAgB,GAAG,SAAAA,CAC9BC,IAAS,EACTJ,IAAoB,EACpB5E,SAAoB;EAEpB,IAAInB,OAAO,CAAC+F,IAAI,CAAC,EAAE;IACjB,QAAQA,IAAI,CAAC,CAAC,CAAC;MACb,KAAK,QAAQ;QACX,OAAOlD,UAAU,CAACpC,WAAW,CAAC0F,IAAI,EAAEJ,IAAI,EAAE5E,SAAS,CAAC;MACtD,KAAK,OAAO;QACV,OAAOuD,SAAS,CAACjE,WAAW,CAAC0F,IAAI,EAAEJ,IAAI,EAAE5E,SAAS,CAAC;MACrD,KAAK,QAAQ;QACX,OAAOiE,UAAU,CAAC3E,WAAW,CAAC0F,IAAI,EAAEJ,IAAI,EAAE5E,SAAS,CAAC;MACtD,KAAK,aAAa;QAChB,OAAOkD,cAAc,CAAC5D,WAAW,CAAC0F,IAAI,EAAEJ,IAAI,EAAE5E,SAAS,CAAC;MAC1D;QACE,MAAM,IAAIQ,KAAK,CAAC,0BAA0B,GAAGoE,IAAI,CAAC;;GAEvD,MAAM;IACL,IAAMK,cAAc,GAAGH,uBAAuB,CAACF,IAAI,CAAC;IACpD,IAAI,CAACK,cAAc,EAAE;MACnB,MAAM,IAAIzE,KAAK,CAAC,0BAA0B,GAAGoE,IAAI,CAAC;;IAGpD,OAAOK,cAAc,CAAC3F,WAAW,CAAC0F,IAAa,EAAEhF,SAAS,CAAC;;AAE/D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}