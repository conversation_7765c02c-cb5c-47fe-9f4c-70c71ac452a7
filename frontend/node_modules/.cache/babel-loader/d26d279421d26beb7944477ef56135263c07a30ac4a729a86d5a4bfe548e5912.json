{"ast": null, "code": "import { createContext, useContext } from 'react';\nexport var LegendPortalContext = /*#__PURE__*/createContext(null);\nexport var useLegendPortal = () => useContext(LegendPortalContext);", "map": {"version": 3, "names": ["createContext", "useContext", "LegendPortalContext", "useLegendPortal"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/recharts/es6/context/legendPortalContext.js"], "sourcesContent": ["import { createContext, useContext } from 'react';\nexport var LegendPortalContext = /*#__PURE__*/createContext(null);\nexport var useLegendPortal = () => useContext(LegendPortalContext);"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,OAAO;AACjD,OAAO,IAAIC,mBAAmB,GAAG,aAAaF,aAAa,CAAC,IAAI,CAAC;AACjE,OAAO,IAAIG,eAAe,GAAGA,CAAA,KAAMF,UAAU,CAACC,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}