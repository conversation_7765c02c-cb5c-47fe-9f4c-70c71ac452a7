{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction isPlainObject(object) {\n  if (typeof object !== 'object') {\n    return false;\n  }\n  if (object == null) {\n    return false;\n  }\n  if (Object.getPrototypeOf(object) === null) {\n    return true;\n  }\n  if (Object.prototype.toString.call(object) !== '[object Object]') {\n    var _Object$getOwnPropert;\n    const tag = object[Symbol.toStringTag];\n    if (tag == null) {\n      return false;\n    }\n    const isTagReadonly = !((_Object$getOwnPropert = Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)) !== null && _Object$getOwnPropert !== void 0 && _Object$getOwnPropert.writable);\n    if (isTagReadonly) {\n      return false;\n    }\n    return object.toString() === \"[object \".concat(tag, \"]\");\n  }\n  let proto = object;\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Object.getPrototypeOf(object) === proto;\n}\nexports.isPlainObject = isPlainObject;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isPlainObject", "object", "getPrototypeOf", "prototype", "toString", "call", "_Object$getOwnPropert", "tag", "isTagReadonly", "getOwnPropertyDescriptor", "writable", "concat", "proto"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/es-toolkit/dist/compat/predicate/isPlainObject.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction isPlainObject(object) {\n    if (typeof object !== 'object') {\n        return false;\n    }\n    if (object == null) {\n        return false;\n    }\n    if (Object.getPrototypeOf(object) === null) {\n        return true;\n    }\n    if (Object.prototype.toString.call(object) !== '[object Object]') {\n        const tag = object[Symbol.toStringTag];\n        if (tag == null) {\n            return false;\n        }\n        const isTagReadonly = !Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)?.writable;\n        if (isTagReadonly) {\n            return false;\n        }\n        return object.toString() === `[object ${tag}]`;\n    }\n    let proto = object;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(object) === proto;\n}\n\nexports.isPlainObject = isPlainObject;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC3B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC5B,OAAO,KAAK;EAChB;EACA,IAAIA,MAAM,IAAI,IAAI,EAAE;IAChB,OAAO,KAAK;EAChB;EACA,IAAIP,MAAM,CAACQ,cAAc,CAACD,MAAM,CAAC,KAAK,IAAI,EAAE;IACxC,OAAO,IAAI;EACf;EACA,IAAIP,MAAM,CAACS,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,MAAM,CAAC,KAAK,iBAAiB,EAAE;IAAA,IAAAK,qBAAA;IAC9D,MAAMC,GAAG,GAAGN,MAAM,CAACJ,MAAM,CAACC,WAAW,CAAC;IACtC,IAAIS,GAAG,IAAI,IAAI,EAAE;MACb,OAAO,KAAK;IAChB;IACA,MAAMC,aAAa,GAAG,GAAAF,qBAAA,GAACZ,MAAM,CAACe,wBAAwB,CAACR,MAAM,EAAEJ,MAAM,CAACC,WAAW,CAAC,cAAAQ,qBAAA,eAA3DA,qBAAA,CAA6DI,QAAQ;IAC5F,IAAIF,aAAa,EAAE;MACf,OAAO,KAAK;IAChB;IACA,OAAOP,MAAM,CAACG,QAAQ,CAAC,CAAC,gBAAAO,MAAA,CAAgBJ,GAAG,MAAG;EAClD;EACA,IAAIK,KAAK,GAAGX,MAAM;EAClB,OAAOP,MAAM,CAACQ,cAAc,CAACU,KAAK,CAAC,KAAK,IAAI,EAAE;IAC1CA,KAAK,GAAGlB,MAAM,CAACQ,cAAc,CAACU,KAAK,CAAC;EACxC;EACA,OAAOlB,MAAM,CAACQ,cAAc,CAACD,MAAM,CAAC,KAAKW,KAAK;AAClD;AAEAhB,OAAO,CAACI,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}