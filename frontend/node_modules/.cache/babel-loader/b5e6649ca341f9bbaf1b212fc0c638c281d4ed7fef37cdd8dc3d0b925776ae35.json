{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport group, { rollup } from \"./group.js\";\nimport sort from \"./sort.js\";\nexport default function groupSort(values, reduce, key) {\n  return (reduce.length !== 2 ? sort(rollup(values, reduce, key), (_ref, _ref2) => {\n    let [ak, av] = _ref;\n    let [bk, bv] = _ref2;\n    return ascending(av, bv) || ascending(ak, bk);\n  }) : sort(group(values, key), (_ref3, _ref4) => {\n    let [ak, av] = _ref3;\n    let [bk, bv] = _ref4;\n    return reduce(av, bv) || ascending(ak, bk);\n  })).map(_ref5 => {\n    let [key] = _ref5;\n    return key;\n  });\n}", "map": {"version": 3, "names": ["ascending", "group", "rollup", "sort", "groupSort", "values", "reduce", "key", "length", "_ref", "_ref2", "ak", "av", "bk", "bv", "_ref3", "_ref4", "map", "_ref5"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-array/src/groupSort.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport group, {rollup} from \"./group.js\";\nimport sort from \"./sort.js\";\n\nexport default function groupSort(values, reduce, key) {\n  return (reduce.length !== 2\n    ? sort(rollup(values, reduce, key), (([ak, av], [bk, bv]) => ascending(av, bv) || ascending(ak, bk)))\n    : sort(group(values, key), (([ak, av], [bk, bv]) => reduce(av, bv) || ascending(ak, bk))))\n    .map(([key]) => key);\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,KAAK,IAAGC,MAAM,QAAO,YAAY;AACxC,OAAOC,IAAI,MAAM,WAAW;AAE5B,eAAe,SAASC,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAEC,GAAG,EAAE;EACrD,OAAO,CAACD,MAAM,CAACE,MAAM,KAAK,CAAC,GACvBL,IAAI,CAACD,MAAM,CAACG,MAAM,EAAEC,MAAM,EAAEC,GAAG,CAAC,EAAG,CAAAE,IAAA,EAAAC,KAAA;IAAA,IAAC,CAACC,EAAE,EAAEC,EAAE,CAAC,GAAAH,IAAA;IAAA,IAAE,CAACI,EAAE,EAAEC,EAAE,CAAC,GAAAJ,KAAA;IAAA,OAAKV,SAAS,CAACY,EAAE,EAAEE,EAAE,CAAC,IAAId,SAAS,CAACW,EAAE,EAAEE,EAAE,CAAC;EAAA,CAAC,CAAC,GACnGV,IAAI,CAACF,KAAK,CAACI,MAAM,EAAEE,GAAG,CAAC,EAAG,CAAAQ,KAAA,EAAAC,KAAA;IAAA,IAAC,CAACL,EAAE,EAAEC,EAAE,CAAC,GAAAG,KAAA;IAAA,IAAE,CAACF,EAAE,EAAEC,EAAE,CAAC,GAAAE,KAAA;IAAA,OAAKV,MAAM,CAACM,EAAE,EAAEE,EAAE,CAAC,IAAId,SAAS,CAACW,EAAE,EAAEE,EAAE,CAAC;EAAA,CAAC,CAAC,EACxFI,GAAG,CAACC,KAAA;IAAA,IAAC,CAACX,GAAG,CAAC,GAAAW,KAAA;IAAA,OAAKX,GAAG;EAAA,EAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}