{"ast": null, "code": "import { range as sequence } from \"d3-array\";\nimport { initRange } from \"./init.js\";\nimport ordinal from \"./ordinal.js\";\nexport default function band() {\n  var scale = ordinal().unknown(undefined),\n    domain = scale.domain,\n    ordinalRange = scale.range,\n    r0 = 0,\n    r1 = 1,\n    step,\n    bandwidth,\n    round = false,\n    paddingInner = 0,\n    paddingOuter = 0,\n    align = 0.5;\n  delete scale.unknown;\n  function rescale() {\n    var n = domain().length,\n      reverse = r1 < r0,\n      start = reverse ? r1 : r0,\n      stop = reverse ? r0 : r1;\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = sequence(n).map(function (i) {\n      return start + step * i;\n    });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n  scale.domain = function (_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n  scale.range = function (_) {\n    return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];\n  };\n  scale.rangeRound = function (_) {\n    return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();\n  };\n  scale.bandwidth = function () {\n    return bandwidth;\n  };\n  scale.step = function () {\n    return step;\n  };\n  scale.round = function (_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n  scale.padding = function (_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n  scale.paddingInner = function (_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n  scale.paddingOuter = function (_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n  scale.align = function (_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n  scale.copy = function () {\n    return band(domain(), [r0, r1]).round(round).paddingInner(paddingInner).paddingOuter(paddingOuter).align(align);\n  };\n  return initRange.apply(rescale(), arguments);\n}\nfunction pointish(scale) {\n  var copy = scale.copy;\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n  scale.copy = function () {\n    return pointish(copy());\n  };\n  return scale;\n}\nexport function point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}", "map": {"version": 3, "names": ["range", "sequence", "initRange", "ordinal", "band", "scale", "unknown", "undefined", "domain", "ordinalRange", "r0", "r1", "step", "bandwidth", "round", "paddingInner", "paddingOuter", "align", "rescale", "n", "length", "reverse", "start", "stop", "Math", "max", "floor", "values", "map", "i", "_", "arguments", "rangeRound", "padding", "min", "copy", "apply", "pointish", "point"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-scale/src/band.js"], "sourcesContent": ["import {range as sequence} from \"d3-array\";\nimport {initRange} from \"./init.js\";\nimport ordinal from \"./ordinal.js\";\n\nexport default function band() {\n  var scale = ordinal().unknown(undefined),\n      domain = scale.domain,\n      ordinalRange = scale.range,\n      r0 = 0,\n      r1 = 1,\n      step,\n      bandwidth,\n      round = false,\n      paddingInner = 0,\n      paddingOuter = 0,\n      align = 0.5;\n\n  delete scale.unknown;\n\n  function rescale() {\n    var n = domain().length,\n        reverse = r1 < r0,\n        start = reverse ? r1 : r0,\n        stop = reverse ? r0 : r1;\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = sequence(n).map(function(i) { return start + step * i; });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];\n  };\n\n  scale.rangeRound = function(_) {\n    return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();\n  };\n\n  scale.bandwidth = function() {\n    return bandwidth;\n  };\n\n  scale.step = function() {\n    return step;\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n\n  scale.padding = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n\n  scale.paddingInner = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n\n  scale.paddingOuter = function(_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n\n  scale.align = function(_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n\n  scale.copy = function() {\n    return band(domain(), [r0, r1])\n        .round(round)\n        .paddingInner(paddingInner)\n        .paddingOuter(paddingOuter)\n        .align(align);\n  };\n\n  return initRange.apply(rescale(), arguments);\n}\n\nfunction pointish(scale) {\n  var copy = scale.copy;\n\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n\n  scale.copy = function() {\n    return pointish(copy());\n  };\n\n  return scale;\n}\n\nexport function point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}\n"], "mappings": "AAAA,SAAQA,KAAK,IAAIC,QAAQ,QAAO,UAAU;AAC1C,SAAQC,SAAS,QAAO,WAAW;AACnC,OAAOC,OAAO,MAAM,cAAc;AAElC,eAAe,SAASC,IAAIA,CAAA,EAAG;EAC7B,IAAIC,KAAK,GAAGF,OAAO,CAAC,CAAC,CAACG,OAAO,CAACC,SAAS,CAAC;IACpCC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,YAAY,GAAGJ,KAAK,CAACL,KAAK;IAC1BU,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,IAAI;IACJC,SAAS;IACTC,KAAK,GAAG,KAAK;IACbC,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAG,CAAC;IAChBC,KAAK,GAAG,GAAG;EAEf,OAAOZ,KAAK,CAACC,OAAO;EAEpB,SAASY,OAAOA,CAAA,EAAG;IACjB,IAAIC,CAAC,GAAGX,MAAM,CAAC,CAAC,CAACY,MAAM;MACnBC,OAAO,GAAGV,EAAE,GAAGD,EAAE;MACjBY,KAAK,GAAGD,OAAO,GAAGV,EAAE,GAAGD,EAAE;MACzBa,IAAI,GAAGF,OAAO,GAAGX,EAAE,GAAGC,EAAE;IAC5BC,IAAI,GAAG,CAACW,IAAI,GAAGD,KAAK,IAAIE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,CAAC,GAAGJ,YAAY,GAAGC,YAAY,GAAG,CAAC,CAAC;IACxE,IAAIF,KAAK,EAAEF,IAAI,GAAGY,IAAI,CAACE,KAAK,CAACd,IAAI,CAAC;IAClCU,KAAK,IAAI,CAACC,IAAI,GAAGD,KAAK,GAAGV,IAAI,IAAIO,CAAC,GAAGJ,YAAY,CAAC,IAAIE,KAAK;IAC3DJ,SAAS,GAAGD,IAAI,IAAI,CAAC,GAAGG,YAAY,CAAC;IACrC,IAAID,KAAK,EAAEQ,KAAK,GAAGE,IAAI,CAACV,KAAK,CAACQ,KAAK,CAAC,EAAET,SAAS,GAAGW,IAAI,CAACV,KAAK,CAACD,SAAS,CAAC;IACvE,IAAIc,MAAM,GAAG1B,QAAQ,CAACkB,CAAC,CAAC,CAACS,GAAG,CAAC,UAASC,CAAC,EAAE;MAAE,OAAOP,KAAK,GAAGV,IAAI,GAAGiB,CAAC;IAAE,CAAC,CAAC;IACtE,OAAOpB,YAAY,CAACY,OAAO,GAAGM,MAAM,CAACN,OAAO,CAAC,CAAC,GAAGM,MAAM,CAAC;EAC1D;EAEAtB,KAAK,CAACG,MAAM,GAAG,UAASsB,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACX,MAAM,IAAIZ,MAAM,CAACsB,CAAC,CAAC,EAAEZ,OAAO,CAAC,CAAC,IAAIV,MAAM,CAAC,CAAC;EAC7D,CAAC;EAEDH,KAAK,CAACL,KAAK,GAAG,UAAS8B,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACX,MAAM,IAAI,CAACV,EAAE,EAAEC,EAAE,CAAC,GAAGmB,CAAC,EAAEpB,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,GAAG,CAACA,EAAE,EAAEO,OAAO,CAAC,CAAC,IAAI,CAACR,EAAE,EAAEC,EAAE,CAAC;EACpF,CAAC;EAEDN,KAAK,CAAC2B,UAAU,GAAG,UAASF,CAAC,EAAE;IAC7B,OAAO,CAACpB,EAAE,EAAEC,EAAE,CAAC,GAAGmB,CAAC,EAAEpB,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,GAAG,CAACA,EAAE,EAAEG,KAAK,GAAG,IAAI,EAAEI,OAAO,CAAC,CAAC;EAClE,CAAC;EAEDb,KAAK,CAACQ,SAAS,GAAG,YAAW;IAC3B,OAAOA,SAAS;EAClB,CAAC;EAEDR,KAAK,CAACO,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI;EACb,CAAC;EAEDP,KAAK,CAACS,KAAK,GAAG,UAASgB,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACX,MAAM,IAAIN,KAAK,GAAG,CAAC,CAACgB,CAAC,EAAEZ,OAAO,CAAC,CAAC,IAAIJ,KAAK;EAC5D,CAAC;EAEDT,KAAK,CAAC4B,OAAO,GAAG,UAASH,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACX,MAAM,IAAIL,YAAY,GAAGS,IAAI,CAACU,GAAG,CAAC,CAAC,EAAElB,YAAY,GAAG,CAACc,CAAC,CAAC,EAAEZ,OAAO,CAAC,CAAC,IAAIH,YAAY;EACrG,CAAC;EAEDV,KAAK,CAACU,YAAY,GAAG,UAASe,CAAC,EAAE;IAC/B,OAAOC,SAAS,CAACX,MAAM,IAAIL,YAAY,GAAGS,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,EAAEZ,OAAO,CAAC,CAAC,IAAIH,YAAY;EACrF,CAAC;EAEDV,KAAK,CAACW,YAAY,GAAG,UAASc,CAAC,EAAE;IAC/B,OAAOC,SAAS,CAACX,MAAM,IAAIJ,YAAY,GAAG,CAACc,CAAC,EAAEZ,OAAO,CAAC,CAAC,IAAIF,YAAY;EACzE,CAAC;EAEDX,KAAK,CAACY,KAAK,GAAG,UAASa,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACX,MAAM,IAAIH,KAAK,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,CAAC,EAAEZ,OAAO,CAAC,CAAC,IAAID,KAAK;EACpF,CAAC;EAEDZ,KAAK,CAAC8B,IAAI,GAAG,YAAW;IACtB,OAAO/B,IAAI,CAACI,MAAM,CAAC,CAAC,EAAE,CAACE,EAAE,EAAEC,EAAE,CAAC,CAAC,CAC1BG,KAAK,CAACA,KAAK,CAAC,CACZC,YAAY,CAACA,YAAY,CAAC,CAC1BC,YAAY,CAACA,YAAY,CAAC,CAC1BC,KAAK,CAACA,KAAK,CAAC;EACnB,CAAC;EAED,OAAOf,SAAS,CAACkC,KAAK,CAAClB,OAAO,CAAC,CAAC,EAAEa,SAAS,CAAC;AAC9C;AAEA,SAASM,QAAQA,CAAChC,KAAK,EAAE;EACvB,IAAI8B,IAAI,GAAG9B,KAAK,CAAC8B,IAAI;EAErB9B,KAAK,CAAC4B,OAAO,GAAG5B,KAAK,CAACW,YAAY;EAClC,OAAOX,KAAK,CAACU,YAAY;EACzB,OAAOV,KAAK,CAACW,YAAY;EAEzBX,KAAK,CAAC8B,IAAI,GAAG,YAAW;IACtB,OAAOE,QAAQ,CAACF,IAAI,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,OAAO9B,KAAK;AACd;AAEA,OAAO,SAASiC,KAAKA,CAAA,EAAG;EACtB,OAAOD,QAAQ,CAACjC,IAAI,CAACgC,KAAK,CAAC,IAAI,EAAEL,SAAS,CAAC,CAAChB,YAAY,CAAC,CAAC,CAAC,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}