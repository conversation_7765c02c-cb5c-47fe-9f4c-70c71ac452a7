{"ast": null, "code": "import { isArray, isPlainObject } from 'is-what';\nfunction assignProp(carry, key, newVal, originalObject, includeNonenumerable) {\n  const propType = {}.propertyIsEnumerable.call(originalObject, key) ? \"enumerable\" : \"nonenumerable\";\n  if (propType === \"enumerable\") carry[key] = newVal;\n  if (includeNonenumerable && propType === \"nonenumerable\") {\n    Object.defineProperty(carry, key, {\n      value: newVal,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    });\n  }\n}\nfunction copy(target) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (isArray(target)) {\n    return target.map(item => copy(item, options));\n  }\n  if (!isPlainObject(target)) {\n    return target;\n  }\n  const props = Object.getOwnPropertyNames(target);\n  const symbols = Object.getOwnPropertySymbols(target);\n  return [...props, ...symbols].reduce((carry, key) => {\n    if (isArray(options.props) && !options.props.includes(key)) {\n      return carry;\n    }\n    const val = target[key];\n    const newVal = copy(val, options);\n    assignProp(carry, key, newVal, target, options.nonenumerable);\n    return carry;\n  }, {});\n}\nexport { copy };", "map": {"version": 3, "names": ["isArray", "isPlainObject", "assignProp", "carry", "key", "newVal", "originalObject", "includeNonenumerable", "propType", "propertyIsEnumerable", "call", "Object", "defineProperty", "value", "enumerable", "writable", "configurable", "copy", "target", "options", "arguments", "length", "undefined", "map", "item", "props", "getOwnPropertyNames", "symbols", "getOwnPropertySymbols", "reduce", "includes", "val", "nonenumerable"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/copy-anything/dist/index.js"], "sourcesContent": ["import { isArray, isPlainObject } from 'is-what';\n\nfunction assignProp(carry, key, newVal, originalObject, includeNonenumerable) {\n  const propType = {}.propertyIsEnumerable.call(originalObject, key) ? \"enumerable\" : \"nonenumerable\";\n  if (propType === \"enumerable\")\n    carry[key] = newVal;\n  if (includeNonenumerable && propType === \"nonenumerable\") {\n    Object.defineProperty(carry, key, {\n      value: newVal,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    });\n  }\n}\nfunction copy(target, options = {}) {\n  if (isArray(target)) {\n    return target.map((item) => copy(item, options));\n  }\n  if (!isPlainObject(target)) {\n    return target;\n  }\n  const props = Object.getOwnPropertyNames(target);\n  const symbols = Object.getOwnPropertySymbols(target);\n  return [...props, ...symbols].reduce((carry, key) => {\n    if (isArray(options.props) && !options.props.includes(key)) {\n      return carry;\n    }\n    const val = target[key];\n    const newVal = copy(val, options);\n    assignProp(carry, key, newVal, target, options.nonenumerable);\n    return carry;\n  }, {});\n}\n\nexport { copy };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,aAAa,QAAQ,SAAS;AAEhD,SAASC,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,cAAc,EAAEC,oBAAoB,EAAE;EAC5E,MAAMC,QAAQ,GAAG,CAAC,CAAC,CAACC,oBAAoB,CAACC,IAAI,CAACJ,cAAc,EAAEF,GAAG,CAAC,GAAG,YAAY,GAAG,eAAe;EACnG,IAAII,QAAQ,KAAK,YAAY,EAC3BL,KAAK,CAACC,GAAG,CAAC,GAAGC,MAAM;EACrB,IAAIE,oBAAoB,IAAIC,QAAQ,KAAK,eAAe,EAAE;IACxDG,MAAM,CAACC,cAAc,CAACT,KAAK,EAAEC,GAAG,EAAE;MAChCS,KAAK,EAAER,MAAM;MACbS,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ;AACF;AACA,SAASC,IAAIA,CAACC,MAAM,EAAgB;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAChC,IAAIpB,OAAO,CAACkB,MAAM,CAAC,EAAE;IACnB,OAAOA,MAAM,CAACK,GAAG,CAAEC,IAAI,IAAKP,IAAI,CAACO,IAAI,EAAEL,OAAO,CAAC,CAAC;EAClD;EACA,IAAI,CAAClB,aAAa,CAACiB,MAAM,CAAC,EAAE;IAC1B,OAAOA,MAAM;EACf;EACA,MAAMO,KAAK,GAAGd,MAAM,CAACe,mBAAmB,CAACR,MAAM,CAAC;EAChD,MAAMS,OAAO,GAAGhB,MAAM,CAACiB,qBAAqB,CAACV,MAAM,CAAC;EACpD,OAAO,CAAC,GAAGO,KAAK,EAAE,GAAGE,OAAO,CAAC,CAACE,MAAM,CAAC,CAAC1B,KAAK,EAAEC,GAAG,KAAK;IACnD,IAAIJ,OAAO,CAACmB,OAAO,CAACM,KAAK,CAAC,IAAI,CAACN,OAAO,CAACM,KAAK,CAACK,QAAQ,CAAC1B,GAAG,CAAC,EAAE;MAC1D,OAAOD,KAAK;IACd;IACA,MAAM4B,GAAG,GAAGb,MAAM,CAACd,GAAG,CAAC;IACvB,MAAMC,MAAM,GAAGY,IAAI,CAACc,GAAG,EAAEZ,OAAO,CAAC;IACjCjB,UAAU,CAACC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEa,MAAM,EAAEC,OAAO,CAACa,aAAa,CAAC;IAC7D,OAAO7B,KAAK;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AAEA,SAASc,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}