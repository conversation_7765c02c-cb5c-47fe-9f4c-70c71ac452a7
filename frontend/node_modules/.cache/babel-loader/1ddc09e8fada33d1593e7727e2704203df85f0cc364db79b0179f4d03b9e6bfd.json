{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\n\n/**\n * This is the data that's coming through main chart `data` prop\n * Recharts is very flexible in what it accepts so the type is very flexible too.\n * This will typically be an object, and various components will provide various `dataKey`\n * that dictates how to pull data from that object.\n *\n * TL;DR: before dataKey\n */\n\n/**\n * So this is the same unknown type as ChartData but this is after the dataKey has been applied.\n * We still don't know what the type is - that depends on what exactly it was before the dataKey application,\n * and the dataKey can return whatever anyway - but let's keep it separate as a form of documentation.\n *\n * TL;DR: ChartData after dataKey.\n */\n\nexport var initialChartDataState = {\n  chartData: undefined,\n  computedData: undefined,\n  dataStartIndex: 0,\n  dataEndIndex: 0\n};\nvar chartDataSlice = createSlice({\n  name: 'chartData',\n  initialState: initialChartDataState,\n  reducers: {\n    setChartData(state, action) {\n      state.chartData = action.payload;\n      if (action.payload == null) {\n        state.dataStartIndex = 0;\n        state.dataEndIndex = 0;\n        return;\n      }\n      if (action.payload.length > 0 && state.dataEndIndex !== action.payload.length - 1) {\n        state.dataEndIndex = action.payload.length - 1;\n      }\n    },\n    setComputedData(state, action) {\n      state.computedData = action.payload;\n    },\n    setDataStartEndIndexes(state, action) {\n      var {\n        startIndex,\n        endIndex\n      } = action.payload;\n      if (startIndex != null) {\n        state.dataStartIndex = startIndex;\n      }\n      if (endIndex != null) {\n        state.dataEndIndex = endIndex;\n      }\n    }\n  }\n});\nexport var {\n  setChartData,\n  setDataStartEndIndexes,\n  setComputedData\n} = chartDataSlice.actions;\nexport var chartDataReducer = chartDataSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialChartDataState", "chartData", "undefined", "computedData", "dataStartIndex", "dataEndIndex", "chartDataSlice", "name", "initialState", "reducers", "setChartData", "state", "action", "payload", "length", "setComputedData", "setDataStartEndIndexes", "startIndex", "endIndex", "actions", "chartDataReducer", "reducer"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/recharts/es6/state/chartDataSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\n\n/**\n * This is the data that's coming through main chart `data` prop\n * Recharts is very flexible in what it accepts so the type is very flexible too.\n * This will typically be an object, and various components will provide various `dataKey`\n * that dictates how to pull data from that object.\n *\n * TL;DR: before dataKey\n */\n\n/**\n * So this is the same unknown type as ChartData but this is after the dataKey has been applied.\n * We still don't know what the type is - that depends on what exactly it was before the dataKey application,\n * and the dataKey can return whatever anyway - but let's keep it separate as a form of documentation.\n *\n * TL;DR: ChartData after dataKey.\n */\n\nexport var initialChartDataState = {\n  chartData: undefined,\n  computedData: undefined,\n  dataStartIndex: 0,\n  dataEndIndex: 0\n};\nvar chartDataSlice = createSlice({\n  name: 'chartData',\n  initialState: initialChartDataState,\n  reducers: {\n    setChartData(state, action) {\n      state.chartData = action.payload;\n      if (action.payload == null) {\n        state.dataStartIndex = 0;\n        state.dataEndIndex = 0;\n        return;\n      }\n      if (action.payload.length > 0 && state.dataEndIndex !== action.payload.length - 1) {\n        state.dataEndIndex = action.payload.length - 1;\n      }\n    },\n    setComputedData(state, action) {\n      state.computedData = action.payload;\n    },\n    setDataStartEndIndexes(state, action) {\n      var {\n        startIndex,\n        endIndex\n      } = action.payload;\n      if (startIndex != null) {\n        state.dataStartIndex = startIndex;\n      }\n      if (endIndex != null) {\n        state.dataEndIndex = endIndex;\n      }\n    }\n  }\n});\nexport var {\n  setChartData,\n  setDataStartEndIndexes,\n  setComputedData\n} = chartDataSlice.actions;\nexport var chartDataReducer = chartDataSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,IAAIC,qBAAqB,GAAG;EACjCC,SAAS,EAAEC,SAAS;EACpBC,YAAY,EAAED,SAAS;EACvBE,cAAc,EAAE,CAAC;EACjBC,YAAY,EAAE;AAChB,CAAC;AACD,IAAIC,cAAc,GAAGP,WAAW,CAAC;EAC/BQ,IAAI,EAAE,WAAW;EACjBC,YAAY,EAAER,qBAAqB;EACnCS,QAAQ,EAAE;IACRC,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAAE;MAC1BD,KAAK,CAACV,SAAS,GAAGW,MAAM,CAACC,OAAO;MAChC,IAAID,MAAM,CAACC,OAAO,IAAI,IAAI,EAAE;QAC1BF,KAAK,CAACP,cAAc,GAAG,CAAC;QACxBO,KAAK,CAACN,YAAY,GAAG,CAAC;QACtB;MACF;MACA,IAAIO,MAAM,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC,IAAIH,KAAK,CAACN,YAAY,KAAKO,MAAM,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;QACjFH,KAAK,CAACN,YAAY,GAAGO,MAAM,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC;MAChD;IACF,CAAC;IACDC,eAAeA,CAACJ,KAAK,EAAEC,MAAM,EAAE;MAC7BD,KAAK,CAACR,YAAY,GAAGS,MAAM,CAACC,OAAO;IACrC,CAAC;IACDG,sBAAsBA,CAACL,KAAK,EAAEC,MAAM,EAAE;MACpC,IAAI;QACFK,UAAU;QACVC;MACF,CAAC,GAAGN,MAAM,CAACC,OAAO;MAClB,IAAII,UAAU,IAAI,IAAI,EAAE;QACtBN,KAAK,CAACP,cAAc,GAAGa,UAAU;MACnC;MACA,IAAIC,QAAQ,IAAI,IAAI,EAAE;QACpBP,KAAK,CAACN,YAAY,GAAGa,QAAQ;MAC/B;IACF;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAI;EACTR,YAAY;EACZM,sBAAsB;EACtBD;AACF,CAAC,GAAGT,cAAc,CAACa,OAAO;AAC1B,OAAO,IAAIC,gBAAgB,GAAGd,cAAc,CAACe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}