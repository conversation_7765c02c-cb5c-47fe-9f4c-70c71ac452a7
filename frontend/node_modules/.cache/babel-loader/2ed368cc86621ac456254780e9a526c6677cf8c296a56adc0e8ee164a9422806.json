{"ast": null, "code": "import cloneArrayBuffer from './_cloneArrayBuffer.js';\n\n/**\n * Creates a clone of `dataView`.\n *\n * @private\n * @param {Object} dataView The data view to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned data view.\n */\nfunction cloneDataView(dataView, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(dataView.buffer) : dataView.buffer;\n  return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);\n}\nexport default cloneDataView;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cloneDataView", "dataView", "isDeep", "buffer", "constructor", "byteOffset", "byteLength"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/lodash-es/_cloneDataView.js"], "sourcesContent": ["import cloneArrayBuffer from './_cloneArrayBuffer.js';\n\n/**\n * Creates a clone of `dataView`.\n *\n * @private\n * @param {Object} dataView The data view to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned data view.\n */\nfunction cloneDataView(dataView, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(dataView.buffer) : dataView.buffer;\n  return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);\n}\n\nexport default cloneDataView;\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EACvC,IAAIC,MAAM,GAAGD,MAAM,GAAGH,gBAAgB,CAACE,QAAQ,CAACE,MAAM,CAAC,GAAGF,QAAQ,CAACE,MAAM;EACzE,OAAO,IAAIF,QAAQ,CAACG,WAAW,CAACD,MAAM,EAAEF,QAAQ,CAACI,UAAU,EAAEJ,QAAQ,CAACK,UAAU,CAAC;AACnF;AAEA,eAAeN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}