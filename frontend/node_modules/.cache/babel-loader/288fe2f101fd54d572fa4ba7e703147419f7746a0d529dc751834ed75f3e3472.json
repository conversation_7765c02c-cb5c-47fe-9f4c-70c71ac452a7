{"ast": null, "code": "import { isMap, isArray, isPlainObject, isSet } from './is';\nimport { includes } from './util';\nvar getNthKey = function (value, n) {\n  var keys = value.keys();\n  while (n > 0) {\n    keys.next();\n    n--;\n  }\n  return keys.next().value;\n};\nfunction validatePath(path) {\n  if (includes(path, '__proto__')) {\n    throw new Error('__proto__ is not allowed as a property');\n  }\n  if (includes(path, 'prototype')) {\n    throw new Error('prototype is not allowed as a property');\n  }\n  if (includes(path, 'constructor')) {\n    throw new Error('constructor is not allowed as a property');\n  }\n}\nexport var getDeep = function (object, path) {\n  validatePath(path);\n  for (var i = 0; i < path.length; i++) {\n    var key = path[i];\n    if (isSet(object)) {\n      object = getNthKey(object, +key);\n    } else if (isMap(object)) {\n      var row = +key;\n      var type = +path[++i] === 0 ? 'key' : 'value';\n      var keyOfRow = getNthKey(object, row);\n      switch (type) {\n        case 'key':\n          object = keyOfRow;\n          break;\n        case 'value':\n          object = object.get(keyOfRow);\n          break;\n      }\n    } else {\n      object = object[key];\n    }\n  }\n  return object;\n};\nexport var setDeep = function (object, path, mapper) {\n  validatePath(path);\n  if (path.length === 0) {\n    return mapper(object);\n  }\n  var parent = object;\n  for (var i = 0; i < path.length - 1; i++) {\n    var key = path[i];\n    if (isArray(parent)) {\n      var index = +key;\n      parent = parent[index];\n    } else if (isPlainObject(parent)) {\n      parent = parent[key];\n    } else if (isSet(parent)) {\n      var row = +key;\n      parent = getNthKey(parent, row);\n    } else if (isMap(parent)) {\n      var isEnd = i === path.length - 2;\n      if (isEnd) {\n        break;\n      }\n      var row = +key;\n      var type = +path[++i] === 0 ? 'key' : 'value';\n      var keyOfRow = getNthKey(parent, row);\n      switch (type) {\n        case 'key':\n          parent = keyOfRow;\n          break;\n        case 'value':\n          parent = parent.get(keyOfRow);\n          break;\n      }\n    }\n  }\n  var lastKey = path[path.length - 1];\n  if (isArray(parent)) {\n    parent[+lastKey] = mapper(parent[+lastKey]);\n  } else if (isPlainObject(parent)) {\n    parent[lastKey] = mapper(parent[lastKey]);\n  }\n  if (isSet(parent)) {\n    var oldValue = getNthKey(parent, +lastKey);\n    var newValue = mapper(oldValue);\n    if (oldValue !== newValue) {\n      parent[\"delete\"](oldValue);\n      parent.add(newValue);\n    }\n  }\n  if (isMap(parent)) {\n    var row = +path[path.length - 2];\n    var keyToRow = getNthKey(parent, row);\n    var type = +lastKey === 0 ? 'key' : 'value';\n    switch (type) {\n      case 'key':\n        {\n          var newKey = mapper(keyToRow);\n          parent.set(newKey, parent.get(keyToRow));\n          if (newKey !== keyToRow) {\n            parent[\"delete\"](keyToRow);\n          }\n          break;\n        }\n      case 'value':\n        {\n          parent.set(keyToRow, mapper(parent.get(keyToRow)));\n          break;\n        }\n    }\n  }\n  return object;\n};", "map": {"version": 3, "names": ["isMap", "isArray", "isPlainObject", "isSet", "includes", "getNthKey", "value", "n", "keys", "next", "validatePath", "path", "Error", "getDeep", "object", "i", "length", "key", "row", "type", "keyOfRow", "get", "setDeep", "mapper", "parent", "index", "isEnd", "last<PERSON>ey", "oldValue", "newValue", "add", "keyToRow", "new<PERSON>ey", "set"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/superjson/src/accessDeep.ts"], "sourcesContent": ["import { isMap, isArray, isPlainObject, isSet } from './is';\nimport { includes } from './util';\n\nconst getNthKey = (value: Map<any, any> | Set<any>, n: number): any => {\n  const keys = value.keys();\n  while (n > 0) {\n    keys.next();\n    n--;\n  }\n\n  return keys.next().value;\n};\n\nfunction validatePath(path: (string | number)[]) {\n  if (includes(path, '__proto__')) {\n    throw new Error('__proto__ is not allowed as a property');\n  }\n  if (includes(path, 'prototype')) {\n    throw new Error('prototype is not allowed as a property');\n  }\n  if (includes(path, 'constructor')) {\n    throw new Error('constructor is not allowed as a property');\n  }\n}\n\nexport const getDeep = (object: object, path: (string | number)[]): object => {\n  validatePath(path);\n\n  for (let i = 0; i < path.length; i++) {\n    const key = path[i];\n    if (isSet(object)) {\n      object = getNthKey(object, +key);\n    } else if (isMap(object)) {\n      const row = +key;\n      const type = +path[++i] === 0 ? 'key' : 'value';\n\n      const keyOfRow = getNthKey(object, row);\n      switch (type) {\n        case 'key':\n          object = keyOfRow;\n          break;\n        case 'value':\n          object = object.get(keyOfRow);\n          break;\n      }\n    } else {\n      object = (object as any)[key];\n    }\n  }\n\n  return object;\n};\n\nexport const setDeep = (\n  object: any,\n  path: (string | number)[],\n  mapper: (v: any) => any\n): any => {\n  validatePath(path);\n\n  if (path.length === 0) {\n    return mapper(object);\n  }\n\n  let parent = object;\n\n  for (let i = 0; i < path.length - 1; i++) {\n    const key = path[i];\n\n    if (isArray(parent)) {\n      const index = +key;\n      parent = parent[index];\n    } else if (isPlainObject(parent)) {\n      parent = parent[key];\n    } else if (isSet(parent)) {\n      const row = +key;\n      parent = getNthKey(parent, row);\n    } else if (isMap(parent)) {\n      const isEnd = i === path.length - 2;\n      if (isEnd) {\n        break;\n      }\n\n      const row = +key;\n      const type = +path[++i] === 0 ? 'key' : 'value';\n\n      const keyOfRow = getNthKey(parent, row);\n      switch (type) {\n        case 'key':\n          parent = keyOfRow;\n          break;\n        case 'value':\n          parent = parent.get(keyOfRow);\n          break;\n      }\n    }\n  }\n\n  const lastKey = path[path.length - 1];\n\n  if (isArray(parent)) {\n    parent[+lastKey] = mapper(parent[+lastKey]);\n  } else if (isPlainObject(parent)) {\n    parent[lastKey] = mapper(parent[lastKey]);\n  }\n\n  if (isSet(parent)) {\n    const oldValue = getNthKey(parent, +lastKey);\n    const newValue = mapper(oldValue);\n    if (oldValue !== newValue) {\n      parent.delete(oldValue);\n      parent.add(newValue);\n    }\n  }\n\n  if (isMap(parent)) {\n    const row = +path[path.length - 2];\n    const keyToRow = getNthKey(parent, row);\n\n    const type = +lastKey === 0 ? 'key' : 'value';\n    switch (type) {\n      case 'key': {\n        const newKey = mapper(keyToRow);\n        parent.set(newKey, parent.get(keyToRow));\n\n        if (newKey !== keyToRow) {\n          parent.delete(keyToRow);\n        }\n        break;\n      }\n\n      case 'value': {\n        parent.set(keyToRow, mapper(parent.get(keyToRow)));\n        break;\n      }\n    }\n  }\n\n  return object;\n};\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,OAAO,EAAEC,aAAa,EAAEC,KAAK,QAAQ,MAAM;AAC3D,SAASC,QAAQ,QAAQ,QAAQ;AAEjC,IAAMC,SAAS,GAAG,SAAAA,CAACC,KAA+B,EAAEC,CAAS;EAC3D,IAAMC,IAAI,GAAGF,KAAK,CAACE,IAAI,EAAE;EACzB,OAAOD,CAAC,GAAG,CAAC,EAAE;IACZC,IAAI,CAACC,IAAI,EAAE;IACXF,CAAC,EAAE;;EAGL,OAAOC,IAAI,CAACC,IAAI,EAAE,CAACH,KAAK;AAC1B,CAAC;AAED,SAASI,YAAYA,CAACC,IAAyB;EAC7C,IAAIP,QAAQ,CAACO,IAAI,EAAE,WAAW,CAAC,EAAE;IAC/B,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;;EAE3D,IAAIR,QAAQ,CAACO,IAAI,EAAE,WAAW,CAAC,EAAE;IAC/B,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;;EAE3D,IAAIR,QAAQ,CAACO,IAAI,EAAE,aAAa,CAAC,EAAE;IACjC,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;;AAE/D;AAEA,OAAO,IAAMC,OAAO,GAAG,SAAAA,CAACC,MAAc,EAAEH,IAAyB;EAC/DD,YAAY,CAACC,IAAI,CAAC;EAElB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAME,GAAG,GAAGN,IAAI,CAACI,CAAC,CAAC;IACnB,IAAIZ,KAAK,CAACW,MAAM,CAAC,EAAE;MACjBA,MAAM,GAAGT,SAAS,CAACS,MAAM,EAAE,CAACG,GAAG,CAAC;KACjC,MAAM,IAAIjB,KAAK,CAACc,MAAM,CAAC,EAAE;MACxB,IAAMI,GAAG,GAAG,CAACD,GAAG;MAChB,IAAME,IAAI,GAAG,CAACR,IAAI,CAAC,EAAEI,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO;MAE/C,IAAMK,QAAQ,GAAGf,SAAS,CAACS,MAAM,EAAEI,GAAG,CAAC;MACvC,QAAQC,IAAI;QACV,KAAK,KAAK;UACRL,MAAM,GAAGM,QAAQ;UACjB;QACF,KAAK,OAAO;UACVN,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACD,QAAQ,CAAC;UAC7B;;KAEL,MAAM;MACLN,MAAM,GAAIA,MAAc,CAACG,GAAG,CAAC;;;EAIjC,OAAOH,MAAM;AACf,CAAC;AAED,OAAO,IAAMQ,OAAO,GAAG,SAAAA,CACrBR,MAAW,EACXH,IAAyB,EACzBY,MAAuB;EAEvBb,YAAY,CAACC,IAAI,CAAC;EAElB,IAAIA,IAAI,CAACK,MAAM,KAAK,CAAC,EAAE;IACrB,OAAOO,MAAM,CAACT,MAAM,CAAC;;EAGvB,IAAIU,MAAM,GAAGV,MAAM;EAEnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACK,MAAM,GAAG,CAAC,EAAED,CAAC,EAAE,EAAE;IACxC,IAAME,GAAG,GAAGN,IAAI,CAACI,CAAC,CAAC;IAEnB,IAAId,OAAO,CAACuB,MAAM,CAAC,EAAE;MACnB,IAAMC,KAAK,GAAG,CAACR,GAAG;MAClBO,MAAM,GAAGA,MAAM,CAACC,KAAK,CAAC;KACvB,MAAM,IAAIvB,aAAa,CAACsB,MAAM,CAAC,EAAE;MAChCA,MAAM,GAAGA,MAAM,CAACP,GAAG,CAAC;KACrB,MAAM,IAAId,KAAK,CAACqB,MAAM,CAAC,EAAE;MACxB,IAAMN,GAAG,GAAG,CAACD,GAAG;MAChBO,MAAM,GAAGnB,SAAS,CAACmB,MAAM,EAAEN,GAAG,CAAC;KAChC,MAAM,IAAIlB,KAAK,CAACwB,MAAM,CAAC,EAAE;MACxB,IAAME,KAAK,GAAGX,CAAC,KAAKJ,IAAI,CAACK,MAAM,GAAG,CAAC;MACnC,IAAIU,KAAK,EAAE;QACT;;MAGF,IAAMR,GAAG,GAAG,CAACD,GAAG;MAChB,IAAME,IAAI,GAAG,CAACR,IAAI,CAAC,EAAEI,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO;MAE/C,IAAMK,QAAQ,GAAGf,SAAS,CAACmB,MAAM,EAAEN,GAAG,CAAC;MACvC,QAAQC,IAAI;QACV,KAAK,KAAK;UACRK,MAAM,GAAGJ,QAAQ;UACjB;QACF,KAAK,OAAO;UACVI,MAAM,GAAGA,MAAM,CAACH,GAAG,CAACD,QAAQ,CAAC;UAC7B;;;;EAKR,IAAMO,OAAO,GAAGhB,IAAI,CAACA,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC;EAErC,IAAIf,OAAO,CAACuB,MAAM,CAAC,EAAE;IACnBA,MAAM,CAAC,CAACG,OAAO,CAAC,GAAGJ,MAAM,CAACC,MAAM,CAAC,CAACG,OAAO,CAAC,CAAC;GAC5C,MAAM,IAAIzB,aAAa,CAACsB,MAAM,CAAC,EAAE;IAChCA,MAAM,CAACG,OAAO,CAAC,GAAGJ,MAAM,CAACC,MAAM,CAACG,OAAO,CAAC,CAAC;;EAG3C,IAAIxB,KAAK,CAACqB,MAAM,CAAC,EAAE;IACjB,IAAMI,QAAQ,GAAGvB,SAAS,CAACmB,MAAM,EAAE,CAACG,OAAO,CAAC;IAC5C,IAAME,QAAQ,GAAGN,MAAM,CAACK,QAAQ,CAAC;IACjC,IAAIA,QAAQ,KAAKC,QAAQ,EAAE;MACzBL,MAAM,CAAC,QAAM,EAACI,QAAQ,CAAC;MACvBJ,MAAM,CAACM,GAAG,CAACD,QAAQ,CAAC;;;EAIxB,IAAI7B,KAAK,CAACwB,MAAM,CAAC,EAAE;IACjB,IAAMN,GAAG,GAAG,CAACP,IAAI,CAACA,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC;IAClC,IAAMe,QAAQ,GAAG1B,SAAS,CAACmB,MAAM,EAAEN,GAAG,CAAC;IAEvC,IAAMC,IAAI,GAAG,CAACQ,OAAO,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO;IAC7C,QAAQR,IAAI;MACV,KAAK,KAAK;QAAE;UACV,IAAMa,MAAM,GAAGT,MAAM,CAACQ,QAAQ,CAAC;UAC/BP,MAAM,CAACS,GAAG,CAACD,MAAM,EAAER,MAAM,CAACH,GAAG,CAACU,QAAQ,CAAC,CAAC;UAExC,IAAIC,MAAM,KAAKD,QAAQ,EAAE;YACvBP,MAAM,CAAC,QAAM,EAACO,QAAQ,CAAC;;UAEzB;;MAGF,KAAK,OAAO;QAAE;UACZP,MAAM,CAACS,GAAG,CAACF,QAAQ,EAAER,MAAM,CAACC,MAAM,CAACH,GAAG,CAACU,QAAQ,CAAC,CAAC,CAAC;UAClD;;;;EAKN,OAAOjB,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}