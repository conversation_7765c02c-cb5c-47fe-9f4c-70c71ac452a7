{"ast": null, "code": "import { ascendingDefined, compareDefined } from \"./sort.js\";\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nexport default function quickselect(array, k) {\n  let left = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  let right = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : Infinity;\n  let compare = arguments.length > 4 ? arguments[4] : undefined;\n  k = Math.floor(k);\n  left = Math.floor(Math.max(0, left));\n  right = Math.floor(Math.min(array.length - 1, right));\n  if (!(left <= k && k <= right)) return array;\n  compare = compare === undefined ? ascendingDefined : compareDefined(compare);\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n    const t = array[k];\n    let i = left;\n    let j = right;\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n    if (compare(array[left], t) === 0) swap(array, left, j);else ++j, swap(array, j, right);\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n  return array;\n}\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}", "map": {"version": 3, "names": ["ascendingDefined", "compareDefined", "quickselect", "array", "k", "left", "arguments", "length", "undefined", "right", "Infinity", "compare", "Math", "floor", "max", "min", "n", "m", "z", "log", "s", "exp", "sd", "sqrt", "newLeft", "newRight", "t", "i", "j", "swap"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-array/src/quickselect.js"], "sourcesContent": ["import {ascendingDefined, compareDefined} from \"./sort.js\";\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nexport default function quickselect(array, k, left = 0, right = Infinity, compare) {\n  k = Math.floor(k);\n  left = Math.floor(Math.max(0, left));\n  right = Math.floor(Math.min(array.length - 1, right));\n\n  if (!(left <= k && k <= right)) return array;\n\n  compare = compare === undefined ? ascendingDefined : compareDefined(compare);\n\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);\n    else ++j, swap(array, j, right);\n\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}\n"], "mappings": "AAAA,SAAQA,gBAAgB,EAAEC,cAAc,QAAO,WAAW;;AAE1D;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAEC,CAAC,EAAuC;EAAA,IAArCC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,KAAK,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGI,QAAQ;EAAA,IAAEC,OAAO,GAAAL,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAC/EJ,CAAC,GAAGQ,IAAI,CAACC,KAAK,CAACT,CAAC,CAAC;EACjBC,IAAI,GAAGO,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAET,IAAI,CAAC,CAAC;EACpCI,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACG,GAAG,CAACZ,KAAK,CAACI,MAAM,GAAG,CAAC,EAAEE,KAAK,CAAC,CAAC;EAErD,IAAI,EAAEJ,IAAI,IAAID,CAAC,IAAIA,CAAC,IAAIK,KAAK,CAAC,EAAE,OAAON,KAAK;EAE5CQ,OAAO,GAAGA,OAAO,KAAKH,SAAS,GAAGR,gBAAgB,GAAGC,cAAc,CAACU,OAAO,CAAC;EAE5E,OAAOF,KAAK,GAAGJ,IAAI,EAAE;IACnB,IAAII,KAAK,GAAGJ,IAAI,GAAG,GAAG,EAAE;MACtB,MAAMW,CAAC,GAAGP,KAAK,GAAGJ,IAAI,GAAG,CAAC;MAC1B,MAAMY,CAAC,GAAGb,CAAC,GAAGC,IAAI,GAAG,CAAC;MACtB,MAAMa,CAAC,GAAGN,IAAI,CAACO,GAAG,CAACH,CAAC,CAAC;MACrB,MAAMI,CAAC,GAAG,GAAG,GAAGR,IAAI,CAACS,GAAG,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,CAAC;MACnC,MAAMI,EAAE,GAAG,GAAG,GAAGV,IAAI,CAACW,IAAI,CAACL,CAAC,GAAGE,CAAC,IAAIJ,CAAC,GAAGI,CAAC,CAAC,GAAGJ,CAAC,CAAC,IAAIC,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1E,MAAMQ,OAAO,GAAGZ,IAAI,CAACE,GAAG,CAACT,IAAI,EAAEO,IAAI,CAACC,KAAK,CAACT,CAAC,GAAGa,CAAC,GAAGG,CAAC,GAAGJ,CAAC,GAAGM,EAAE,CAAC,CAAC;MAC9D,MAAMG,QAAQ,GAAGb,IAAI,CAACG,GAAG,CAACN,KAAK,EAAEG,IAAI,CAACC,KAAK,CAACT,CAAC,GAAG,CAACY,CAAC,GAAGC,CAAC,IAAIG,CAAC,GAAGJ,CAAC,GAAGM,EAAE,CAAC,CAAC;MACtEpB,WAAW,CAACC,KAAK,EAAEC,CAAC,EAAEoB,OAAO,EAAEC,QAAQ,EAAEd,OAAO,CAAC;IACnD;IAEA,MAAMe,CAAC,GAAGvB,KAAK,CAACC,CAAC,CAAC;IAClB,IAAIuB,CAAC,GAAGtB,IAAI;IACZ,IAAIuB,CAAC,GAAGnB,KAAK;IAEboB,IAAI,CAAC1B,KAAK,EAAEE,IAAI,EAAED,CAAC,CAAC;IACpB,IAAIO,OAAO,CAACR,KAAK,CAACM,KAAK,CAAC,EAAEiB,CAAC,CAAC,GAAG,CAAC,EAAEG,IAAI,CAAC1B,KAAK,EAAEE,IAAI,EAAEI,KAAK,CAAC;IAE1D,OAAOkB,CAAC,GAAGC,CAAC,EAAE;MACZC,IAAI,CAAC1B,KAAK,EAAEwB,CAAC,EAAEC,CAAC,CAAC,EAAE,EAAED,CAAC,EAAE,EAAEC,CAAC;MAC3B,OAAOjB,OAAO,CAACR,KAAK,CAACwB,CAAC,CAAC,EAAED,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEC,CAAC;MACpC,OAAOhB,OAAO,CAACR,KAAK,CAACyB,CAAC,CAAC,EAAEF,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEE,CAAC;IACtC;IAEA,IAAIjB,OAAO,CAACR,KAAK,CAACE,IAAI,CAAC,EAAEqB,CAAC,CAAC,KAAK,CAAC,EAAEG,IAAI,CAAC1B,KAAK,EAAEE,IAAI,EAAEuB,CAAC,CAAC,CAAC,KACnD,EAAEA,CAAC,EAAEC,IAAI,CAAC1B,KAAK,EAAEyB,CAAC,EAAEnB,KAAK,CAAC;IAE/B,IAAImB,CAAC,IAAIxB,CAAC,EAAEC,IAAI,GAAGuB,CAAC,GAAG,CAAC;IACxB,IAAIxB,CAAC,IAAIwB,CAAC,EAAEnB,KAAK,GAAGmB,CAAC,GAAG,CAAC;EAC3B;EAEA,OAAOzB,KAAK;AACd;AAEA,SAAS0B,IAAIA,CAAC1B,KAAK,EAAEwB,CAAC,EAAEC,CAAC,EAAE;EACzB,MAAMF,CAAC,GAAGvB,KAAK,CAACwB,CAAC,CAAC;EAClBxB,KAAK,CAACwB,CAAC,CAAC,GAAGxB,KAAK,CAACyB,CAAC,CAAC;EACnBzB,KAAK,CAACyB,CAAC,CAAC,GAAGF,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}