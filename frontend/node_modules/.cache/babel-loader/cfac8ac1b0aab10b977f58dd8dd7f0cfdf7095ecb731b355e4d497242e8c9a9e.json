{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\n// src/utils/formatProdErrorMessage.ts\nfunction formatProdErrorMessage(code) {\n  return \"Minified Redux error #\".concat(code, \"; visit https://redux.js.org/Errors?code=\").concat(code, \" for the full message or use the non-minified dev environment for full errors. \");\n}\n\n// src/utils/symbol-observable.ts\nvar $$observable = /* @__PURE__ */(() => typeof Symbol === \"function\" && Symbol.observable || \"@@observable\")();\nvar symbol_observable_default = $$observable;\n\n// src/utils/actionTypes.ts\nvar randomString = () => Math.random().toString(36).substring(7).split(\"\").join(\".\");\nvar ActionTypes = {\n  INIT: \"@@redux/INIT\".concat(/* @__PURE__ */randomString()),\n  REPLACE: \"@@redux/REPLACE\".concat(/* @__PURE__ */randomString()),\n  PROBE_UNKNOWN_ACTION: () => \"@@redux/PROBE_UNKNOWN_ACTION\".concat(randomString())\n};\nvar actionTypes_default = ActionTypes;\n\n// src/utils/isPlainObject.ts\nfunction isPlainObject(obj) {\n  if (typeof obj !== \"object\" || obj === null) return false;\n  let proto = obj;\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Object.getPrototypeOf(obj) === proto || Object.getPrototypeOf(obj) === null;\n}\n\n// src/utils/kindOf.ts\nfunction miniKindOf(val) {\n  if (val === void 0) return \"undefined\";\n  if (val === null) return \"null\";\n  const type = typeof val;\n  switch (type) {\n    case \"boolean\":\n    case \"string\":\n    case \"number\":\n    case \"symbol\":\n    case \"function\":\n      {\n        return type;\n      }\n  }\n  if (Array.isArray(val)) return \"array\";\n  if (isDate(val)) return \"date\";\n  if (isError(val)) return \"error\";\n  const constructorName = ctorName(val);\n  switch (constructorName) {\n    case \"Symbol\":\n    case \"Promise\":\n    case \"WeakMap\":\n    case \"WeakSet\":\n    case \"Map\":\n    case \"Set\":\n      return constructorName;\n  }\n  return Object.prototype.toString.call(val).slice(8, -1).toLowerCase().replace(/\\s/g, \"\");\n}\nfunction ctorName(val) {\n  return typeof val.constructor === \"function\" ? val.constructor.name : null;\n}\nfunction isError(val) {\n  return val instanceof Error || typeof val.message === \"string\" && val.constructor && typeof val.constructor.stackTraceLimit === \"number\";\n}\nfunction isDate(val) {\n  if (val instanceof Date) return true;\n  return typeof val.toDateString === \"function\" && typeof val.getDate === \"function\" && typeof val.setDate === \"function\";\n}\nfunction kindOf(val) {\n  let typeOfVal = typeof val;\n  if (process.env.NODE_ENV !== \"production\") {\n    typeOfVal = miniKindOf(val);\n  }\n  return typeOfVal;\n}\n\n// src/createStore.ts\nfunction createStore(reducer, preloadedState, enhancer) {\n  if (typeof reducer !== \"function\") {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(2) : \"Expected the root reducer to be a function. Instead, received: '\".concat(kindOf(reducer), \"'\"));\n  }\n  if (typeof preloadedState === \"function\" && typeof enhancer === \"function\" || typeof enhancer === \"function\" && typeof arguments[3] === \"function\") {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(0) : \"It looks like you are passing several store enhancers to createStore(). This is not supported. Instead, compose them together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.\");\n  }\n  if (typeof preloadedState === \"function\" && typeof enhancer === \"undefined\") {\n    enhancer = preloadedState;\n    preloadedState = void 0;\n  }\n  if (typeof enhancer !== \"undefined\") {\n    if (typeof enhancer !== \"function\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(1) : \"Expected the enhancer to be a function. Instead, received: '\".concat(kindOf(enhancer), \"'\"));\n    }\n    return enhancer(createStore)(reducer, preloadedState);\n  }\n  let currentReducer = reducer;\n  let currentState = preloadedState;\n  let currentListeners = /* @__PURE__ */new Map();\n  let nextListeners = currentListeners;\n  let listenerIdCounter = 0;\n  let isDispatching = false;\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = /* @__PURE__ */new Map();\n      currentListeners.forEach((listener, key) => {\n        nextListeners.set(key, listener);\n      });\n    }\n  }\n  function getState() {\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(3) : \"You may not call store.getState() while the reducer is executing. The reducer has already received the state as an argument. Pass it down from the top reducer instead of reading it from the store.\");\n    }\n    return currentState;\n  }\n  function subscribe(listener) {\n    if (typeof listener !== \"function\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(4) : \"Expected the listener to be a function. Instead, received: '\".concat(kindOf(listener), \"'\"));\n    }\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(5) : \"You may not call store.subscribe() while the reducer is executing. If you would like to be notified after the store has been updated, subscribe from a component and invoke store.getState() in the callback to access the latest state. See https://redux.js.org/api/store#subscribelistener for more details.\");\n    }\n    let isSubscribed = true;\n    ensureCanMutateNextListeners();\n    const listenerId = listenerIdCounter++;\n    nextListeners.set(listenerId, listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n      if (isDispatching) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(6) : \"You may not unsubscribe from a store listener while the reducer is executing. See https://redux.js.org/api/store#subscribelistener for more details.\");\n      }\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      nextListeners.delete(listenerId);\n      currentListeners = null;\n    };\n  }\n  function dispatch(action) {\n    if (!isPlainObject(action)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(7) : \"Actions must be plain objects. Instead, the actual type was: '\".concat(kindOf(action), \"'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.\"));\n    }\n    if (typeof action.type === \"undefined\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(8) : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n    }\n    if (typeof action.type !== \"string\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(17) : \"Action \\\"type\\\" property must be a string. Instead, the actual type was: '\".concat(kindOf(action.type), \"'. Value was: '\").concat(action.type, \"' (stringified)\"));\n    }\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(9) : \"Reducers may not dispatch actions.\");\n    }\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n    const listeners = currentListeners = nextListeners;\n    listeners.forEach(listener => {\n      listener();\n    });\n    return action;\n  }\n  function replaceReducer(nextReducer) {\n    if (typeof nextReducer !== \"function\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(10) : \"Expected the nextReducer to be a function. Instead, received: '\".concat(kindOf(nextReducer)));\n    }\n    currentReducer = nextReducer;\n    dispatch({\n      type: actionTypes_default.REPLACE\n    });\n  }\n  function observable() {\n    const outerSubscribe = subscribe;\n    return {\n      /**\n       * The minimal observable subscription method.\n       * @param observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe(observer) {\n        if (typeof observer !== \"object\" || observer === null) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(11) : \"Expected the observer to be an object. Instead, received: '\".concat(kindOf(observer), \"'\"));\n        }\n        function observeState() {\n          const observerAsObserver = observer;\n          if (observerAsObserver.next) {\n            observerAsObserver.next(getState());\n          }\n        }\n        observeState();\n        const unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe\n        };\n      },\n      [symbol_observable_default]() {\n        return this;\n      }\n    };\n  }\n  dispatch({\n    type: actionTypes_default.INIT\n  });\n  const store = {\n    dispatch,\n    subscribe,\n    getState,\n    replaceReducer,\n    [symbol_observable_default]: observable\n  };\n  return store;\n}\nfunction legacy_createStore(reducer, preloadedState, enhancer) {\n  return createStore(reducer, preloadedState, enhancer);\n}\n\n// src/utils/warning.ts\nfunction warning(message) {\n  if (typeof console !== \"undefined\" && typeof console.error === \"function\") {\n    console.error(message);\n  }\n  try {\n    throw new Error(message);\n  } catch (e) {}\n}\n\n// src/combineReducers.ts\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n  const reducerKeys = Object.keys(reducers);\n  const argumentName = action && action.type === actionTypes_default.INIT ? \"preloadedState argument passed to createStore\" : \"previous state received by the reducer\";\n  if (reducerKeys.length === 0) {\n    return \"Store does not have a valid reducer. Make sure the argument passed to combineReducers is an object whose values are reducers.\";\n  }\n  if (!isPlainObject(inputState)) {\n    return \"The \".concat(argumentName, \" has unexpected type of \\\"\").concat(kindOf(inputState), \"\\\". Expected argument to be an object with the following keys: \\\"\").concat(reducerKeys.join('\", \"'), \"\\\"\");\n  }\n  const unexpectedKeys = Object.keys(inputState).filter(key => !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key]);\n  unexpectedKeys.forEach(key => {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === actionTypes_default.REPLACE) return;\n  if (unexpectedKeys.length > 0) {\n    return \"Unexpected \".concat(unexpectedKeys.length > 1 ? \"keys\" : \"key\", \" \\\"\").concat(unexpectedKeys.join('\", \"'), \"\\\" found in \").concat(argumentName, \". Expected to find one of the known reducer keys instead: \\\"\").concat(reducerKeys.join('\", \"'), \"\\\". Unexpected keys will be ignored.\");\n  }\n}\nfunction assertReducerShape(reducers) {\n  Object.keys(reducers).forEach(key => {\n    const reducer = reducers[key];\n    const initialState = reducer(void 0, {\n      type: actionTypes_default.INIT\n    });\n    if (typeof initialState === \"undefined\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(12) : \"The slice reducer for key \\\"\".concat(key, \"\\\" returned undefined during initialization. If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.\"));\n    }\n    if (typeof reducer(void 0, {\n      type: actionTypes_default.PROBE_UNKNOWN_ACTION()\n    }) === \"undefined\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(13) : \"The slice reducer for key \\\"\".concat(key, \"\\\" returned undefined when probed with a random type. Don't try to handle '\").concat(actionTypes_default.INIT, \"' or other actions in \\\"redux/*\\\" namespace. They are considered private. Instead, you must return the current state for any unknown actions, unless it is undefined, in which case you must return the initial state, regardless of the action type. The initial state may not be undefined, but can be null.\"));\n    }\n  });\n}\nfunction combineReducers(reducers) {\n  const reducerKeys = Object.keys(reducers);\n  const finalReducers = {};\n  for (let i = 0; i < reducerKeys.length; i++) {\n    const key = reducerKeys[i];\n    if (process.env.NODE_ENV !== \"production\") {\n      if (typeof reducers[key] === \"undefined\") {\n        warning(\"No reducer provided for key \\\"\".concat(key, \"\\\"\"));\n      }\n    }\n    if (typeof reducers[key] === \"function\") {\n      finalReducers[key] = reducers[key];\n    }\n  }\n  const finalReducerKeys = Object.keys(finalReducers);\n  let unexpectedKeyCache;\n  if (process.env.NODE_ENV !== \"production\") {\n    unexpectedKeyCache = {};\n  }\n  let shapeAssertionError;\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n  return function combination() {\n    let state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let action = arguments.length > 1 ? arguments[1] : undefined;\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n      const warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n    let hasChanged = false;\n    const nextState = {};\n    for (let i = 0; i < finalReducerKeys.length; i++) {\n      const key = finalReducerKeys[i];\n      const reducer = finalReducers[key];\n      const previousStateForKey = state[key];\n      const nextStateForKey = reducer(previousStateForKey, action);\n      if (typeof nextStateForKey === \"undefined\") {\n        const actionType = action && action.type;\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(14) : \"When called with an action of type \".concat(actionType ? \"\\\"\".concat(String(actionType), \"\\\"\") : \"(unknown type)\", \", the slice reducer for key \\\"\").concat(key, \"\\\" returned undefined. To ignore an action, you must explicitly return the previous state. If you want this reducer to hold no value, you can return null instead of undefined.\"));\n      }\n      nextState[key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n    return hasChanged ? nextState : state;\n  };\n}\n\n// src/bindActionCreators.ts\nfunction bindActionCreator(actionCreator, dispatch) {\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return dispatch(actionCreator.apply(this, args));\n  };\n}\nfunction bindActionCreators(actionCreators, dispatch) {\n  if (typeof actionCreators === \"function\") {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n  if (typeof actionCreators !== \"object\" || actionCreators === null) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(16) : \"bindActionCreators expected an object or a function, but instead received: '\".concat(kindOf(actionCreators), \"'. Did you write \\\"import ActionCreators from\\\" instead of \\\"import * as ActionCreators from\\\"?\"));\n  }\n  const boundActionCreators = {};\n  for (const key in actionCreators) {\n    const actionCreator = actionCreators[key];\n    if (typeof actionCreator === \"function\") {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n  return boundActionCreators;\n}\n\n// src/compose.ts\nfunction compose() {\n  for (var _len2 = arguments.length, funcs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    funcs[_key2] = arguments[_key2];\n  }\n  if (funcs.length === 0) {\n    return arg => arg;\n  }\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n  return funcs.reduce((a, b) => function () {\n    return a(b(...arguments));\n  });\n}\n\n// src/applyMiddleware.ts\nfunction applyMiddleware() {\n  for (var _len3 = arguments.length, middlewares = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    middlewares[_key3] = arguments[_key3];\n  }\n  return createStore2 => (reducer, preloadedState) => {\n    const store = createStore2(reducer, preloadedState);\n    let dispatch = () => {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(15) : \"Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.\");\n    };\n    const middlewareAPI = {\n      getState: store.getState,\n      dispatch: function (action) {\n        for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n          args[_key4 - 1] = arguments[_key4];\n        }\n        return dispatch(action, ...args);\n      }\n    };\n    const chain = middlewares.map(middleware => middleware(middlewareAPI));\n    dispatch = compose(...chain)(store.dispatch);\n    return _objectSpread(_objectSpread({}, store), {}, {\n      dispatch\n    });\n  };\n}\n\n// src/utils/isAction.ts\nfunction isAction(action) {\n  return isPlainObject(action) && \"type\" in action && typeof action.type === \"string\";\n}\nexport { actionTypes_default as __DO_NOT_USE__ActionTypes, applyMiddleware, bindActionCreators, combineReducers, compose, createStore, isAction, isPlainObject, legacy_createStore };", "map": {"version": 3, "names": ["formatProdErrorMessage", "code", "concat", "$$observable", "Symbol", "observable", "symbol_observable_default", "randomString", "Math", "random", "toString", "substring", "split", "join", "ActionTypes", "INIT", "REPLACE", "PROBE_UNKNOWN_ACTION", "actionTypes_default", "isPlainObject", "obj", "proto", "Object", "getPrototypeOf", "miniKindOf", "val", "type", "Array", "isArray", "isDate", "isError", "constructorName", "ctorName", "prototype", "call", "slice", "toLowerCase", "replace", "constructor", "name", "Error", "message", "stackTraceLimit", "Date", "toDateString", "getDate", "setDate", "kindOf", "typeOfVal", "process", "env", "NODE_ENV", "createStore", "reducer", "preloadedState", "enhancer", "arguments", "currentReducer", "currentState", "currentListeners", "Map", "nextListeners", "listenerIdCounter", "isDispatching", "ensureCanMutateNextListeners", "for<PERSON>ach", "listener", "key", "set", "getState", "subscribe", "isSubscribed", "listenerId", "unsubscribe", "delete", "dispatch", "action", "listeners", "replaceReducer", "nextReducer", "outerSubscribe", "observer", "observeState", "observerAsObserver", "next", "store", "legacy_createStore", "warning", "console", "error", "e", "getUnexpectedStateShapeWarningMessage", "inputState", "reducers", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reducerKeys", "keys", "argumentName", "length", "<PERSON><PERSON><PERSON><PERSON>", "filter", "hasOwnProperty", "assertReducerShape", "initialState", "combineReducers", "finalReducers", "i", "final<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shapeAssertionError", "combination", "state", "undefined", "warningMessage", "has<PERSON><PERSON>ed", "nextState", "previousStateForKey", "nextStateForKey", "actionType", "String", "bindActionCreator", "actionCreator", "_len", "args", "_key", "apply", "bindActionCreators", "actionCreators", "boundActionCreators", "compose", "_len2", "funcs", "_key2", "arg", "reduce", "a", "b", "applyMiddleware", "_len3", "middlewares", "_key3", "createStore2", "middlewareAPI", "_len4", "_key4", "chain", "map", "middleware", "_objectSpread", "isAction"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/redux/src/utils/formatProdErrorMessage.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/redux/src/utils/symbol-observable.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/redux/src/utils/actionTypes.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/redux/src/utils/isPlainObject.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/redux/src/utils/kindOf.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/redux/src/createStore.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/redux/src/utils/warning.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/redux/src/combineReducers.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/redux/src/bindActionCreators.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/redux/src/compose.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/redux/src/applyMiddleware.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/redux/src/utils/isAction.ts"], "sourcesContent": ["/**\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\n *\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\n * during build.\n * @param {number} code\n */\nexport function formatProdErrorMessage(code: number) {\n  return `Minified Redux error #${code}; visit https://redux.js.org/Errors?code=${code} for the full message or ` + 'use the non-minified dev environment for full errors. ';\n}", "declare global {\n  interface SymbolConstructor {\n    readonly observable: symbol;\n  }\n}\nconst $$observable = /* #__PURE__ */(() => typeof Symbol === 'function' && Symbol.observable || '@@observable')();\nexport default $$observable;", "/**\n * These are private action types reserved by Redux.\n * For any unknown actions, you must return the current state.\n * If the current state is undefined, you must return the initial state.\n * Do not reference these action types directly in your code.\n */\n\nconst randomString = () => Math.random().toString(36).substring(7).split('').join('.');\nconst ActionTypes = {\n  INIT: `@@redux/INIT${/* #__PURE__ */randomString()}`,\n  REPLACE: `@@redux/REPLACE${/* #__PURE__ */randomString()}`,\n  PROBE_UNKNOWN_ACTION: () => `@@redux/PROBE_UNKNOWN_ACTION${randomString()}`\n};\nexport default ActionTypes;", "/**\n * @param obj The object to inspect.\n * @returns True if the argument appears to be a plain object.\n */\nexport default function isPlainObject(obj: any): obj is object {\n  if (typeof obj !== 'object' || obj === null) return false;\n  let proto = obj;\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Object.getPrototypeOf(obj) === proto || Object.getPrototypeOf(obj) === null;\n}", "// Inlined / shortened version of `kindOf` from https://github.com/jonschlinkert/kind-of\nexport function miniKindOf(val: any): string {\n  if (val === void 0) return 'undefined';\n  if (val === null) return 'null';\n  const type = typeof val;\n  switch (type) {\n    case 'boolean':\n    case 'string':\n    case 'number':\n    case 'symbol':\n    case 'function':\n      {\n        return type;\n      }\n  }\n  if (Array.isArray(val)) return 'array';\n  if (isDate(val)) return 'date';\n  if (isError(val)) return 'error';\n  const constructorName = ctorName(val);\n  switch (constructorName) {\n    case 'Symbol':\n    case 'Promise':\n    case 'WeakMap':\n    case 'WeakSet':\n    case 'Map':\n    case 'Set':\n      return constructorName;\n  }\n\n  // other\n  return Object.prototype.toString.call(val).slice(8, -1).toLowerCase().replace(/\\s/g, '');\n}\nfunction ctorName(val: any): string | null {\n  return typeof val.constructor === 'function' ? val.constructor.name : null;\n}\nfunction isError(val: any) {\n  return val instanceof Error || typeof val.message === 'string' && val.constructor && typeof val.constructor.stackTraceLimit === 'number';\n}\nfunction isDate(val: any) {\n  if (val instanceof Date) return true;\n  return typeof val.toDateString === 'function' && typeof val.getDate === 'function' && typeof val.setDate === 'function';\n}\nexport function kindOf(val: any) {\n  let typeOfVal: string = typeof val;\n  if (process.env.NODE_ENV !== 'production') {\n    typeOfVal = miniKindOf(val);\n  }\n  return typeOfVal;\n}", "import { formatProdErrorMessage as _formatProdErrorMessage13 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage12 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage11 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage10 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage9 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage8 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage7 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage6 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage5 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage4 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage3 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage2 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage } from \"src/utils/formatProdErrorMessage\";\nimport $$observable from './utils/symbol-observable';\nimport { Store, StoreEnhancer, Dispatch, Observer, ListenerCallback, UnknownIfNonSpecific } from './types/store';\nimport { Action } from './types/actions';\nimport { Reducer } from './types/reducers';\nimport ActionTypes from './utils/actionTypes';\nimport isPlainObject from './utils/isPlainObject';\nimport { kindOf } from './utils/kindOf';\n\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */\nexport function createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}>(reducer: Reducer<S, A>, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext;\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */\nexport function createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}, PreloadedState = S>(reducer: Reducer<S, A, PreloadedState>, preloadedState?: PreloadedState | undefined, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext;\nexport function createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}, PreloadedState = S>(reducer: Reducer<S, A, PreloadedState>, preloadedState?: PreloadedState | StoreEnhancer<Ext, StateExt> | undefined, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext {\n  if (typeof reducer !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(2) : `Expected the root reducer to be a function. Instead, received: '${kindOf(reducer)}'`);\n  }\n  if (typeof preloadedState === 'function' && typeof enhancer === 'function' || typeof enhancer === 'function' && typeof arguments[3] === 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(0) : 'It looks like you are passing several store enhancers to ' + 'createStore(). This is not supported. Instead, compose them ' + 'together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.');\n  }\n  if (typeof preloadedState === 'function' && typeof enhancer === 'undefined') {\n    enhancer = (preloadedState as StoreEnhancer<Ext, StateExt>);\n    preloadedState = undefined;\n  }\n  if (typeof enhancer !== 'undefined') {\n    if (typeof enhancer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(1) : `Expected the enhancer to be a function. Instead, received: '${kindOf(enhancer)}'`);\n    }\n    return enhancer(createStore)(reducer, (preloadedState as PreloadedState | undefined));\n  }\n  let currentReducer = reducer;\n  let currentState: S | PreloadedState | undefined = (preloadedState as PreloadedState | undefined);\n  let currentListeners: Map<number, ListenerCallback> | null = new Map();\n  let nextListeners = currentListeners;\n  let listenerIdCounter = 0;\n  let isDispatching = false;\n\n  /**\n   * This makes a shallow copy of currentListeners so we can use\n   * nextListeners as a temporary list while dispatching.\n   *\n   * This prevents any bugs around consumers calling\n   * subscribe/unsubscribe in the middle of a dispatch.\n   */\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = new Map();\n      currentListeners.forEach((listener, key) => {\n        nextListeners.set(key, listener);\n      });\n    }\n  }\n\n  /**\n   * Reads the state tree managed by the store.\n   *\n   * @returns The current state tree of your application.\n   */\n  function getState(): S {\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(3) : 'You may not call store.getState() while the reducer is executing. ' + 'The reducer has already received the state as an argument. ' + 'Pass it down from the top reducer instead of reading it from the store.');\n    }\n    return (currentState as S);\n  }\n\n  /**\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param listener A callback to be invoked on every dispatch.\n   * @returns A function to remove this change listener.\n   */\n  function subscribe(listener: () => void) {\n    if (typeof listener !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(4) : `Expected the listener to be a function. Instead, received: '${kindOf(listener)}'`);\n    }\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(5) : 'You may not call store.subscribe() while the reducer is executing. ' + 'If you would like to be notified after the store has been updated, subscribe from a ' + 'component and invoke store.getState() in the callback to access the latest state. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n    }\n    let isSubscribed = true;\n    ensureCanMutateNextListeners();\n    const listenerId = listenerIdCounter++;\n    nextListeners.set(listenerId, listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n      if (isDispatching) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage7(6) : 'You may not unsubscribe from a store listener while the reducer is executing. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n      }\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      nextListeners.delete(listenerId);\n      currentListeners = null;\n    };\n  }\n\n  /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */\n  function dispatch(action: A) {\n    if (!isPlainObject(action)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage8(7) : `Actions must be plain objects. Instead, the actual type was: '${kindOf(action)}'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.`);\n    }\n    if (typeof action.type === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage9(8) : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n    }\n    if (typeof action.type !== 'string') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage10(17) : `Action \"type\" property must be a string. Instead, the actual type was: '${kindOf(action.type)}'. Value was: '${action.type}' (stringified)`);\n    }\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage11(9) : 'Reducers may not dispatch actions.');\n    }\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n    const listeners = currentListeners = nextListeners;\n    listeners.forEach(listener => {\n      listener();\n    });\n    return action;\n  }\n\n  /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param nextReducer The reducer for the store to use instead.\n   */\n  function replaceReducer(nextReducer: Reducer<S, A>): void {\n    if (typeof nextReducer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage12(10) : `Expected the nextReducer to be a function. Instead, received: '${kindOf(nextReducer)}`);\n    }\n    currentReducer = ((nextReducer as unknown) as Reducer<S, A, PreloadedState>);\n\n    // This action has a similar effect to ActionTypes.INIT.\n    // Any reducers that existed in both the new and old rootReducer\n    // will receive the previous state. This effectively populates\n    // the new state tree with any relevant data from the old one.\n    dispatch(({\n      type: ActionTypes.REPLACE\n    } as A));\n  }\n\n  /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */\n  function observable() {\n    const outerSubscribe = subscribe;\n    return {\n      /**\n       * The minimal observable subscription method.\n       * @param observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe(observer: unknown) {\n        if (typeof observer !== 'object' || observer === null) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage13(11) : `Expected the observer to be an object. Instead, received: '${kindOf(observer)}'`);\n        }\n        function observeState() {\n          const observerAsObserver = (observer as Observer<S>);\n          if (observerAsObserver.next) {\n            observerAsObserver.next(getState());\n          }\n        }\n        observeState();\n        const unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe\n        };\n      },\n      [$$observable]() {\n        return this;\n      }\n    };\n  }\n\n  // When a store is created, an \"INIT\" action is dispatched so that every\n  // reducer returns their initial state. This effectively populates\n  // the initial state tree.\n  dispatch(({\n    type: ActionTypes.INIT\n  } as A));\n  const store = (({\n    dispatch: (dispatch as Dispatch<A>),\n    subscribe,\n    getState,\n    replaceReducer,\n    [$$observable]: observable\n  } as unknown) as Store<S, A, StateExt> & Ext);\n  return store;\n}\n\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\nexport function legacy_createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}>(reducer: Reducer<S, A>, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext;\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\nexport function legacy_createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}, PreloadedState = S>(reducer: Reducer<S, A, PreloadedState>, preloadedState?: PreloadedState | undefined, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext;\nexport function legacy_createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}, PreloadedState = S>(reducer: Reducer<S, A>, preloadedState?: PreloadedState | StoreEnhancer<Ext, StateExt> | undefined, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext {\n  return createStore(reducer, (preloadedState as any), enhancer);\n}", "/**\n * Prints a warning in the console if it exists.\n *\n * @param message The warning message.\n */\nexport default function warning(message: string): void {\n  /* eslint-disable no-console */\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    console.error(message);\n  }\n  /* eslint-enable no-console */\n  try {\n    // This error was thrown as a convenience so that if you enable\n    // \"break on all exceptions\" in your console,\n    // it would pause the execution at this line.\n    throw new Error(message);\n  } catch (e) {} // eslint-disable-line no-empty\n}", "import { formatProdErrorMessage as _formatProdErrorMessage3 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage2 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage } from \"src/utils/formatProdErrorMessage\";\nimport { Action } from './types/actions';\nimport { ActionFromReducersMapObject, PreloadedStateShapeFromReducersMapObject, Reducer, StateFromReducersMapObject } from './types/reducers';\nimport ActionTypes from './utils/actionTypes';\nimport isPlainObject from './utils/isPlainObject';\nimport warning from './utils/warning';\nimport { kindOf } from './utils/kindOf';\nfunction getUnexpectedStateShapeWarningMessage(inputState: object, reducers: {\n  [key: string]: Reducer<any, any, any>;\n}, action: Action, unexpectedKeyCache: {\n  [key: string]: true;\n}) {\n  const reducerKeys = Object.keys(reducers);\n  const argumentName = action && action.type === ActionTypes.INIT ? 'preloadedState argument passed to createStore' : 'previous state received by the reducer';\n  if (reducerKeys.length === 0) {\n    return 'Store does not have a valid reducer. Make sure the argument passed ' + 'to combineReducers is an object whose values are reducers.';\n  }\n  if (!isPlainObject(inputState)) {\n    return `The ${argumentName} has unexpected type of \"${kindOf(inputState)}\". Expected argument to be an object with the following ` + `keys: \"${reducerKeys.join('\", \"')}\"`;\n  }\n  const unexpectedKeys = Object.keys(inputState).filter(key => !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key]);\n  unexpectedKeys.forEach(key => {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === ActionTypes.REPLACE) return;\n  if (unexpectedKeys.length > 0) {\n    return `Unexpected ${unexpectedKeys.length > 1 ? 'keys' : 'key'} ` + `\"${unexpectedKeys.join('\", \"')}\" found in ${argumentName}. ` + `Expected to find one of the known reducer keys instead: ` + `\"${reducerKeys.join('\", \"')}\". Unexpected keys will be ignored.`;\n  }\n}\nfunction assertReducerShape(reducers: {\n  [key: string]: Reducer<any, any, any>;\n}) {\n  Object.keys(reducers).forEach(key => {\n    const reducer = reducers[key];\n    const initialState = reducer(undefined, {\n      type: ActionTypes.INIT\n    });\n    if (typeof initialState === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(12) : `The slice reducer for key \"${key}\" returned undefined during initialization. ` + `If the state passed to the reducer is undefined, you must ` + `explicitly return the initial state. The initial state may ` + `not be undefined. If you don't want to set a value for this reducer, ` + `you can use null instead of undefined.`);\n    }\n    if (typeof reducer(undefined, {\n      type: ActionTypes.PROBE_UNKNOWN_ACTION()\n    }) === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(13) : `The slice reducer for key \"${key}\" returned undefined when probed with a random type. ` + `Don't try to handle '${ActionTypes.INIT}' or other actions in \"redux/*\" ` + `namespace. They are considered private. Instead, you must return the ` + `current state for any unknown actions, unless it is undefined, ` + `in which case you must return the initial state, regardless of the ` + `action type. The initial state may not be undefined, but can be null.`);\n    }\n  });\n}\n\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @template S Combined state object type.\n *\n * @param reducers An object whose values correspond to different reducer\n *   functions that need to be combined into one. One handy way to obtain it\n *   is to use `import * as reducers` syntax. The reducers may never\n *   return undefined for any action. Instead, they should return their\n *   initial state if the state passed to them was undefined, and the current\n *   state for any unrecognized action.\n *\n * @returns A reducer function that invokes every reducer inside the passed\n *   object, and builds a state object with the same shape.\n */\nexport default function combineReducers<M>(reducers: M): M[keyof M] extends Reducer<any, any, any> | undefined ? Reducer<StateFromReducersMapObject<M>, ActionFromReducersMapObject<M>, Partial<PreloadedStateShapeFromReducersMapObject<M>>> : never;\nexport default function combineReducers(reducers: {\n  [key: string]: Reducer<any, any, any>;\n}) {\n  const reducerKeys = Object.keys(reducers);\n  const finalReducers: {\n    [key: string]: Reducer<any, any, any>;\n  } = {};\n  for (let i = 0; i < reducerKeys.length; i++) {\n    const key = reducerKeys[i];\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof reducers[key] === 'undefined') {\n        warning(`No reducer provided for key \"${key}\"`);\n      }\n    }\n    if (typeof reducers[key] === 'function') {\n      finalReducers[key] = reducers[key];\n    }\n  }\n  const finalReducerKeys = Object.keys(finalReducers);\n\n  // This is used to make sure we don't warn about the same\n  // keys multiple times.\n  let unexpectedKeyCache: {\n    [key: string]: true;\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    unexpectedKeyCache = {};\n  }\n  let shapeAssertionError: unknown;\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n  return function combination(state: StateFromReducersMapObject<typeof reducers> = {}, action: Action) {\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n    let hasChanged = false;\n    const nextState: StateFromReducersMapObject<typeof reducers> = {};\n    for (let i = 0; i < finalReducerKeys.length; i++) {\n      const key = finalReducerKeys[i];\n      const reducer = finalReducers[key];\n      const previousStateForKey = state[key];\n      const nextStateForKey = reducer(previousStateForKey, action);\n      if (typeof nextStateForKey === 'undefined') {\n        const actionType = action && action.type;\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(14) : `When called with an action of type ${actionType ? `\"${String(actionType)}\"` : '(unknown type)'}, the slice reducer for key \"${key}\" returned undefined. ` + `To ignore an action, you must explicitly return the previous state. ` + `If you want this reducer to hold no value, you can return null instead of undefined.`);\n      }\n      nextState[key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n    return hasChanged ? nextState : state;\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"src/utils/formatProdErrorMessage\";\nimport { Dispatch } from './types/store';\nimport { ActionCreator, ActionCreatorsMapObject, Action } from './types/actions';\nimport { kindOf } from './utils/kindOf';\nfunction bindActionCreator<A extends Action>(actionCreator: ActionCreator<A>, dispatch: Dispatch<A>) {\n  return function (this: any, ...args: any[]) {\n    return dispatch(actionCreator.apply(this, args));\n  };\n}\n\n/**\n * Turns an object whose values are action creators, into an object with the\n * same keys, but with every function wrapped into a `dispatch` call so they\n * may be invoked directly. This is just a convenience method, as you can call\n * `store.dispatch(MyActionCreators.doSomething())` yourself just fine.\n *\n * For convenience, you can also pass an action creator as the first argument,\n * and get a dispatch wrapped function in return.\n *\n * @param actionCreators An object whose values are action\n * creator functions. One handy way to obtain it is to use `import * as`\n * syntax. You may also pass a single function.\n *\n * @param dispatch The `dispatch` function available on your Redux\n * store.\n *\n * @returns The object mimicking the original object, but with\n * every action creator wrapped into the `dispatch` call. If you passed a\n * function as `actionCreators`, the return value will also be a single\n * function.\n */\nexport default function bindActionCreators<A, C extends ActionCreator<A>>(actionCreator: C, dispatch: Dispatch): C;\nexport default function bindActionCreators<A extends ActionCreator<any>, B extends ActionCreator<any>>(actionCreator: A, dispatch: Dispatch): B;\nexport default function bindActionCreators<A, M extends ActionCreatorsMapObject<A>>(actionCreators: M, dispatch: Dispatch): M;\nexport default function bindActionCreators<M extends ActionCreatorsMapObject, N extends ActionCreatorsMapObject>(actionCreators: M, dispatch: Dispatch): N;\nexport default function bindActionCreators(actionCreators: ActionCreator<any> | ActionCreatorsMapObject, dispatch: Dispatch) {\n  if (typeof actionCreators === 'function') {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n  if (typeof actionCreators !== 'object' || actionCreators === null) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(16) : `bindActionCreators expected an object or a function, but instead received: '${kindOf(actionCreators)}'. ` + `Did you write \"import ActionCreators from\" instead of \"import * as ActionCreators from\"?`);\n  }\n  const boundActionCreators: ActionCreatorsMapObject = {};\n  for (const key in actionCreators) {\n    const actionCreator = actionCreators[key];\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n  return boundActionCreators;\n}", "type Func<T extends any[], R> = (...a: T) => R;\n\n/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for the\n * resulting composite function.\n *\n * @param funcs The functions to compose.\n * @returns A function obtained by composing the argument functions from right\n *   to left. For example, `compose(f, g, h)` is identical to doing\n *   `(...args) => f(g(h(...args)))`.\n */\nexport default function compose(): <R>(a: R) => R;\nexport default function compose<F extends Function>(f: F): F;\n\n/* two functions */\nexport default function compose<A, T extends any[], R>(f1: (a: A) => R, f2: Func<T, A>): Func<T, R>;\n\n/* three functions */\nexport default function compose<A, B, T extends any[], R>(f1: (b: B) => R, f2: (a: A) => B, f3: Func<T, A>): Func<T, R>;\n\n/* four functions */\nexport default function compose<A, B, C, T extends any[], R>(f1: (c: C) => R, f2: (b: B) => C, f3: (a: A) => B, f4: Func<T, A>): Func<T, R>;\n\n/* rest */\nexport default function compose<R>(f1: (a: any) => R, ...funcs: Function[]): (...args: any[]) => R;\nexport default function compose<R>(...funcs: Function[]): (...args: any[]) => R;\nexport default function compose(...funcs: Function[]) {\n  if (funcs.length === 0) {\n    // infer the argument type so it is usable in inference down the line\n    return <T,>(arg: T) => arg;\n  }\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n  return funcs.reduce((a, b) => (...args: any) => a(b(...args)));\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"src/utils/formatProdErrorMessage\";\nimport compose from './compose';\nimport { Middleware, MiddlewareAPI } from './types/middleware';\nimport { StoreEnhancer, Dispatch } from './types/store';\n\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param middlewares The middleware chain to be applied.\n * @returns A store enhancer applying the middleware.\n *\n * @template Ext Dispatch signature added by a middleware.\n * @template S The type of the state supported by a middleware.\n */\nexport default function applyMiddleware(): StoreEnhancer;\nexport default function applyMiddleware<Ext1, S>(middleware1: Middleware<Ext1, S, any>): StoreEnhancer<{\n  dispatch: Ext1;\n}>;\nexport default function applyMiddleware<Ext1, Ext2, S>(middleware1: Middleware<Ext1, S, any>, middleware2: Middleware<Ext2, S, any>): StoreEnhancer<{\n  dispatch: Ext1 & Ext2;\n}>;\nexport default function applyMiddleware<Ext1, Ext2, Ext3, S>(middleware1: Middleware<Ext1, S, any>, middleware2: Middleware<Ext2, S, any>, middleware3: Middleware<Ext3, S, any>): StoreEnhancer<{\n  dispatch: Ext1 & Ext2 & Ext3;\n}>;\nexport default function applyMiddleware<Ext1, Ext2, Ext3, Ext4, S>(middleware1: Middleware<Ext1, S, any>, middleware2: Middleware<Ext2, S, any>, middleware3: Middleware<Ext3, S, any>, middleware4: Middleware<Ext4, S, any>): StoreEnhancer<{\n  dispatch: Ext1 & Ext2 & Ext3 & Ext4;\n}>;\nexport default function applyMiddleware<Ext1, Ext2, Ext3, Ext4, Ext5, S>(middleware1: Middleware<Ext1, S, any>, middleware2: Middleware<Ext2, S, any>, middleware3: Middleware<Ext3, S, any>, middleware4: Middleware<Ext4, S, any>, middleware5: Middleware<Ext5, S, any>): StoreEnhancer<{\n  dispatch: Ext1 & Ext2 & Ext3 & Ext4 & Ext5;\n}>;\nexport default function applyMiddleware<Ext, S = any>(...middlewares: Middleware<any, S, any>[]): StoreEnhancer<{\n  dispatch: Ext;\n}>;\nexport default function applyMiddleware(...middlewares: Middleware[]): StoreEnhancer<any> {\n  return createStore => (reducer, preloadedState) => {\n    const store = createStore(reducer, preloadedState);\n    let dispatch: Dispatch = () => {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(15) : 'Dispatching while constructing your middleware is not allowed. ' + 'Other middleware would not be applied to this dispatch.');\n    };\n    const middlewareAPI: MiddlewareAPI = {\n      getState: store.getState,\n      dispatch: (action, ...args) => dispatch(action, ...args)\n    };\n    const chain = middlewares.map(middleware => middleware(middlewareAPI));\n    dispatch = compose<typeof dispatch>(...chain)(store.dispatch);\n    return {\n      ...store,\n      dispatch\n    };\n  };\n}", "import { Action } from '../types/actions';\nimport isPlainObject from './isPlainObject';\nexport default function isAction(action: unknown): action is Action<string> {\n  return isPlainObject(action) && 'type' in action && typeof (action as Record<'type', unknown>).type === 'string';\n}"], "mappings": ";;AAOO,SAASA,uBAAuBC,IAAA,EAAc;EACnD,gCAAAC,MAAA,CAAgCD,IAAI,+CAAAC,MAAA,CAA4CD,IAAI;AACtF;;;ACJA,IAAME,YAAA,GAA+B,sBAAM,OAAOC,MAAA,KAAW,cAAcA,MAAA,CAAOC,UAAA,IAAc,gBAAgB;AAChH,IAAOC,yBAAA,GAAQH,YAAA;;;ACCf,IAAMI,YAAA,GAAeA,CAAA,KAAMC,IAAA,CAAKC,MAAA,CAAO,EAAEC,QAAA,CAAS,EAAE,EAAEC,SAAA,CAAU,CAAC,EAAEC,KAAA,CAAM,EAAE,EAAEC,IAAA,CAAK,GAAG;AACrF,IAAMC,WAAA,GAAc;EAClBC,IAAA,iBAAAb,MAAA,CAAoC,eAAAK,YAAA,CAAa,CAAC;EAClDS,OAAA,oBAAAd,MAAA,CAA0C,eAAAK,YAAA,CAAa,CAAC;EACxDU,oBAAA,EAAsBA,CAAA,oCAAAf,MAAA,CAAqCK,YAAA,CAAa,CAAC;AAC3E;AACA,IAAOW,mBAAA,GAAQJ,WAAA;;;ACTA,SAARK,cAA+BC,GAAA,EAAyB;EAC7D,IAAI,OAAOA,GAAA,KAAQ,YAAYA,GAAA,KAAQ,MAAM,OAAO;EACpD,IAAIC,KAAA,GAAQD,GAAA;EACZ,OAAOE,MAAA,CAAOC,cAAA,CAAeF,KAAK,MAAM,MAAM;IAC5CA,KAAA,GAAQC,MAAA,CAAOC,cAAA,CAAeF,KAAK;EACrC;EACA,OAAOC,MAAA,CAAOC,cAAA,CAAeH,GAAG,MAAMC,KAAA,IAASC,MAAA,CAAOC,cAAA,CAAeH,GAAG,MAAM;AAChF;;;ACVO,SAASI,WAAWC,GAAA,EAAkB;EAC3C,IAAIA,GAAA,KAAQ,QAAQ,OAAO;EAC3B,IAAIA,GAAA,KAAQ,MAAM,OAAO;EACzB,MAAMC,IAAA,GAAO,OAAOD,GAAA;EACpB,QAAQC,IAAA;IACN,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;MACH;QACE,OAAOA,IAAA;MACT;EACJ;EACA,IAAIC,KAAA,CAAMC,OAAA,CAAQH,GAAG,GAAG,OAAO;EAC/B,IAAII,MAAA,CAAOJ,GAAG,GAAG,OAAO;EACxB,IAAIK,OAAA,CAAQL,GAAG,GAAG,OAAO;EACzB,MAAMM,eAAA,GAAkBC,QAAA,CAASP,GAAG;EACpC,QAAQM,eAAA;IACN,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;MACH,OAAOA,eAAA;EACX;EAGA,OAAOT,MAAA,CAAOW,SAAA,CAAUvB,QAAA,CAASwB,IAAA,CAAKT,GAAG,EAAEU,KAAA,CAAM,GAAG,EAAE,EAAEC,WAAA,CAAY,EAAEC,OAAA,CAAQ,OAAO,EAAE;AACzF;AACA,SAASL,SAASP,GAAA,EAAyB;EACzC,OAAO,OAAOA,GAAA,CAAIa,WAAA,KAAgB,aAAab,GAAA,CAAIa,WAAA,CAAYC,IAAA,GAAO;AACxE;AACA,SAAST,QAAQL,GAAA,EAAU;EACzB,OAAOA,GAAA,YAAee,KAAA,IAAS,OAAOf,GAAA,CAAIgB,OAAA,KAAY,YAAYhB,GAAA,CAAIa,WAAA,IAAe,OAAOb,GAAA,CAAIa,WAAA,CAAYI,eAAA,KAAoB;AAClI;AACA,SAASb,OAAOJ,GAAA,EAAU;EACxB,IAAIA,GAAA,YAAekB,IAAA,EAAM,OAAO;EAChC,OAAO,OAAOlB,GAAA,CAAImB,YAAA,KAAiB,cAAc,OAAOnB,GAAA,CAAIoB,OAAA,KAAY,cAAc,OAAOpB,GAAA,CAAIqB,OAAA,KAAY;AAC/G;AACO,SAASC,OAAOtB,GAAA,EAAU;EAC/B,IAAIuB,SAAA,GAAoB,OAAOvB,GAAA;EAC/B,IAAIwB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzCH,SAAA,GAAYxB,UAAA,CAAWC,GAAG;EAC5B;EACA,OAAOuB,SAAA;AACT;;;ACyBO,SAASI,YAAoGC,OAAA,EAAwCC,cAAA,EAA4EC,QAAA,EAA4F;EAClU,IAAI,OAAOF,OAAA,KAAY,YAAY;IACjC,MAAM,IAAIb,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAwB,CAAC,uEAAAE,MAAA,CAAuE6C,MAAA,CAAOM,OAAO,CAAC,MAAG;EAC5K;EACA,IAAI,OAAOC,cAAA,KAAmB,cAAc,OAAOC,QAAA,KAAa,cAAc,OAAOA,QAAA,KAAa,cAAc,OAAOC,SAAA,CAAU,CAAC,MAAM,YAAY;IAClJ,MAAM,IAAIhB,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAyB,CAAC,IAAI,kQAA4Q;EACpW;EACA,IAAI,OAAOsD,cAAA,KAAmB,cAAc,OAAOC,QAAA,KAAa,aAAa;IAC3EA,QAAA,GAAYD,cAAA;IACZA,cAAA,GAAiB;EACnB;EACA,IAAI,OAAOC,QAAA,KAAa,aAAa;IACnC,IAAI,OAAOA,QAAA,KAAa,YAAY;MAClC,MAAM,IAAIf,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAyB,CAAC,mEAAAE,MAAA,CAAmE6C,MAAA,CAAOQ,QAAQ,CAAC,MAAG;IAC1K;IACA,OAAOA,QAAA,CAASH,WAAW,EAAEC,OAAA,EAAUC,cAA6C;EACtF;EACA,IAAIG,cAAA,GAAiBJ,OAAA;EACrB,IAAIK,YAAA,GAAgDJ,cAAA;EACpD,IAAIK,gBAAA,GAAyD,mBAAIC,GAAA,CAAI;EACrE,IAAIC,aAAA,GAAgBF,gBAAA;EACpB,IAAIG,iBAAA,GAAoB;EACxB,IAAIC,aAAA,GAAgB;EASpB,SAASC,6BAAA,EAA+B;IACtC,IAAIH,aAAA,KAAkBF,gBAAA,EAAkB;MACtCE,aAAA,GAAgB,mBAAID,GAAA,CAAI;MACxBD,gBAAA,CAAiBM,OAAA,CAAQ,CAACC,QAAA,EAAUC,GAAA,KAAQ;QAC1CN,aAAA,CAAcO,GAAA,CAAID,GAAA,EAAKD,QAAQ;MACjC,CAAC;IACH;EACF;EAOA,SAASG,SAAA,EAAc;IACrB,IAAIN,aAAA,EAAe;MACjB,MAAM,IAAIvB,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAyB,CAAC,IAAI,sMAAgN;IACxS;IACA,OAAQ0D,YAAA;EACV;EAyBA,SAASY,UAAUJ,QAAA,EAAsB;IACvC,IAAI,OAAOA,QAAA,KAAa,YAAY;MAClC,MAAM,IAAI1B,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAyB,CAAC,mEAAAE,MAAA,CAAmE6C,MAAA,CAAOmB,QAAQ,CAAC,MAAG;IAC1K;IACA,IAAIH,aAAA,EAAe;MACjB,MAAM,IAAIvB,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAyB,CAAC,IAAI,iTAAgU;IACxZ;IACA,IAAIuE,YAAA,GAAe;IACnBP,4BAAA,CAA6B;IAC7B,MAAMQ,UAAA,GAAaV,iBAAA;IACnBD,aAAA,CAAcO,GAAA,CAAII,UAAA,EAAYN,QAAQ;IACtC,OAAO,SAASO,YAAA,EAAc;MAC5B,IAAI,CAACF,YAAA,EAAc;QACjB;MACF;MACA,IAAIR,aAAA,EAAe;QACjB,MAAM,IAAIvB,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAyB,CAAC,IAAI,sJAA2J;MACnP;MACAuE,YAAA,GAAe;MACfP,4BAAA,CAA6B;MAC7BH,aAAA,CAAca,MAAA,CAAOF,UAAU;MAC/Bb,gBAAA,GAAmB;IACrB;EACF;EA2BA,SAASgB,SAASC,MAAA,EAAW;IAC3B,IAAI,CAACzD,aAAA,CAAcyD,MAAM,GAAG;MAC1B,MAAM,IAAIpC,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAyB,CAAC,qEAAAE,MAAA,CAAqE6C,MAAA,CAAO6B,MAAM,CAAC,+UAA4U;IACnf;IACA,IAAI,OAAOA,MAAA,CAAOlD,IAAA,KAAS,aAAa;MACtC,MAAM,IAAIc,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAyB,CAAC,IAAI,4GAA4G;IACpM;IACA,IAAI,OAAO4E,MAAA,CAAOlD,IAAA,KAAS,UAAU;MACnC,MAAM,IAAIc,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAA0B,EAAE,iFAAAE,MAAA,CAA+E6C,MAAA,CAAO6B,MAAA,CAAOlD,IAAI,CAAC,qBAAAxB,MAAA,CAAkB0E,MAAA,CAAOlD,IAAI,oBAAiB;IACtO;IACA,IAAIqC,aAAA,EAAe;MACjB,MAAM,IAAIvB,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAA0B,CAAC,IAAI,oCAAoC;IAC7H;IACA,IAAI;MACF+D,aAAA,GAAgB;MAChBL,YAAA,GAAeD,cAAA,CAAeC,YAAA,EAAckB,MAAM;IACpD,UAAE;MACAb,aAAA,GAAgB;IAClB;IACA,MAAMc,SAAA,GAAYlB,gBAAA,GAAmBE,aAAA;IACrCgB,SAAA,CAAUZ,OAAA,CAAQC,QAAA,IAAY;MAC5BA,QAAA,CAAS;IACX,CAAC;IACD,OAAOU,MAAA;EACT;EAWA,SAASE,eAAeC,WAAA,EAAkC;IACxD,IAAI,OAAOA,WAAA,KAAgB,YAAY;MACrC,MAAM,IAAIvC,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAA0B,EAAE,sEAAAE,MAAA,CAAsE6C,MAAA,CAAOgC,WAAW,CAAC,CAAE;IACjL;IACAtB,cAAA,GAAmBsB,WAAA;IAMnBJ,QAAA,CAAU;MACRjD,IAAA,EAAMR,mBAAA,CAAYF;IACpB,CAAO;EACT;EAQA,SAASX,WAAA,EAAa;IACpB,MAAM2E,cAAA,GAAiBV,SAAA;IACvB,OAAO;MAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;MASLA,UAAUW,QAAA,EAAmB;QAC3B,IAAI,OAAOA,QAAA,KAAa,YAAYA,QAAA,KAAa,MAAM;UACrD,MAAM,IAAIzC,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAA0B,EAAE,kEAAAE,MAAA,CAAkE6C,MAAA,CAAOkC,QAAQ,CAAC,MAAG;QAC3K;QACA,SAASC,aAAA,EAAe;UACtB,MAAMC,kBAAA,GAAsBF,QAAA;UAC5B,IAAIE,kBAAA,CAAmBC,IAAA,EAAM;YAC3BD,kBAAA,CAAmBC,IAAA,CAAKf,QAAA,CAAS,CAAC;UACpC;QACF;QACAa,YAAA,CAAa;QACb,MAAMT,WAAA,GAAcO,cAAA,CAAeE,YAAY;QAC/C,OAAO;UACLT;QACF;MACF;MACA,CAACnE,yBAAY,IAAI;QACf,OAAO;MACT;IACF;EACF;EAKAqE,QAAA,CAAU;IACRjD,IAAA,EAAMR,mBAAA,CAAYH;EACpB,CAAO;EACP,MAAMsE,KAAA,GAAU;IACdV,QAAA;IACAL,SAAA;IACAD,QAAA;IACAS,cAAA;IACA,CAACxE,yBAAY,GAAGD;EAClB;EACA,OAAOgF,KAAA;AACT;AAgEO,SAASC,mBAA2GjC,OAAA,EAAwBC,cAAA,EAA4EC,QAAA,EAA4F;EACzT,OAAOH,WAAA,CAAYC,OAAA,EAAUC,cAAA,EAAwBC,QAAQ;AAC/D;;;AC1We,SAARgC,QAAyB9C,OAAA,EAAuB;EAErD,IAAI,OAAO+C,OAAA,KAAY,eAAe,OAAOA,OAAA,CAAQC,KAAA,KAAU,YAAY;IACzED,OAAA,CAAQC,KAAA,CAAMhD,OAAO;EACvB;EAEA,IAAI;IAIF,MAAM,IAAID,KAAA,CAAMC,OAAO;EACzB,SAASiD,CAAA,EAAG,CAAC;AACf;;;ACRA,SAASC,sCAAsCC,UAAA,EAAoBC,QAAA,EAEhEjB,MAAA,EAAgBkB,kBAAA,EAEhB;EACD,MAAMC,WAAA,GAAczE,MAAA,CAAO0E,IAAA,CAAKH,QAAQ;EACxC,MAAMI,YAAA,GAAerB,MAAA,IAAUA,MAAA,CAAOlD,IAAA,KAASR,mBAAA,CAAYH,IAAA,GAAO,kDAAkD;EACpH,IAAIgF,WAAA,CAAYG,MAAA,KAAW,GAAG;IAC5B,OAAO;EACT;EACA,IAAI,CAAC/E,aAAA,CAAcyE,UAAU,GAAG;IAC9B,cAAA1F,MAAA,CAAc+F,YAAY,gCAAA/F,MAAA,CAA4B6C,MAAA,CAAO6C,UAAU,CAAC,uEAAA1F,MAAA,CAAuE6F,WAAA,CAAYlF,IAAA,CAAK,MAAM,CAAC;EACzK;EACA,MAAMsF,cAAA,GAAiB7E,MAAA,CAAO0E,IAAA,CAAKJ,UAAU,EAAEQ,MAAA,CAAOjC,GAAA,IAAO,CAAC0B,QAAA,CAASQ,cAAA,CAAelC,GAAG,KAAK,CAAC2B,kBAAA,CAAmB3B,GAAG,CAAC;EACtHgC,cAAA,CAAelC,OAAA,CAAQE,GAAA,IAAO;IAC5B2B,kBAAA,CAAmB3B,GAAG,IAAI;EAC5B,CAAC;EACD,IAAIS,MAAA,IAAUA,MAAA,CAAOlD,IAAA,KAASR,mBAAA,CAAYF,OAAA,EAAS;EACnD,IAAImF,cAAA,CAAeD,MAAA,GAAS,GAAG;IAC7B,qBAAAhG,MAAA,CAAqBiG,cAAA,CAAeD,MAAA,GAAS,IAAI,SAAS,KAAK,SAAAhG,MAAA,CAAUiG,cAAA,CAAetF,IAAA,CAAK,MAAM,CAAC,kBAAAX,MAAA,CAAc+F,YAAY,kEAAA/F,MAAA,CAAwE6F,WAAA,CAAYlF,IAAA,CAAK,MAAM,CAAC;EAChO;AACF;AACA,SAASyF,mBAAmBT,QAAA,EAEzB;EACDvE,MAAA,CAAO0E,IAAA,CAAKH,QAAQ,EAAE5B,OAAA,CAAQE,GAAA,IAAO;IACnC,MAAMd,OAAA,GAAUwC,QAAA,CAAS1B,GAAG;IAC5B,MAAMoC,YAAA,GAAelD,OAAA,CAAQ,QAAW;MACtC3B,IAAA,EAAMR,mBAAA,CAAYH;IACpB,CAAC;IACD,IAAI,OAAOwF,YAAA,KAAiB,aAAa;MACvC,MAAM,IAAI/D,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAwB,EAAE,mCAAAE,MAAA,CAAkCiE,GAAG,kRAAkS;IAC3Z;IACA,IAAI,OAAOd,OAAA,CAAQ,QAAW;MAC5B3B,IAAA,EAAMR,mBAAA,CAAYD,oBAAA,CAAqB;IACzC,CAAC,MAAM,aAAa;MAClB,MAAM,IAAIuB,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAyB,EAAE,mCAAAE,MAAA,CAAkCiE,GAAG,iFAAAjE,MAAA,CAAkFgB,mBAAA,CAAYH,IAAI,mTAAkU;IAC9hB;EACF,CAAC;AACH;AAqBe,SAARyF,gBAAiCX,QAAA,EAErC;EACD,MAAME,WAAA,GAAczE,MAAA,CAAO0E,IAAA,CAAKH,QAAQ;EACxC,MAAMY,aAAA,GAEF,CAAC;EACL,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIX,WAAA,CAAYG,MAAA,EAAQQ,CAAA,IAAK;IAC3C,MAAMvC,GAAA,GAAM4B,WAAA,CAAYW,CAAC;IACzB,IAAIzD,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;MACzC,IAAI,OAAO0C,QAAA,CAAS1B,GAAG,MAAM,aAAa;QACxCoB,OAAA,kCAAArF,MAAA,CAAwCiE,GAAG,OAAG;MAChD;IACF;IACA,IAAI,OAAO0B,QAAA,CAAS1B,GAAG,MAAM,YAAY;MACvCsC,aAAA,CAActC,GAAG,IAAI0B,QAAA,CAAS1B,GAAG;IACnC;EACF;EACA,MAAMwC,gBAAA,GAAmBrF,MAAA,CAAO0E,IAAA,CAAKS,aAAa;EAIlD,IAAIX,kBAAA;EAGJ,IAAI7C,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC2C,kBAAA,GAAqB,CAAC;EACxB;EACA,IAAIc,mBAAA;EACJ,IAAI;IACFN,kBAAA,CAAmBG,aAAa;EAClC,SAASf,CAAA,EAAG;IACVkB,mBAAA,GAAsBlB,CAAA;EACxB;EACA,OAAO,SAASmB,YAAA,EAAqF;IAAA,IAAzEC,KAAA,GAAAtD,SAAA,CAAA0C,MAAA,QAAA1C,SAAA,QAAAuD,SAAA,GAAAvD,SAAA,MAAqD,CAAC;IAAA,IAAGoB,MAAA,GAAApB,SAAA,CAAA0C,MAAA,OAAA1C,SAAA,MAAAuD,SAAA;IACnF,IAAIH,mBAAA,EAAqB;MACvB,MAAMA,mBAAA;IACR;IACA,IAAI3D,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;MACzC,MAAM6D,cAAA,GAAiBrB,qCAAA,CAAsCmB,KAAA,EAAOL,aAAA,EAAe7B,MAAA,EAAQkB,kBAAkB;MAC7G,IAAIkB,cAAA,EAAgB;QAClBzB,OAAA,CAAQyB,cAAc;MACxB;IACF;IACA,IAAIC,UAAA,GAAa;IACjB,MAAMC,SAAA,GAAyD,CAAC;IAChE,SAASR,CAAA,GAAI,GAAGA,CAAA,GAAIC,gBAAA,CAAiBT,MAAA,EAAQQ,CAAA,IAAK;MAChD,MAAMvC,GAAA,GAAMwC,gBAAA,CAAiBD,CAAC;MAC9B,MAAMrD,OAAA,GAAUoD,aAAA,CAActC,GAAG;MACjC,MAAMgD,mBAAA,GAAsBL,KAAA,CAAM3C,GAAG;MACrC,MAAMiD,eAAA,GAAkB/D,OAAA,CAAQ8D,mBAAA,EAAqBvC,MAAM;MAC3D,IAAI,OAAOwC,eAAA,KAAoB,aAAa;QAC1C,MAAMC,UAAA,GAAazC,MAAA,IAAUA,MAAA,CAAOlD,IAAA;QACpC,MAAM,IAAIc,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAyB,EAAE,0CAAAE,MAAA,CAA0CmH,UAAA,QAAAnH,MAAA,CAAiBoH,MAAA,CAAOD,UAAU,CAAC,UAAM,gBAAgB,oCAAAnH,MAAA,CAAgCiE,GAAG,oLAA0L;MACrZ;MACA+C,SAAA,CAAU/C,GAAG,IAAIiD,eAAA;MACjBH,UAAA,GAAaA,UAAA,IAAcG,eAAA,KAAoBD,mBAAA;IACjD;IACAF,UAAA,GAAaA,UAAA,IAAcN,gBAAA,CAAiBT,MAAA,KAAW5E,MAAA,CAAO0E,IAAA,CAAKc,KAAK,EAAEZ,MAAA;IAC1E,OAAOe,UAAA,GAAaC,SAAA,GAAYJ,KAAA;EAClC;AACF;;;AC9HA,SAASS,kBAAoCC,aAAA,EAAiC7C,QAAA,EAAuB;EACnG,OAAO,YAAqC;IAAA,SAAA8C,IAAA,GAAAjE,SAAA,CAAA0C,MAAA,EAAbwB,IAAA,OAAA/F,KAAA,CAAA8F,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAAAD,IAAA,CAAAC,IAAA,IAAAnE,SAAA,CAAAmE,IAAA;IAAA;IAC7B,OAAOhD,QAAA,CAAS6C,aAAA,CAAcI,KAAA,CAAM,MAAMF,IAAI,CAAC;EACjD;AACF;AA2Be,SAARG,mBAAoCC,cAAA,EAA8DnD,QAAA,EAAoB;EAC3H,IAAI,OAAOmD,cAAA,KAAmB,YAAY;IACxC,OAAOP,iBAAA,CAAkBO,cAAA,EAAgBnD,QAAQ;EACnD;EACA,IAAI,OAAOmD,cAAA,KAAmB,YAAYA,cAAA,KAAmB,MAAM;IACjE,MAAM,IAAItF,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAwB,EAAE,mFAAAE,MAAA,CAAmF6C,MAAA,CAAO+E,cAAc,CAAC,oGAAkG;EAC/R;EACA,MAAMC,mBAAA,GAA+C,CAAC;EACtD,WAAW5D,GAAA,IAAO2D,cAAA,EAAgB;IAChC,MAAMN,aAAA,GAAgBM,cAAA,CAAe3D,GAAG;IACxC,IAAI,OAAOqD,aAAA,KAAkB,YAAY;MACvCO,mBAAA,CAAoB5D,GAAG,IAAIoD,iBAAA,CAAkBC,aAAA,EAAe7C,QAAQ;IACtE;EACF;EACA,OAAOoD,mBAAA;AACT;;;ACvBe,SAARC,QAAA,EAA+C;EAAA,SAAAC,KAAA,GAAAzE,SAAA,CAAA0C,MAAA,EAAnBgC,KAAA,OAAAvG,KAAA,CAAAsG,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;IAAAD,KAAA,CAAAC,KAAA,IAAA3E,SAAA,CAAA2E,KAAA;EAAA;EACjC,IAAID,KAAA,CAAMhC,MAAA,KAAW,GAAG;IAEtB,OAAYkC,GAAA,IAAWA,GAAA;EACzB;EACA,IAAIF,KAAA,CAAMhC,MAAA,KAAW,GAAG;IACtB,OAAOgC,KAAA,CAAM,CAAC;EAChB;EACA,OAAOA,KAAA,CAAMG,MAAA,CAAO,CAACC,CAAA,EAAGC,CAAA,KAAM;IAAA,OAAkBD,CAAA,CAAEC,CAAA,CAAE,GAAA/E,SAAO,CAAC,CAAC;EAAA;AAC/D;;;ACOe,SAARgF,gBAAA,EAAmF;EAAA,SAAAC,KAAA,GAAAjF,SAAA,CAAA0C,MAAA,EAA/CwC,WAAA,OAAA/G,KAAA,CAAA8G,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;IAAAD,WAAA,CAAAC,KAAA,IAAAnF,SAAA,CAAAmF,KAAA;EAAA;EACzC,OAAOC,YAAA,IAAe,CAACvF,OAAA,EAASC,cAAA,KAAmB;IACjD,MAAM+B,KAAA,GAAQuD,YAAA,CAAYvF,OAAA,EAASC,cAAc;IACjD,IAAIqB,QAAA,GAAqBA,CAAA,KAAM;MAC7B,MAAM,IAAInC,KAAA,CAAMS,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAenD,sBAAA,CAAwB,EAAE,IAAI,wHAA6H;IACrN;IACA,MAAM6I,aAAA,GAA+B;MACnCxE,QAAA,EAAUgB,KAAA,CAAMhB,QAAA;MAChBM,QAAA,EAAU,SAAAA,CAACC,MAAA;QAAA,SAAAkE,KAAA,GAAAtF,SAAA,CAAA0C,MAAA,EAAWwB,IAAA,OAAA/F,KAAA,CAAAmH,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAArB,IAAA,CAAAqB,KAAA,QAAAvF,SAAA,CAAAuF,KAAA;QAAA;QAAA,OAASpE,QAAA,CAASC,MAAA,EAAQ,GAAG8C,IAAI;MAAA;IACzD;IACA,MAAMsB,KAAA,GAAQN,WAAA,CAAYO,GAAA,CAAIC,UAAA,IAAcA,UAAA,CAAWL,aAAa,CAAC;IACrElE,QAAA,GAAWqD,OAAA,CAAyB,GAAGgB,KAAK,EAAE3D,KAAA,CAAMV,QAAQ;IAC5D,OAAAwE,aAAA,CAAAA,aAAA,KACK9D,KAAA;MACHV;IAAA;EAEJ;AACF;;;AC1De,SAARyE,SAA0BxE,MAAA,EAA2C;EAC1E,OAAOzD,aAAA,CAAcyD,MAAM,KAAK,UAAUA,MAAA,IAAU,OAAQA,MAAA,CAAmClD,IAAA,KAAS;AAC1G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}