{"ast": null, "code": "import { sin, cos, sqrt, pi, tau } from \"../math.js\";\nconst ka = 0.89081309152928522810;\nconst kr = sin(pi / 10) / sin(7 * pi / 10);\nconst kx = sin(tau / 10) * kr;\nconst ky = -cos(tau / 10) * kr;\nexport default {\n  draw(context, size) {\n    const r = sqrt(size * ka);\n    const x = kx * r;\n    const y = ky * r;\n    context.moveTo(0, -r);\n    context.lineTo(x, y);\n    for (let i = 1; i < 5; ++i) {\n      const a = tau * i / 5;\n      const c = cos(a);\n      const s = sin(a);\n      context.lineTo(s * r, -c * r);\n      context.lineTo(c * x - s * y, s * x + c * y);\n    }\n    context.closePath();\n  }\n};", "map": {"version": 3, "names": ["sin", "cos", "sqrt", "pi", "tau", "ka", "kr", "kx", "ky", "draw", "context", "size", "r", "x", "y", "moveTo", "lineTo", "i", "a", "c", "s", "closePath"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-shape/src/symbol/star.js"], "sourcesContent": ["import {sin, cos, sqrt, pi, tau} from \"../math.js\";\n\nconst ka = 0.89081309152928522810;\nconst kr = sin(pi / 10) / sin(7 * pi / 10);\nconst kx = sin(tau / 10) * kr;\nconst ky = -cos(tau / 10) * kr;\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size * ka);\n    const x = kx * r;\n    const y = ky * r;\n    context.moveTo(0, -r);\n    context.lineTo(x, y);\n    for (let i = 1; i < 5; ++i) {\n      const a = tau * i / 5;\n      const c = cos(a);\n      const s = sin(a);\n      context.lineTo(s * r, -c * r);\n      context.lineTo(c * x - s * y, s * x + c * y);\n    }\n    context.closePath();\n  }\n};\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,EAAE,EAAEC,GAAG,QAAO,YAAY;AAElD,MAAMC,EAAE,GAAG,sBAAsB;AACjC,MAAMC,EAAE,GAAGN,GAAG,CAACG,EAAE,GAAG,EAAE,CAAC,GAAGH,GAAG,CAAC,CAAC,GAAGG,EAAE,GAAG,EAAE,CAAC;AAC1C,MAAMI,EAAE,GAAGP,GAAG,CAACI,GAAG,GAAG,EAAE,CAAC,GAAGE,EAAE;AAC7B,MAAME,EAAE,GAAG,CAACP,GAAG,CAACG,GAAG,GAAG,EAAE,CAAC,GAAGE,EAAE;AAE9B,eAAe;EACbG,IAAIA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAClB,MAAMC,CAAC,GAAGV,IAAI,CAACS,IAAI,GAAGN,EAAE,CAAC;IACzB,MAAMQ,CAAC,GAAGN,EAAE,GAAGK,CAAC;IAChB,MAAME,CAAC,GAAGN,EAAE,GAAGI,CAAC;IAChBF,OAAO,CAACK,MAAM,CAAC,CAAC,EAAE,CAACH,CAAC,CAAC;IACrBF,OAAO,CAACM,MAAM,CAACH,CAAC,EAAEC,CAAC,CAAC;IACpB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC1B,MAAMC,CAAC,GAAGd,GAAG,GAAGa,CAAC,GAAG,CAAC;MACrB,MAAME,CAAC,GAAGlB,GAAG,CAACiB,CAAC,CAAC;MAChB,MAAME,CAAC,GAAGpB,GAAG,CAACkB,CAAC,CAAC;MAChBR,OAAO,CAACM,MAAM,CAACI,CAAC,GAAGR,CAAC,EAAE,CAACO,CAAC,GAAGP,CAAC,CAAC;MAC7BF,OAAO,CAACM,MAAM,CAACG,CAAC,GAAGN,CAAC,GAAGO,CAAC,GAAGN,CAAC,EAAEM,CAAC,GAAGP,CAAC,GAAGM,CAAC,GAAGL,CAAC,CAAC;IAC9C;IACAJ,OAAO,CAACW,SAAS,CAAC,CAAC;EACrB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}