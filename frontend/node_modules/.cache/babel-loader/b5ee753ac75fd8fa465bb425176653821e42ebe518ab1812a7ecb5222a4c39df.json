{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst get = require('./get.js');\nfunction property(path) {\n  return function (object) {\n    return get.get(object, path);\n  };\n}\nexports.property = property;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "get", "require", "property", "path", "object"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/es-toolkit/dist/compat/object/property.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst get = require('./get.js');\n\nfunction property(path) {\n    return function (object) {\n        return get.get(object, path);\n    };\n}\n\nexports.property = property;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,GAAG,GAAGC,OAAO,CAAC,UAAU,CAAC;AAE/B,SAASC,QAAQA,CAACC,IAAI,EAAE;EACpB,OAAO,UAAUC,MAAM,EAAE;IACrB,OAAOJ,GAAG,CAACA,GAAG,CAACI,MAAM,EAAED,IAAI,CAAC;EAChC,CAAC;AACL;AAEAP,OAAO,CAACM,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}