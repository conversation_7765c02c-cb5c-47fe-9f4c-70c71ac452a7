{"ast": null, "code": "import { interpolate, interpolateRound } from \"d3-interpolate\";\nimport { identity } from \"./continuous.js\";\nimport { initInterpolator } from \"./init.js\";\nimport { linearish } from \"./linear.js\";\nimport { loggish } from \"./log.js\";\nimport { symlogish } from \"./symlog.js\";\nimport { powish } from \"./pow.js\";\nfunction transformer() {\n  var x0 = 0,\n    x1 = 1,\n    t0,\n    t1,\n    k10,\n    transform,\n    interpolator = identity,\n    clamp = false,\n    unknown;\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : interpolator(k10 === 0 ? 0.5 : (x = (transform(x) - t0) * k10, clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n  scale.domain = function (_) {\n    return arguments.length ? ([x0, x1] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0), scale) : [x0, x1];\n  };\n  scale.clamp = function (_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n  scale.interpolator = function (_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n  function range(interpolate) {\n    return function (_) {\n      var r0, r1;\n      return arguments.length ? ([r0, r1] = _, interpolator = interpolate(r0, r1), scale) : [interpolator(0), interpolator(1)];\n    };\n  }\n  scale.range = range(interpolate);\n  scale.rangeRound = range(interpolateRound);\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  return function (t) {\n    transform = t, t0 = t(x0), t1 = t(x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0);\n    return scale;\n  };\n}\nexport function copy(source, target) {\n  return target.domain(source.domain()).interpolator(source.interpolator()).clamp(source.clamp()).unknown(source.unknown());\n}\nexport default function sequential() {\n  var scale = linearish(transformer()(identity));\n  scale.copy = function () {\n    return copy(scale, sequential());\n  };\n  return initInterpolator.apply(scale, arguments);\n}\nexport function sequentialLog() {\n  var scale = loggish(transformer()).domain([1, 10]);\n  scale.copy = function () {\n    return copy(scale, sequentialLog()).base(scale.base());\n  };\n  return initInterpolator.apply(scale, arguments);\n}\nexport function sequentialSymlog() {\n  var scale = symlogish(transformer());\n  scale.copy = function () {\n    return copy(scale, sequentialSymlog()).constant(scale.constant());\n  };\n  return initInterpolator.apply(scale, arguments);\n}\nexport function sequentialPow() {\n  var scale = powish(transformer());\n  scale.copy = function () {\n    return copy(scale, sequentialPow()).exponent(scale.exponent());\n  };\n  return initInterpolator.apply(scale, arguments);\n}\nexport function sequentialSqrt() {\n  return sequentialPow.apply(null, arguments).exponent(0.5);\n}", "map": {"version": 3, "names": ["interpolate", "interpolateRound", "identity", "initInterpolator", "linearish", "loggish", "symlogish", "powish", "transformer", "x0", "x1", "t0", "t1", "k10", "transform", "interpolator", "clamp", "unknown", "scale", "x", "isNaN", "Math", "max", "min", "domain", "_", "arguments", "length", "range", "r0", "r1", "rangeRound", "t", "copy", "source", "target", "sequential", "apply", "sequentialLog", "base", "sequentialSymlog", "constant", "sequentialPow", "exponent", "sequentialSqrt"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-scale/src/sequential.js"], "sourcesContent": ["import {interpolate, interpolateRound} from \"d3-interpolate\";\nimport {identity} from \"./continuous.js\";\nimport {initInterpolator} from \"./init.js\";\nimport {linearish} from \"./linear.js\";\nimport {loggish} from \"./log.js\";\nimport {symlogish} from \"./symlog.js\";\nimport {powish} from \"./pow.js\";\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 1,\n      t0,\n      t1,\n      k10,\n      transform,\n      interpolator = identity,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : interpolator(k10 === 0 ? 0.5 : (x = (transform(x) - t0) * k10, clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0), scale) : [x0, x1];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function(_) {\n      var r0, r1;\n      return arguments.length ? ([r0, r1] = _, interpolator = interpolate(r0, r1), scale) : [interpolator(0), interpolator(1)];\n    };\n  }\n\n  scale.range = range(interpolate);\n\n  scale.rangeRound = range(interpolateRound);\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0);\n    return scale;\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .interpolator(source.interpolator())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport default function sequential() {\n  var scale = linearish(transformer()(identity));\n\n  scale.copy = function() {\n    return copy(scale, sequential());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialLog() {\n  var scale = loggish(transformer()).domain([1, 10]);\n\n  scale.copy = function() {\n    return copy(scale, sequentialLog()).base(scale.base());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialSymlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialSymlog()).constant(scale.constant());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialPow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialPow()).exponent(scale.exponent());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialSqrt() {\n  return sequentialPow.apply(null, arguments).exponent(0.5);\n}\n"], "mappings": "AAAA,SAAQA,WAAW,EAAEC,gBAAgB,QAAO,gBAAgB;AAC5D,SAAQC,QAAQ,QAAO,iBAAiB;AACxC,SAAQC,gBAAgB,QAAO,WAAW;AAC1C,SAAQC,SAAS,QAAO,aAAa;AACrC,SAAQC,OAAO,QAAO,UAAU;AAChC,SAAQC,SAAS,QAAO,aAAa;AACrC,SAAQC,MAAM,QAAO,UAAU;AAE/B,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAIC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,EAAE;IACFC,EAAE;IACFC,GAAG;IACHC,SAAS;IACTC,YAAY,GAAGb,QAAQ;IACvBc,KAAK,GAAG,KAAK;IACbC,OAAO;EAEX,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,OAAOA,CAAC,IAAI,IAAI,IAAIC,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,GAAGF,OAAO,GAAGF,YAAY,CAACF,GAAG,KAAK,CAAC,GAAG,GAAG,IAAIM,CAAC,GAAG,CAACL,SAAS,CAACK,CAAC,CAAC,GAAGR,EAAE,IAAIE,GAAG,EAAEG,KAAK,GAAGK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC;EACxJ;EAEAD,KAAK,CAACM,MAAM,GAAG,UAASC,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,IAAI,CAAClB,EAAE,EAAEC,EAAE,CAAC,GAAGe,CAAC,EAAEd,EAAE,GAAGG,SAAS,CAACL,EAAE,GAAG,CAACA,EAAE,CAAC,EAAEG,EAAE,GAAGE,SAAS,CAACJ,EAAE,GAAG,CAACA,EAAE,CAAC,EAAEG,GAAG,GAAGF,EAAE,KAAKC,EAAE,GAAG,CAAC,GAAG,CAAC,IAAIA,EAAE,GAAGD,EAAE,CAAC,EAAEO,KAAK,IAAI,CAACT,EAAE,EAAEC,EAAE,CAAC;EACrJ,CAAC;EAEDQ,KAAK,CAACF,KAAK,GAAG,UAASS,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACC,MAAM,IAAIX,KAAK,GAAG,CAAC,CAACS,CAAC,EAAEP,KAAK,IAAIF,KAAK;EACxD,CAAC;EAEDE,KAAK,CAACH,YAAY,GAAG,UAASU,CAAC,EAAE;IAC/B,OAAOC,SAAS,CAACC,MAAM,IAAIZ,YAAY,GAAGU,CAAC,EAAEP,KAAK,IAAIH,YAAY;EACpE,CAAC;EAED,SAASa,KAAKA,CAAC5B,WAAW,EAAE;IAC1B,OAAO,UAASyB,CAAC,EAAE;MACjB,IAAII,EAAE,EAAEC,EAAE;MACV,OAAOJ,SAAS,CAACC,MAAM,IAAI,CAACE,EAAE,EAAEC,EAAE,CAAC,GAAGL,CAAC,EAAEV,YAAY,GAAGf,WAAW,CAAC6B,EAAE,EAAEC,EAAE,CAAC,EAAEZ,KAAK,IAAI,CAACH,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,CAAC;IAC1H,CAAC;EACH;EAEAG,KAAK,CAACU,KAAK,GAAGA,KAAK,CAAC5B,WAAW,CAAC;EAEhCkB,KAAK,CAACa,UAAU,GAAGH,KAAK,CAAC3B,gBAAgB,CAAC;EAE1CiB,KAAK,CAACD,OAAO,GAAG,UAASQ,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACC,MAAM,IAAIV,OAAO,GAAGQ,CAAC,EAAEP,KAAK,IAAID,OAAO;EAC1D,CAAC;EAED,OAAO,UAASe,CAAC,EAAE;IACjBlB,SAAS,GAAGkB,CAAC,EAAErB,EAAE,GAAGqB,CAAC,CAACvB,EAAE,CAAC,EAAEG,EAAE,GAAGoB,CAAC,CAACtB,EAAE,CAAC,EAAEG,GAAG,GAAGF,EAAE,KAAKC,EAAE,GAAG,CAAC,GAAG,CAAC,IAAIA,EAAE,GAAGD,EAAE,CAAC;IAC1E,OAAOO,KAAK;EACd,CAAC;AACH;AAEA,OAAO,SAASe,IAAIA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACnC,OAAOA,MAAM,CACRX,MAAM,CAACU,MAAM,CAACV,MAAM,CAAC,CAAC,CAAC,CACvBT,YAAY,CAACmB,MAAM,CAACnB,YAAY,CAAC,CAAC,CAAC,CACnCC,KAAK,CAACkB,MAAM,CAAClB,KAAK,CAAC,CAAC,CAAC,CACrBC,OAAO,CAACiB,MAAM,CAACjB,OAAO,CAAC,CAAC,CAAC;AAChC;AAEA,eAAe,SAASmB,UAAUA,CAAA,EAAG;EACnC,IAAIlB,KAAK,GAAGd,SAAS,CAACI,WAAW,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC;EAE9CgB,KAAK,CAACe,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI,CAACf,KAAK,EAAEkB,UAAU,CAAC,CAAC,CAAC;EAClC,CAAC;EAED,OAAOjC,gBAAgB,CAACkC,KAAK,CAACnB,KAAK,EAAEQ,SAAS,CAAC;AACjD;AAEA,OAAO,SAASY,aAAaA,CAAA,EAAG;EAC9B,IAAIpB,KAAK,GAAGb,OAAO,CAACG,WAAW,CAAC,CAAC,CAAC,CAACgB,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAElDN,KAAK,CAACe,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI,CAACf,KAAK,EAAEoB,aAAa,CAAC,CAAC,CAAC,CAACC,IAAI,CAACrB,KAAK,CAACqB,IAAI,CAAC,CAAC,CAAC;EACxD,CAAC;EAED,OAAOpC,gBAAgB,CAACkC,KAAK,CAACnB,KAAK,EAAEQ,SAAS,CAAC;AACjD;AAEA,OAAO,SAASc,gBAAgBA,CAAA,EAAG;EACjC,IAAItB,KAAK,GAAGZ,SAAS,CAACE,WAAW,CAAC,CAAC,CAAC;EAEpCU,KAAK,CAACe,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI,CAACf,KAAK,EAAEsB,gBAAgB,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACvB,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAAC;EACnE,CAAC;EAED,OAAOtC,gBAAgB,CAACkC,KAAK,CAACnB,KAAK,EAAEQ,SAAS,CAAC;AACjD;AAEA,OAAO,SAASgB,aAAaA,CAAA,EAAG;EAC9B,IAAIxB,KAAK,GAAGX,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC;EAEjCU,KAAK,CAACe,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI,CAACf,KAAK,EAAEwB,aAAa,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACzB,KAAK,CAACyB,QAAQ,CAAC,CAAC,CAAC;EAChE,CAAC;EAED,OAAOxC,gBAAgB,CAACkC,KAAK,CAACnB,KAAK,EAAEQ,SAAS,CAAC;AACjD;AAEA,OAAO,SAASkB,cAAcA,CAAA,EAAG;EAC/B,OAAOF,aAAa,CAACL,KAAK,CAAC,IAAI,EAAEX,SAAS,CAAC,CAACiB,QAAQ,CAAC,GAAG,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}