{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction eq(value, other) {\n  return value === other || Number.isNaN(value) && Number.isNaN(other);\n}\nexports.eq = eq;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "eq", "other", "Number", "isNaN"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/es-toolkit/dist/compat/util/eq.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction eq(value, other) {\n    return value === other || (Number.isNaN(value) && Number.isNaN(other));\n}\n\nexports.eq = eq;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,EAAEA,CAACD,KAAK,EAAEE,KAAK,EAAE;EACtB,OAAOF,KAAK,KAAKE,KAAK,IAAKC,MAAM,CAACC,KAAK,CAACJ,KAAK,CAAC,IAAIG,MAAM,CAACC,KAAK,CAACF,KAAK,CAAE;AAC1E;AAEAL,OAAO,CAACI,EAAE,GAAGA,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}