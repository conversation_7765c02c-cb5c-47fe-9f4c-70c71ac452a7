{"ast": null, "code": "import { DoubleIndexedKV } from './double-indexed-kv';\nvar Registry = /** @class */function () {\n  function Registry(generateIdentifier) {\n    this.generateIdentifier = generateIdentifier;\n    this.kv = new DoubleIndexedKV();\n  }\n  Registry.prototype.register = function (value, identifier) {\n    if (this.kv.getByValue(value)) {\n      return;\n    }\n    if (!identifier) {\n      identifier = this.generateIdentifier(value);\n    }\n    this.kv.set(identifier, value);\n  };\n  Registry.prototype.clear = function () {\n    this.kv.clear();\n  };\n  Registry.prototype.getIdentifier = function (value) {\n    return this.kv.getByValue(value);\n  };\n  Registry.prototype.getValue = function (identifier) {\n    return this.kv.getByKey(identifier);\n  };\n  return Registry;\n}();\nexport { Registry };", "map": {"version": 3, "names": ["DoubleIndexedKV", "Registry", "generateIdentifier", "kv", "prototype", "register", "value", "identifier", "getByValue", "set", "clear", "getIdentifier", "getValue", "get<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/superjson/src/registry.ts"], "sourcesContent": ["import { DoubleIndexedKV } from './double-indexed-kv';\n\nexport class Registry<T> {\n  private kv = new DoubleIndexedKV<string, T>();\n\n  constructor(private readonly generateIdentifier: (v: T) => string) {}\n\n  register(value: T, identifier?: string): void {\n    if (this.kv.getByValue(value)) {\n      return;\n    }\n\n    if (!identifier) {\n      identifier = this.generateIdentifier(value);\n    }\n\n    this.kv.set(identifier, value);\n  }\n\n  clear(): void {\n    this.kv.clear();\n  }\n\n  getIdentifier(value: T) {\n    return this.kv.getByValue(value);\n  }\n\n  getValue(identifier: string) {\n    return this.kv.getByKey(identifier);\n  }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,qBAAqB;AAErD,IAAAC,QAAA;EAGE,SAAAA,SAA6BC,kBAAoC;IAApC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IAFvC,KAAAC,EAAE,GAAG,IAAIH,eAAe,EAAa;EAEuB;EAEpEC,QAAA,CAAAG,SAAA,CAAAC,QAAQ,GAAR,UAASC,KAAQ,EAAEC,UAAmB;IACpC,IAAI,IAAI,CAACJ,EAAE,CAACK,UAAU,CAACF,KAAK,CAAC,EAAE;MAC7B;;IAGF,IAAI,CAACC,UAAU,EAAE;MACfA,UAAU,GAAG,IAAI,CAACL,kBAAkB,CAACI,KAAK,CAAC;;IAG7C,IAAI,CAACH,EAAE,CAACM,GAAG,CAACF,UAAU,EAAED,KAAK,CAAC;EAChC,CAAC;EAEDL,QAAA,CAAAG,SAAA,CAAAM,KAAK,GAAL;IACE,IAAI,CAACP,EAAE,CAACO,KAAK,EAAE;EACjB,CAAC;EAEDT,QAAA,CAAAG,SAAA,CAAAO,aAAa,GAAb,UAAcL,KAAQ;IACpB,OAAO,IAAI,CAACH,EAAE,CAACK,UAAU,CAACF,KAAK,CAAC;EAClC,CAAC;EAEDL,QAAA,CAAAG,SAAA,CAAAQ,QAAQ,GAAR,UAASL,UAAkB;IACzB,OAAO,IAAI,CAACJ,EAAE,CAACU,QAAQ,CAACN,UAAU,CAAC;EACrC,CAAC;EACH,OAAAN,QAAC;AAAD,CAAC,CA5BD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}