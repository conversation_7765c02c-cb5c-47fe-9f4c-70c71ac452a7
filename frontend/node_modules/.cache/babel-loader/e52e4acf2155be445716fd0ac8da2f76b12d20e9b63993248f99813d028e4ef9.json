{"ast": null, "code": "module.exports = require('../dist/compat/math/range.js').range;", "map": {"version": 3, "names": ["module", "exports", "require", "range"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/es-toolkit/compat/range.js"], "sourcesContent": ["module.exports = require('../dist/compat/math/range.js').range;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,8BAA8B,CAAC,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}