{"ast": null, "code": "import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * The properties inside this state update independently of each other and quite often.\n * When selecting, never select the whole state because you are going to get\n * unnecessary re-renders. Select only the properties you need.\n *\n * This is why this state type is not exported - don't use it directly.\n */\n\nvar initialState = {\n  settings: {\n    layout: 'horizontal',\n    align: 'center',\n    verticalAlign: 'middle',\n    itemSorter: 'value'\n  },\n  size: {\n    width: 0,\n    height: 0\n  },\n  payload: []\n};\nvar legendSlice = createSlice({\n  name: 'legend',\n  initialState,\n  reducers: {\n    setLegendSize(state, action) {\n      state.size.width = action.payload.width;\n      state.size.height = action.payload.height;\n    },\n    setLegendSettings(state, action) {\n      state.settings.align = action.payload.align;\n      state.settings.layout = action.payload.layout;\n      state.settings.verticalAlign = action.payload.verticalAlign;\n      state.settings.itemSorter = action.payload.itemSorter;\n    },\n    addLegendPayload(state, action) {\n      state.payload.push(castDraft(action.payload));\n    },\n    removeLegendPayload(state, action) {\n      var index = current(state).payload.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.payload.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  setLegendSize,\n  setLegendSettings,\n  addLegendPayload,\n  removeLegendPayload\n} = legendSlice.actions;\nexport var legendReducer = legendSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "current", "castDraft", "initialState", "settings", "layout", "align", "verticalAlign", "itemSorter", "size", "width", "height", "payload", "legendSlice", "name", "reducers", "setLegendSize", "state", "action", "setLegendSettings", "addLegendPayload", "push", "removeLegendPayload", "index", "indexOf", "splice", "actions", "legendReducer", "reducer"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/recharts/es6/state/legendSlice.js"], "sourcesContent": ["import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * The properties inside this state update independently of each other and quite often.\n * When selecting, never select the whole state because you are going to get\n * unnecessary re-renders. Select only the properties you need.\n *\n * This is why this state type is not exported - don't use it directly.\n */\n\nvar initialState = {\n  settings: {\n    layout: 'horizontal',\n    align: 'center',\n    verticalAlign: 'middle',\n    itemSorter: 'value'\n  },\n  size: {\n    width: 0,\n    height: 0\n  },\n  payload: []\n};\nvar legendSlice = createSlice({\n  name: 'legend',\n  initialState,\n  reducers: {\n    setLegendSize(state, action) {\n      state.size.width = action.payload.width;\n      state.size.height = action.payload.height;\n    },\n    setLegendSettings(state, action) {\n      state.settings.align = action.payload.align;\n      state.settings.layout = action.payload.layout;\n      state.settings.verticalAlign = action.payload.verticalAlign;\n      state.settings.itemSorter = action.payload.itemSorter;\n    },\n    addLegendPayload(state, action) {\n      state.payload.push(castDraft(action.payload));\n    },\n    removeLegendPayload(state, action) {\n      var index = current(state).payload.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.payload.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  setLegendSize,\n  setLegendSettings,\n  addLegendPayload,\n  removeLegendPayload\n} = legendSlice.actions;\nexport var legendReducer = legendSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,OAAO,QAAQ,kBAAkB;AACvD,SAASC,SAAS,QAAQ,OAAO;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,YAAY,GAAG;EACjBC,QAAQ,EAAE;IACRC,MAAM,EAAE,YAAY;IACpBC,KAAK,EAAE,QAAQ;IACfC,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE;EACd,CAAC;EACDC,IAAI,EAAE;IACJC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EACDC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,WAAW,GAAGb,WAAW,CAAC;EAC5Bc,IAAI,EAAE,QAAQ;EACdX,YAAY;EACZY,QAAQ,EAAE;IACRC,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAE;MAC3BD,KAAK,CAACR,IAAI,CAACC,KAAK,GAAGQ,MAAM,CAACN,OAAO,CAACF,KAAK;MACvCO,KAAK,CAACR,IAAI,CAACE,MAAM,GAAGO,MAAM,CAACN,OAAO,CAACD,MAAM;IAC3C,CAAC;IACDQ,iBAAiBA,CAACF,KAAK,EAAEC,MAAM,EAAE;MAC/BD,KAAK,CAACb,QAAQ,CAACE,KAAK,GAAGY,MAAM,CAACN,OAAO,CAACN,KAAK;MAC3CW,KAAK,CAACb,QAAQ,CAACC,MAAM,GAAGa,MAAM,CAACN,OAAO,CAACP,MAAM;MAC7CY,KAAK,CAACb,QAAQ,CAACG,aAAa,GAAGW,MAAM,CAACN,OAAO,CAACL,aAAa;MAC3DU,KAAK,CAACb,QAAQ,CAACI,UAAU,GAAGU,MAAM,CAACN,OAAO,CAACJ,UAAU;IACvD,CAAC;IACDY,gBAAgBA,CAACH,KAAK,EAAEC,MAAM,EAAE;MAC9BD,KAAK,CAACL,OAAO,CAACS,IAAI,CAACnB,SAAS,CAACgB,MAAM,CAACN,OAAO,CAAC,CAAC;IAC/C,CAAC;IACDU,mBAAmBA,CAACL,KAAK,EAAEC,MAAM,EAAE;MACjC,IAAIK,KAAK,GAAGtB,OAAO,CAACgB,KAAK,CAAC,CAACL,OAAO,CAACY,OAAO,CAACtB,SAAS,CAACgB,MAAM,CAACN,OAAO,CAAC,CAAC;MACrE,IAAIW,KAAK,GAAG,CAAC,CAAC,EAAE;QACdN,KAAK,CAACL,OAAO,CAACa,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAChC;IACF;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAI;EACTP,aAAa;EACbG,iBAAiB;EACjBC,gBAAgB;EAChBE;AACF,CAAC,GAAGT,WAAW,CAACa,OAAO;AACvB,OAAO,IAAIC,aAAa,GAAGd,WAAW,CAACe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}