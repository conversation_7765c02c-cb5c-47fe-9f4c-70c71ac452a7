{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst debounce$1 = require('../../function/debounce.js');\nfunction debounce(func) {\n  let debounceMs = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (typeof options !== 'object') {\n    options = {};\n  }\n  const {\n    leading = false,\n    trailing = true,\n    maxWait\n  } = options;\n  const edges = Array(2);\n  if (leading) {\n    edges[0] = 'leading';\n  }\n  if (trailing) {\n    edges[1] = 'trailing';\n  }\n  let result = undefined;\n  let pendingAt = null;\n  const _debounced = debounce$1.debounce(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    result = func.apply(this, args);\n    pendingAt = null;\n  }, debounceMs, {\n    edges\n  });\n  const debounced = function () {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (maxWait != null) {\n      if (pendingAt === null) {\n        pendingAt = Date.now();\n      }\n      if (Date.now() - pendingAt >= maxWait) {\n        result = func.apply(this, args);\n        pendingAt = Date.now();\n        _debounced.cancel();\n        _debounced.schedule();\n        return result;\n      }\n    }\n    _debounced.apply(this, args);\n    return result;\n  };\n  const flush = () => {\n    _debounced.flush();\n    return result;\n  };\n  debounced.cancel = _debounced.cancel;\n  debounced.flush = flush;\n  return debounced;\n}\nexports.debounce = debounce;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "debounce$1", "require", "debounce", "func", "debounceMs", "arguments", "length", "undefined", "options", "leading", "trailing", "max<PERSON><PERSON>", "edges", "Array", "result", "pendingAt", "_debounced", "_len", "args", "_key", "apply", "debounced", "_len2", "_key2", "Date", "now", "cancel", "schedule", "flush"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/es-toolkit/dist/compat/function/debounce.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst debounce$1 = require('../../function/debounce.js');\n\nfunction debounce(func, debounceMs = 0, options = {}) {\n    if (typeof options !== 'object') {\n        options = {};\n    }\n    const { leading = false, trailing = true, maxWait } = options;\n    const edges = Array(2);\n    if (leading) {\n        edges[0] = 'leading';\n    }\n    if (trailing) {\n        edges[1] = 'trailing';\n    }\n    let result = undefined;\n    let pendingAt = null;\n    const _debounced = debounce$1.debounce(function (...args) {\n        result = func.apply(this, args);\n        pendingAt = null;\n    }, debounceMs, { edges });\n    const debounced = function (...args) {\n        if (maxWait != null) {\n            if (pendingAt === null) {\n                pendingAt = Date.now();\n            }\n            if (Date.now() - pendingAt >= maxWait) {\n                result = func.apply(this, args);\n                pendingAt = Date.now();\n                _debounced.cancel();\n                _debounced.schedule();\n                return result;\n            }\n        }\n        _debounced.apply(this, args);\n        return result;\n    };\n    const flush = () => {\n        _debounced.flush();\n        return result;\n    };\n    debounced.cancel = _debounced.cancel;\n    debounced.flush = flush;\n    return debounced;\n}\n\nexports.debounce = debounce;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,UAAU,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAExD,SAASC,QAAQA,CAACC,IAAI,EAAgC;EAAA,IAA9BC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAChD,IAAI,OAAOG,OAAO,KAAK,QAAQ,EAAE;IAC7BA,OAAO,GAAG,CAAC,CAAC;EAChB;EACA,MAAM;IAAEC,OAAO,GAAG,KAAK;IAAEC,QAAQ,GAAG,IAAI;IAAEC;EAAQ,CAAC,GAAGH,OAAO;EAC7D,MAAMI,KAAK,GAAGC,KAAK,CAAC,CAAC,CAAC;EACtB,IAAIJ,OAAO,EAAE;IACTG,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS;EACxB;EACA,IAAIF,QAAQ,EAAE;IACVE,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU;EACzB;EACA,IAAIE,MAAM,GAAGP,SAAS;EACtB,IAAIQ,SAAS,GAAG,IAAI;EACpB,MAAMC,UAAU,GAAGhB,UAAU,CAACE,QAAQ,CAAC,YAAmB;IAAA,SAAAe,IAAA,GAAAZ,SAAA,CAAAC,MAAA,EAANY,IAAI,OAAAL,KAAA,CAAAI,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAAJD,IAAI,CAAAC,IAAA,IAAAd,SAAA,CAAAc,IAAA;IAAA;IACpDL,MAAM,GAAGX,IAAI,CAACiB,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;IAC/BH,SAAS,GAAG,IAAI;EACpB,CAAC,EAAEX,UAAU,EAAE;IAAEQ;EAAM,CAAC,CAAC;EACzB,MAAMS,SAAS,GAAG,SAAAA,CAAA,EAAmB;IAAA,SAAAC,KAAA,GAAAjB,SAAA,CAAAC,MAAA,EAANY,IAAI,OAAAL,KAAA,CAAAS,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJL,IAAI,CAAAK,KAAA,IAAAlB,SAAA,CAAAkB,KAAA;IAAA;IAC/B,IAAIZ,OAAO,IAAI,IAAI,EAAE;MACjB,IAAII,SAAS,KAAK,IAAI,EAAE;QACpBA,SAAS,GAAGS,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B;MACA,IAAID,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGV,SAAS,IAAIJ,OAAO,EAAE;QACnCG,MAAM,GAAGX,IAAI,CAACiB,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;QAC/BH,SAAS,GAAGS,IAAI,CAACC,GAAG,CAAC,CAAC;QACtBT,UAAU,CAACU,MAAM,CAAC,CAAC;QACnBV,UAAU,CAACW,QAAQ,CAAC,CAAC;QACrB,OAAOb,MAAM;MACjB;IACJ;IACAE,UAAU,CAACI,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;IAC5B,OAAOJ,MAAM;EACjB,CAAC;EACD,MAAMc,KAAK,GAAGA,CAAA,KAAM;IAChBZ,UAAU,CAACY,KAAK,CAAC,CAAC;IAClB,OAAOd,MAAM;EACjB,CAAC;EACDO,SAAS,CAACK,MAAM,GAAGV,UAAU,CAACU,MAAM;EACpCL,SAAS,CAACO,KAAK,GAAGA,KAAK;EACvB,OAAOP,SAAS;AACpB;AAEAzB,OAAO,CAACM,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}