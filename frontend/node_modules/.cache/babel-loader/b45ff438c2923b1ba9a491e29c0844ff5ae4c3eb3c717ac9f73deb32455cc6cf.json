{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction debounce(func, debounceMs) {\n  let {\n    signal,\n    edges\n  } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  let pendingThis = undefined;\n  let pendingArgs = null;\n  const leading = edges != null && edges.includes('leading');\n  const trailing = edges == null || edges.includes('trailing');\n  const invoke = () => {\n    if (pendingArgs !== null) {\n      func.apply(pendingThis, pendingArgs);\n      pendingThis = undefined;\n      pendingArgs = null;\n    }\n  };\n  const onTimerEnd = () => {\n    if (trailing) {\n      invoke();\n    }\n    cancel();\n  };\n  let timeoutId = null;\n  const schedule = () => {\n    if (timeoutId != null) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      timeoutId = null;\n      onTimerEnd();\n    }, debounceMs);\n  };\n  const cancelTimer = () => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n      timeoutId = null;\n    }\n  };\n  const cancel = () => {\n    cancelTimer();\n    pendingThis = undefined;\n    pendingArgs = null;\n  };\n  const flush = () => {\n    cancelTimer();\n    invoke();\n  };\n  const debounced = function () {\n    if (signal !== null && signal !== void 0 && signal.aborted) {\n      return;\n    }\n    pendingThis = this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    pendingArgs = args;\n    const isFirstCall = timeoutId == null;\n    schedule();\n    if (leading && isFirstCall) {\n      invoke();\n    }\n  };\n  debounced.schedule = schedule;\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  signal === null || signal === void 0 || signal.addEventListener('abort', cancel, {\n    once: true\n  });\n  return debounced;\n}\nexports.debounce = debounce;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "debounce", "func", "debounceMs", "signal", "edges", "arguments", "length", "undefined", "pendingThis", "<PERSON><PERSON><PERSON>s", "leading", "includes", "trailing", "invoke", "apply", "onTimerEnd", "cancel", "timeoutId", "schedule", "clearTimeout", "setTimeout", "cancelTimer", "flush", "debounced", "aborted", "_len", "args", "Array", "_key", "isFirstCall", "addEventListener", "once"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/es-toolkit/dist/function/debounce.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction debounce(func, debounceMs, { signal, edges } = {}) {\n    let pendingThis = undefined;\n    let pendingArgs = null;\n    const leading = edges != null && edges.includes('leading');\n    const trailing = edges == null || edges.includes('trailing');\n    const invoke = () => {\n        if (pendingArgs !== null) {\n            func.apply(pendingThis, pendingArgs);\n            pendingThis = undefined;\n            pendingArgs = null;\n        }\n    };\n    const onTimerEnd = () => {\n        if (trailing) {\n            invoke();\n        }\n        cancel();\n    };\n    let timeoutId = null;\n    const schedule = () => {\n        if (timeoutId != null) {\n            clearTimeout(timeoutId);\n        }\n        timeoutId = setTimeout(() => {\n            timeoutId = null;\n            onTimerEnd();\n        }, debounceMs);\n    };\n    const cancelTimer = () => {\n        if (timeoutId !== null) {\n            clearTimeout(timeoutId);\n            timeoutId = null;\n        }\n    };\n    const cancel = () => {\n        cancelTimer();\n        pendingThis = undefined;\n        pendingArgs = null;\n    };\n    const flush = () => {\n        cancelTimer();\n        invoke();\n    };\n    const debounced = function (...args) {\n        if (signal?.aborted) {\n            return;\n        }\n        pendingThis = this;\n        pendingArgs = args;\n        const isFirstCall = timeoutId == null;\n        schedule();\n        if (leading && isFirstCall) {\n            invoke();\n        }\n    };\n    debounced.schedule = schedule;\n    debounced.cancel = cancel;\n    debounced.flush = flush;\n    signal?.addEventListener('abort', cancel, { once: true });\n    return debounced;\n}\n\nexports.debounce = debounce;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,QAAQA,CAACC,IAAI,EAAEC,UAAU,EAA0B;EAAA,IAAxB;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACtD,IAAIG,WAAW,GAAGD,SAAS;EAC3B,IAAIE,WAAW,GAAG,IAAI;EACtB,MAAMC,OAAO,GAAGN,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACO,QAAQ,CAAC,SAAS,CAAC;EAC1D,MAAMC,QAAQ,GAAGR,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACO,QAAQ,CAAC,UAAU,CAAC;EAC5D,MAAME,MAAM,GAAGA,CAAA,KAAM;IACjB,IAAIJ,WAAW,KAAK,IAAI,EAAE;MACtBR,IAAI,CAACa,KAAK,CAACN,WAAW,EAAEC,WAAW,CAAC;MACpCD,WAAW,GAAGD,SAAS;MACvBE,WAAW,GAAG,IAAI;IACtB;EACJ,CAAC;EACD,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIH,QAAQ,EAAE;MACVC,MAAM,CAAC,CAAC;IACZ;IACAG,MAAM,CAAC,CAAC;EACZ,CAAC;EACD,IAAIC,SAAS,GAAG,IAAI;EACpB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACnB,IAAID,SAAS,IAAI,IAAI,EAAE;MACnBE,YAAY,CAACF,SAAS,CAAC;IAC3B;IACAA,SAAS,GAAGG,UAAU,CAAC,MAAM;MACzBH,SAAS,GAAG,IAAI;MAChBF,UAAU,CAAC,CAAC;IAChB,CAAC,EAAEb,UAAU,CAAC;EAClB,CAAC;EACD,MAAMmB,WAAW,GAAGA,CAAA,KAAM;IACtB,IAAIJ,SAAS,KAAK,IAAI,EAAE;MACpBE,YAAY,CAACF,SAAS,CAAC;MACvBA,SAAS,GAAG,IAAI;IACpB;EACJ,CAAC;EACD,MAAMD,MAAM,GAAGA,CAAA,KAAM;IACjBK,WAAW,CAAC,CAAC;IACbb,WAAW,GAAGD,SAAS;IACvBE,WAAW,GAAG,IAAI;EACtB,CAAC;EACD,MAAMa,KAAK,GAAGA,CAAA,KAAM;IAChBD,WAAW,CAAC,CAAC;IACbR,MAAM,CAAC,CAAC;EACZ,CAAC;EACD,MAAMU,SAAS,GAAG,SAAAA,CAAA,EAAmB;IACjC,IAAIpB,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEqB,OAAO,EAAE;MACjB;IACJ;IACAhB,WAAW,GAAG,IAAI;IAAC,SAAAiB,IAAA,GAAApB,SAAA,CAAAC,MAAA,EAJQoB,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAvB,SAAA,CAAAuB,IAAA;IAAA;IAK/BnB,WAAW,GAAGiB,IAAI;IAClB,MAAMG,WAAW,GAAGZ,SAAS,IAAI,IAAI;IACrCC,QAAQ,CAAC,CAAC;IACV,IAAIR,OAAO,IAAImB,WAAW,EAAE;MACxBhB,MAAM,CAAC,CAAC;IACZ;EACJ,CAAC;EACDU,SAAS,CAACL,QAAQ,GAAGA,QAAQ;EAC7BK,SAAS,CAACP,MAAM,GAAGA,MAAM;EACzBO,SAAS,CAACD,KAAK,GAAGA,KAAK;EACvBnB,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE2B,gBAAgB,CAAC,OAAO,EAAEd,MAAM,EAAE;IAAEe,IAAI,EAAE;EAAK,CAAC,CAAC;EACzD,OAAOR,SAAS;AACpB;AAEA3B,OAAO,CAACI,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}