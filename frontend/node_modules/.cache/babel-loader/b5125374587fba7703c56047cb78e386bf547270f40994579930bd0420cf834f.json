{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { useEffect } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addCartesianGraphicalItem, addPolarGraphicalItem, removeCartesianGraphicalItem, removePolarGraphicalItem } from './graphicalItemsSlice';\nimport { getNormalizedStackId } from '../util/ChartUtils';\nexport function SetCartesianGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    var settings = _objectSpread(_objectSpread({}, props), {}, {\n      stackId: getNormalizedStackId(props.stackId)\n    });\n    dispatch(addCartesianGraphicalItem(settings));\n    return () => {\n      dispatch(removeCartesianGraphicalItem(settings));\n    };\n  }, [dispatch, props]);\n  return null;\n}\nexport function SetPolarGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addPolarGraphicalItem(props));\n    return () => {\n      dispatch(removePolarGraphicalItem(props));\n    };\n  }, [dispatch, props]);\n  return null;\n}", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "useEffect", "useAppDispatch", "addCartesianGraphicalItem", "addPolarGraphicalItem", "removeCartesianGraphicalItem", "removePolarGraphicalItem", "getNormalizedStackId", "SetCartesianGraphicalItem", "props", "dispatch", "settings", "stackId", "SetPolarGraphicalItem"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/recharts/es6/state/SetGraphicalItem.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { useEffect } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addCartesianGraphicalItem, addPolarGraphicalItem, removeCartesianGraphicalItem, removePolarGraphicalItem } from './graphicalItemsSlice';\nimport { getNormalizedStackId } from '../util/ChartUtils';\nexport function SetCartesianGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    var settings = _objectSpread(_objectSpread({}, props), {}, {\n      stackId: getNormalizedStackId(props.stackId)\n    });\n    dispatch(addCartesianGraphicalItem(settings));\n    return () => {\n      dispatch(removeCartesianGraphicalItem(settings));\n    };\n  }, [dispatch, props]);\n  return null;\n}\nexport function SetPolarGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addPolarGraphicalItem(props));\n    return () => {\n      dispatch(removePolarGraphicalItem(props));\n    };\n  }, [dispatch, props]);\n  return null;\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,SAAS;AACxC,SAASC,yBAAyB,EAAEC,qBAAqB,EAAEC,4BAA4B,EAAEC,wBAAwB,QAAQ,uBAAuB;AAChJ,SAASC,oBAAoB,QAAQ,oBAAoB;AACzD,OAAO,SAASC,yBAAyBA,CAACC,KAAK,EAAE;EAC/C,IAAIC,QAAQ,GAAGR,cAAc,CAAC,CAAC;EAC/BD,SAAS,CAAC,MAAM;IACd,IAAIU,QAAQ,GAAG9B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4B,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACzDG,OAAO,EAAEL,oBAAoB,CAACE,KAAK,CAACG,OAAO;IAC7C,CAAC,CAAC;IACFF,QAAQ,CAACP,yBAAyB,CAACQ,QAAQ,CAAC,CAAC;IAC7C,OAAO,MAAM;MACXD,QAAQ,CAACL,4BAA4B,CAACM,QAAQ,CAAC,CAAC;IAClD,CAAC;EACH,CAAC,EAAE,CAACD,QAAQ,EAAED,KAAK,CAAC,CAAC;EACrB,OAAO,IAAI;AACb;AACA,OAAO,SAASI,qBAAqBA,CAACJ,KAAK,EAAE;EAC3C,IAAIC,QAAQ,GAAGR,cAAc,CAAC,CAAC;EAC/BD,SAAS,CAAC,MAAM;IACdS,QAAQ,CAACN,qBAAqB,CAACK,KAAK,CAAC,CAAC;IACtC,OAAO,MAAM;MACXC,QAAQ,CAACJ,wBAAwB,CAACG,KAAK,CAAC,CAAC;IAC3C,CAAC;EACH,CAAC,EAAE,CAACC,QAAQ,EAAED,KAAK,CAAC,CAAC;EACrB,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}