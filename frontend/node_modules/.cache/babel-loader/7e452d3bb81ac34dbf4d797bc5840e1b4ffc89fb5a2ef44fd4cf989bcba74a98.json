{"ast": null, "code": "import _objectWithoutProperties from \"/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nimport _defineProperty from \"/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport _objectSpread from \"/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nconst _excluded = [\"reducerPath\"];\n// src/index.ts\nexport * from \"redux\";\nimport { produce, current as current3, freeze, original as original2, isDraft as isDraft5 } from \"immer\";\nimport { createSelector, createSelectorCreator as createSelectorCreator2, lruMemoize, weakMapMemoize as weakMapMemoize2 } from \"reselect\";\n\n// src/createDraftSafeSelector.ts\nimport { current, isDraft } from \"immer\";\nimport { createSelectorCreator, weakMapMemoize } from \"reselect\";\nvar createDraftSafeSelectorCreator = function () {\n  const createSelector2 = createSelectorCreator(...arguments);\n  const createDraftSafeSelector2 = Object.assign(function () {\n    const selector = createSelector2(...arguments);\n    const wrappedSelector = function (value) {\n      for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        rest[_key - 1] = arguments[_key];\n      }\n      return selector(isDraft(value) ? current(value) : value, ...rest);\n    };\n    Object.assign(wrappedSelector, selector);\n    return wrappedSelector;\n  }, {\n    withTypes: () => createDraftSafeSelector2\n  });\n  return createDraftSafeSelector2;\n};\nvar createDraftSafeSelector = /* @__PURE__ */createDraftSafeSelectorCreator(weakMapMemoize);\n\n// src/configureStore.ts\nimport { applyMiddleware, createStore, compose as compose2, combineReducers, isPlainObject as isPlainObject2 } from \"redux\";\n\n// src/devtoolsExtension.ts\nimport { compose } from \"redux\";\nvar composeWithDevTools = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function () {\n  if (arguments.length === 0) return void 0;\n  if (typeof arguments[0] === \"object\") return compose;\n  return compose.apply(null, arguments);\n};\nvar devToolsEnhancer = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function () {\n  return function (noop3) {\n    return noop3;\n  };\n};\n\n// src/getDefaultMiddleware.ts\nimport { thunk as thunkMiddleware, withExtraArgument } from \"redux-thunk\";\n\n// src/createAction.ts\nimport { isAction } from \"redux\";\n\n// src/tsHelpers.ts\nvar hasMatchFunction = v => {\n  return v && typeof v.match === \"function\";\n};\n\n// src/createAction.ts\nfunction createAction(type, prepareAction) {\n  function actionCreator() {\n    if (prepareAction) {\n      let prepared = prepareAction(...arguments);\n      if (!prepared) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(0) : \"prepareAction did not return an object\");\n      }\n      return _objectSpread(_objectSpread({\n        type,\n        payload: prepared.payload\n      }, \"meta\" in prepared && {\n        meta: prepared.meta\n      }), \"error\" in prepared && {\n        error: prepared.error\n      });\n    }\n    return {\n      type,\n      payload: arguments.length <= 0 ? undefined : arguments[0]\n    };\n  }\n  actionCreator.toString = () => \"\".concat(type);\n  actionCreator.type = type;\n  actionCreator.match = action => isAction(action) && action.type === type;\n  return actionCreator;\n}\nfunction isActionCreator(action) {\n  return typeof action === \"function\" && \"type\" in action &&\n  // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\n  hasMatchFunction(action);\n}\nfunction isFSA(action) {\n  return isAction(action) && Object.keys(action).every(isValidKey);\n}\nfunction isValidKey(key) {\n  return [\"type\", \"payload\", \"error\", \"meta\"].indexOf(key) > -1;\n}\n\n// src/actionCreatorInvariantMiddleware.ts\nfunction getMessage(type) {\n  const splitType = type ? \"\".concat(type).split(\"/\") : [];\n  const actionName = splitType[splitType.length - 1] || \"actionCreator\";\n  return \"Detected an action creator with type \\\"\".concat(type || \"unknown\", \"\\\" being dispatched. \\nMake sure you're calling the action creator before dispatching, i.e. `dispatch(\").concat(actionName, \"())` instead of `dispatch(\").concat(actionName, \")`. This is necessary even if the action has no payload.\");\n}\nfunction createActionCreatorInvariantMiddleware() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  if (process.env.NODE_ENV === \"production\") {\n    return () => next => action => next(action);\n  }\n  const {\n    isActionCreator: isActionCreator2 = isActionCreator\n  } = options;\n  return () => next => action => {\n    if (isActionCreator2(action)) {\n      console.warn(getMessage(action.type));\n    }\n    return next(action);\n  };\n}\n\n// src/utils.ts\nimport { produce as createNextState, isDraftable } from \"immer\";\nfunction getTimeMeasureUtils(maxDelay, fnName) {\n  let elapsed = 0;\n  return {\n    measureTime(fn) {\n      const started = Date.now();\n      try {\n        return fn();\n      } finally {\n        const finished = Date.now();\n        elapsed += finished - started;\n      }\n    },\n    warnIfExceeded() {\n      if (elapsed > maxDelay) {\n        console.warn(\"\".concat(fnName, \" took \").concat(elapsed, \"ms, which is more than the warning threshold of \").concat(maxDelay, \"ms. \\nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\\nIt is disabled in production builds, so you don't need to worry about that.\"));\n      }\n    }\n  };\n}\nvar Tuple = class _Tuple extends Array {\n  constructor() {\n    super(...arguments);\n    Object.setPrototypeOf(this, _Tuple.prototype);\n  }\n  static get [Symbol.species]() {\n    return _Tuple;\n  }\n  concat() {\n    for (var _len2 = arguments.length, arr = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      arr[_key2] = arguments[_key2];\n    }\n    return super.concat.apply(this, arr);\n  }\n  prepend() {\n    for (var _len3 = arguments.length, arr = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      arr[_key3] = arguments[_key3];\n    }\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new _Tuple(...arr[0].concat(this));\n    }\n    return new _Tuple(...arr.concat(this));\n  }\n};\nfunction freezeDraftable(val) {\n  return isDraftable(val) ? createNextState(val, () => {}) : val;\n}\nfunction getOrInsertComputed(map, key, compute) {\n  if (map.has(key)) return map.get(key);\n  return map.set(key, compute(key)).get(key);\n}\n\n// src/immutableStateInvariantMiddleware.ts\nfunction isImmutableDefault(value) {\n  return typeof value !== \"object\" || value == null || Object.isFrozen(value);\n}\nfunction trackForMutations(isImmutable, ignorePaths, obj) {\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\n  return {\n    detectMutations() {\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\n    }\n  };\n}\nfunction trackProperties(isImmutable) {\n  let ignorePaths = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  let obj = arguments.length > 2 ? arguments[2] : undefined;\n  let path = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : \"\";\n  let checkedObjects = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : /* @__PURE__ */new Set();\n  const tracked = {\n    value: obj\n  };\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\n    checkedObjects.add(obj);\n    tracked.children = {};\n    for (const key in obj) {\n      const childPath = path ? path + \".\" + key : key;\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\n        continue;\n      }\n      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\n    }\n  }\n  return tracked;\n}\nfunction detectMutations(isImmutable) {\n  let ignoredPaths = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  let trackedProperty = arguments.length > 2 ? arguments[2] : undefined;\n  let obj = arguments.length > 3 ? arguments[3] : undefined;\n  let sameParentRef = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n  let path = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : \"\";\n  const prevObj = trackedProperty ? trackedProperty.value : void 0;\n  const sameRef = prevObj === obj;\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\n    return {\n      wasMutated: true,\n      path\n    };\n  }\n  if (isImmutable(prevObj) || isImmutable(obj)) {\n    return {\n      wasMutated: false\n    };\n  }\n  const keysToDetect = {};\n  for (let key in trackedProperty.children) {\n    keysToDetect[key] = true;\n  }\n  for (let key in obj) {\n    keysToDetect[key] = true;\n  }\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (let key in keysToDetect) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some(ignored => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    const result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\n    if (result.wasMutated) {\n      return result;\n    }\n  }\n  return {\n    wasMutated: false\n  };\n}\nfunction createImmutableStateInvariantMiddleware() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  if (process.env.NODE_ENV === \"production\") {\n    return () => next => action => next(action);\n  } else {\n    let stringify2 = function (obj, serializer, indent, decycler) {\n        return JSON.stringify(obj, getSerialize2(serializer, decycler), indent);\n      },\n      getSerialize2 = function (serializer, decycler) {\n        let stack = [],\n          keys = [];\n        if (!decycler) decycler = function (_, value) {\n          if (stack[0] === value) return \"[Circular ~]\";\n          return \"[Circular ~.\" + keys.slice(0, stack.indexOf(value)).join(\".\") + \"]\";\n        };\n        return function (key, value) {\n          if (stack.length > 0) {\n            var thisPos = stack.indexOf(this);\n            ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n            ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n            if (~stack.indexOf(value)) value = decycler.call(this, key, value);\n          } else stack.push(value);\n          return serializer == null ? value : serializer.call(this, key, value);\n        };\n      };\n    var stringify = stringify2,\n      getSerialize = getSerialize2;\n    let {\n      isImmutable = isImmutableDefault,\n      ignoredPaths,\n      warnAfter = 32\n    } = options;\n    const track = trackForMutations.bind(null, isImmutable, ignoredPaths);\n    return _ref => {\n      let {\n        getState\n      } = _ref;\n      let state = getState();\n      let tracker = track(state);\n      let result;\n      return next => action => {\n        const measureUtils = getTimeMeasureUtils(warnAfter, \"ImmutableStateInvariantMiddleware\");\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(19) : \"A state mutation was detected between dispatches, in the path '\".concat(result.path || \"\", \"'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)\"));\n          }\n        });\n        const dispatchedAction = next(action);\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(20) : \"A state mutation was detected inside a dispatch, in the path: \".concat(result.path || \"\", \". Take a look at the reducer(s) handling the action \").concat(stringify2(action), \". (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)\"));\n          }\n        });\n        measureUtils.warnIfExceeded();\n        return dispatchedAction;\n      };\n    };\n  }\n}\n\n// src/serializableStateInvariantMiddleware.ts\nimport { isAction as isAction2, isPlainObject } from \"redux\";\nfunction isPlain(val) {\n  const type = typeof val;\n  return val == null || type === \"string\" || type === \"boolean\" || type === \"number\" || Array.isArray(val) || isPlainObject(val);\n}\nfunction findNonSerializableValue(value) {\n  let path = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n  let isSerializable = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : isPlain;\n  let getEntries = arguments.length > 3 ? arguments[3] : undefined;\n  let ignoredPaths = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : [];\n  let cache = arguments.length > 5 ? arguments[5] : undefined;\n  let foundNestedSerializable;\n  if (!isSerializable(value)) {\n    return {\n      keyPath: path || \"<root>\",\n      value\n    };\n  }\n  if (typeof value !== \"object\" || value === null) {\n    return false;\n  }\n  if (cache !== null && cache !== void 0 && cache.has(value)) return false;\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value);\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (const [key, nestedValue] of entries) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some(ignored => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    if (!isSerializable(nestedValue)) {\n      return {\n        keyPath: nestedPath,\n        value: nestedValue\n      };\n    }\n    if (typeof nestedValue === \"object\") {\n      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\n      if (foundNestedSerializable) {\n        return foundNestedSerializable;\n      }\n    }\n  }\n  if (cache && isNestedFrozen(value)) cache.add(value);\n  return false;\n}\nfunction isNestedFrozen(value) {\n  if (!Object.isFrozen(value)) return false;\n  for (const nestedValue of Object.values(value)) {\n    if (typeof nestedValue !== \"object\" || nestedValue === null) continue;\n    if (!isNestedFrozen(nestedValue)) return false;\n  }\n  return true;\n}\nfunction createSerializableStateInvariantMiddleware() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  if (process.env.NODE_ENV === \"production\") {\n    return () => next => action => next(action);\n  } else {\n    const {\n      isSerializable = isPlain,\n      getEntries,\n      ignoredActions = [],\n      ignoredActionPaths = [\"meta.arg\", \"meta.baseQueryMeta\"],\n      ignoredPaths = [],\n      warnAfter = 32,\n      ignoreState = false,\n      ignoreActions = false,\n      disableCache = false\n    } = options;\n    const cache = !disableCache && WeakSet ? /* @__PURE__ */new WeakSet() : void 0;\n    return storeAPI => next => action => {\n      if (!isAction2(action)) {\n        return next(action);\n      }\n      const result = next(action);\n      const measureUtils = getTimeMeasureUtils(warnAfter, \"SerializableStateInvariantMiddleware\");\n      if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)) {\n        measureUtils.measureTime(() => {\n          const foundActionNonSerializableValue = findNonSerializableValue(action, \"\", isSerializable, getEntries, ignoredActionPaths, cache);\n          if (foundActionNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundActionNonSerializableValue;\n            console.error(\"A non-serializable value was detected in an action, in the path: `\".concat(keyPath, \"`. Value:\"), value, \"\\nTake a look at the logic that dispatched this action: \", action, \"\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)\", \"\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)\");\n          }\n        });\n      }\n      if (!ignoreState) {\n        measureUtils.measureTime(() => {\n          const state = storeAPI.getState();\n          const foundStateNonSerializableValue = findNonSerializableValue(state, \"\", isSerializable, getEntries, ignoredPaths, cache);\n          if (foundStateNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundStateNonSerializableValue;\n            console.error(\"A non-serializable value was detected in the state, in the path: `\".concat(keyPath, \"`. Value:\"), value, \"\\nTake a look at the reducer(s) handling this action type: \".concat(action.type, \".\\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)\"));\n          }\n        });\n        measureUtils.warnIfExceeded();\n      }\n      return result;\n    };\n  }\n}\n\n// src/getDefaultMiddleware.ts\nfunction isBoolean(x) {\n  return typeof x === \"boolean\";\n}\nvar buildGetDefaultMiddleware = () => function getDefaultMiddleware(options) {\n  const {\n    thunk = true,\n    immutableCheck = true,\n    serializableCheck = true,\n    actionCreatorCheck = true\n  } = options !== null && options !== void 0 ? options : {};\n  let middlewareArray = new Tuple();\n  if (thunk) {\n    if (isBoolean(thunk)) {\n      middlewareArray.push(thunkMiddleware);\n    } else {\n      middlewareArray.push(withExtraArgument(thunk.extraArgument));\n    }\n  }\n  if (process.env.NODE_ENV !== \"production\") {\n    if (immutableCheck) {\n      let immutableOptions = {};\n      if (!isBoolean(immutableCheck)) {\n        immutableOptions = immutableCheck;\n      }\n      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\n    }\n    if (serializableCheck) {\n      let serializableOptions = {};\n      if (!isBoolean(serializableCheck)) {\n        serializableOptions = serializableCheck;\n      }\n      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\n    }\n    if (actionCreatorCheck) {\n      let actionCreatorOptions = {};\n      if (!isBoolean(actionCreatorCheck)) {\n        actionCreatorOptions = actionCreatorCheck;\n      }\n      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\n    }\n  }\n  return middlewareArray;\n};\n\n// src/autoBatchEnhancer.ts\nvar SHOULD_AUTOBATCH = \"RTK_autoBatch\";\nvar prepareAutoBatched = () => payload => ({\n  payload,\n  meta: {\n    [SHOULD_AUTOBATCH]: true\n  }\n});\nvar createQueueWithTimer = timeout => {\n  return notify => {\n    setTimeout(notify, timeout);\n  };\n};\nvar autoBatchEnhancer = function () {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    type: \"raf\"\n  };\n  return next => function () {\n    const store = next(...arguments);\n    let notifying = true;\n    let shouldNotifyAtEndOfTick = false;\n    let notificationQueued = false;\n    const listeners = /* @__PURE__ */new Set();\n    const queueCallback = options.type === \"tick\" ? queueMicrotask : options.type === \"raf\" ?\n    // requestAnimationFrame won't exist in SSR environments. Fall back to a vague approximation just to keep from erroring.\n    typeof window !== \"undefined\" && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10) : options.type === \"callback\" ? options.queueNotification : createQueueWithTimer(options.timeout);\n    const notifyListeners = () => {\n      notificationQueued = false;\n      if (shouldNotifyAtEndOfTick) {\n        shouldNotifyAtEndOfTick = false;\n        listeners.forEach(l => l());\n      }\n    };\n    return Object.assign({}, store, {\n      // Override the base `store.subscribe` method to keep original listeners\n      // from running if we're delaying notifications\n      subscribe(listener2) {\n        const wrappedListener = () => notifying && listener2();\n        const unsubscribe = store.subscribe(wrappedListener);\n        listeners.add(listener2);\n        return () => {\n          unsubscribe();\n          listeners.delete(listener2);\n        };\n      },\n      // Override the base `store.dispatch` method so that we can check actions\n      // for the `shouldAutoBatch` flag and determine if batching is active\n      dispatch(action) {\n        try {\n          var _action$meta;\n          notifying = !(action !== null && action !== void 0 && (_action$meta = action.meta) !== null && _action$meta !== void 0 && _action$meta[SHOULD_AUTOBATCH]);\n          shouldNotifyAtEndOfTick = !notifying;\n          if (shouldNotifyAtEndOfTick) {\n            if (!notificationQueued) {\n              notificationQueued = true;\n              queueCallback(notifyListeners);\n            }\n          }\n          return store.dispatch(action);\n        } finally {\n          notifying = true;\n        }\n      }\n    });\n  };\n};\n\n// src/getDefaultEnhancers.ts\nvar buildGetDefaultEnhancers = middlewareEnhancer => function getDefaultEnhancers(options) {\n  const {\n    autoBatch = true\n  } = options !== null && options !== void 0 ? options : {};\n  let enhancerArray = new Tuple(middlewareEnhancer);\n  if (autoBatch) {\n    enhancerArray.push(autoBatchEnhancer(typeof autoBatch === \"object\" ? autoBatch : void 0));\n  }\n  return enhancerArray;\n};\n\n// src/configureStore.ts\nfunction configureStore(options) {\n  const getDefaultMiddleware = buildGetDefaultMiddleware();\n  const {\n    reducer = void 0,\n    middleware,\n    devTools = true,\n    duplicateMiddlewareCheck = true,\n    preloadedState = void 0,\n    enhancers = void 0\n  } = options || {};\n  let rootReducer;\n  if (typeof reducer === \"function\") {\n    rootReducer = reducer;\n  } else if (isPlainObject2(reducer)) {\n    rootReducer = combineReducers(reducer);\n  } else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(1) : \"`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers\");\n  }\n  if (process.env.NODE_ENV !== \"production\" && middleware && typeof middleware !== \"function\") {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(2) : \"`middleware` field must be a callback\");\n  }\n  let finalMiddleware;\n  if (typeof middleware === \"function\") {\n    finalMiddleware = middleware(getDefaultMiddleware);\n    if (process.env.NODE_ENV !== \"production\" && !Array.isArray(finalMiddleware)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(3) : \"when using a middleware builder function, an array of middleware must be returned\");\n    }\n  } else {\n    finalMiddleware = getDefaultMiddleware();\n  }\n  if (process.env.NODE_ENV !== \"production\" && finalMiddleware.some(item => typeof item !== \"function\")) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(4) : \"each middleware provided to configureStore must be a function\");\n  }\n  if (process.env.NODE_ENV !== \"production\" && duplicateMiddlewareCheck) {\n    let middlewareReferences = /* @__PURE__ */new Set();\n    finalMiddleware.forEach(middleware2 => {\n      if (middlewareReferences.has(middleware2)) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(42) : \"Duplicate middleware references found when creating the store. Ensure that each middleware is only included once.\");\n      }\n      middlewareReferences.add(middleware2);\n    });\n  }\n  let finalCompose = compose2;\n  if (devTools) {\n    finalCompose = composeWithDevTools(_objectSpread({\n      // Enable capture of stack traces for dispatched Redux actions\n      trace: process.env.NODE_ENV !== \"production\"\n    }, typeof devTools === \"object\" && devTools));\n  }\n  const middlewareEnhancer = applyMiddleware(...finalMiddleware);\n  const getDefaultEnhancers = buildGetDefaultEnhancers(middlewareEnhancer);\n  if (process.env.NODE_ENV !== \"production\" && enhancers && typeof enhancers !== \"function\") {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(5) : \"`enhancers` field must be a callback\");\n  }\n  let storeEnhancers = typeof enhancers === \"function\" ? enhancers(getDefaultEnhancers) : getDefaultEnhancers();\n  if (process.env.NODE_ENV !== \"production\" && !Array.isArray(storeEnhancers)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(6) : \"`enhancers` callback must return an array\");\n  }\n  if (process.env.NODE_ENV !== \"production\" && storeEnhancers.some(item => typeof item !== \"function\")) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(7) : \"each enhancer provided to configureStore must be a function\");\n  }\n  if (process.env.NODE_ENV !== \"production\" && finalMiddleware.length && !storeEnhancers.includes(middlewareEnhancer)) {\n    console.error(\"middlewares were provided, but middleware enhancer was not included in final enhancers - make sure to call `getDefaultEnhancers`\");\n  }\n  const composedEnhancer = finalCompose(...storeEnhancers);\n  return createStore(rootReducer, preloadedState, composedEnhancer);\n}\n\n// src/createReducer.ts\nimport { produce as createNextState2, isDraft as isDraft2, isDraftable as isDraftable2 } from \"immer\";\n\n// src/mapBuilders.ts\nfunction executeReducerBuilderCallback(builderCallback) {\n  const actionsMap = {};\n  const actionMatchers = [];\n  let defaultCaseReducer;\n  const builder = {\n    addCase(typeOrActionCreator, reducer) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (actionMatchers.length > 0) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(26) : \"`builder.addCase` should only be called before calling `builder.addMatcher`\");\n        }\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(27) : \"`builder.addCase` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n      if (!type) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(28) : \"`builder.addCase` cannot be called with an empty action type\");\n      }\n      if (type in actionsMap) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(29) : \"`builder.addCase` cannot be called with two reducers for the same action type '\".concat(type, \"'\"));\n      }\n      actionsMap[type] = reducer;\n      return builder;\n    },\n    addMatcher(matcher, reducer) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(30) : \"`builder.addMatcher` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      actionMatchers.push({\n        matcher,\n        reducer\n      });\n      return builder;\n    },\n    addDefaultCase(reducer) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(31) : \"`builder.addDefaultCase` can only be called once\");\n        }\n      }\n      defaultCaseReducer = reducer;\n      return builder;\n    }\n  };\n  builderCallback(builder);\n  return [actionsMap, actionMatchers, defaultCaseReducer];\n}\n\n// src/createReducer.ts\nfunction isStateFunction(x) {\n  return typeof x === \"function\";\n}\nfunction createReducer(initialState, mapOrBuilderCallback) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof mapOrBuilderCallback === \"object\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(8) : \"The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\n    }\n  }\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] = executeReducerBuilderCallback(mapOrBuilderCallback);\n  let getInitialState;\n  if (isStateFunction(initialState)) {\n    getInitialState = () => freezeDraftable(initialState());\n  } else {\n    const frozenInitialState = freezeDraftable(initialState);\n    getInitialState = () => frozenInitialState;\n  }\n  function reducer() {\n    let state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getInitialState();\n    let action = arguments.length > 1 ? arguments[1] : undefined;\n    let caseReducers = [actionsMap[action.type], ...finalActionMatchers.filter(_ref2 => {\n      let {\n        matcher\n      } = _ref2;\n      return matcher(action);\n    }).map(_ref3 => {\n      let {\n        reducer: reducer2\n      } = _ref3;\n      return reducer2;\n    })];\n    if (caseReducers.filter(cr => !!cr).length === 0) {\n      caseReducers = [finalDefaultCaseReducer];\n    }\n    return caseReducers.reduce((previousState, caseReducer) => {\n      if (caseReducer) {\n        if (isDraft2(previousState)) {\n          const draft = previousState;\n          const result = caseReducer(draft, action);\n          if (result === void 0) {\n            return previousState;\n          }\n          return result;\n        } else if (!isDraftable2(previousState)) {\n          const result = caseReducer(previousState, action);\n          if (result === void 0) {\n            if (previousState === null) {\n              return previousState;\n            }\n            throw Error(\"A case reducer on a non-draftable value must not return undefined\");\n          }\n          return result;\n        } else {\n          return createNextState2(previousState, draft => {\n            return caseReducer(draft, action);\n          });\n        }\n      }\n      return previousState;\n    }, state);\n  }\n  reducer.getInitialState = getInitialState;\n  return reducer;\n}\n\n// src/matchers.ts\nvar matches = (matcher, action) => {\n  if (hasMatchFunction(matcher)) {\n    return matcher.match(action);\n  } else {\n    return matcher(action);\n  }\n};\nfunction isAnyOf() {\n  for (var _len4 = arguments.length, matchers = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n    matchers[_key4] = arguments[_key4];\n  }\n  return action => {\n    return matchers.some(matcher => matches(matcher, action));\n  };\n}\nfunction isAllOf() {\n  for (var _len5 = arguments.length, matchers = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n    matchers[_key5] = arguments[_key5];\n  }\n  return action => {\n    return matchers.every(matcher => matches(matcher, action));\n  };\n}\nfunction hasExpectedRequestMetadata(action, validStatus) {\n  if (!action || !action.meta) return false;\n  const hasValidRequestId = typeof action.meta.requestId === \"string\";\n  const hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\n  return hasValidRequestId && hasValidRequestStatus;\n}\nfunction isAsyncThunkArray(a) {\n  return typeof a[0] === \"function\" && \"pending\" in a[0] && \"fulfilled\" in a[0] && \"rejected\" in a[0];\n}\nfunction isPending() {\n  for (var _len6 = arguments.length, asyncThunks = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n    asyncThunks[_key6] = arguments[_key6];\n  }\n  if (asyncThunks.length === 0) {\n    return action => hasExpectedRequestMetadata(action, [\"pending\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isPending()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.pending));\n}\nfunction isRejected() {\n  for (var _len7 = arguments.length, asyncThunks = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n    asyncThunks[_key7] = arguments[_key7];\n  }\n  if (asyncThunks.length === 0) {\n    return action => hasExpectedRequestMetadata(action, [\"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejected()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.rejected));\n}\nfunction isRejectedWithValue() {\n  const hasFlag = action => {\n    return action && action.meta && action.meta.rejectedWithValue;\n  };\n  for (var _len8 = arguments.length, asyncThunks = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n    asyncThunks[_key8] = arguments[_key8];\n  }\n  if (asyncThunks.length === 0) {\n    return isAllOf(isRejected(...asyncThunks), hasFlag);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejectedWithValue()(asyncThunks[0]);\n  }\n  return isAllOf(isRejected(...asyncThunks), hasFlag);\n}\nfunction isFulfilled() {\n  for (var _len9 = arguments.length, asyncThunks = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n    asyncThunks[_key9] = arguments[_key9];\n  }\n  if (asyncThunks.length === 0) {\n    return action => hasExpectedRequestMetadata(action, [\"fulfilled\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isFulfilled()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.fulfilled));\n}\nfunction isAsyncThunkAction() {\n  for (var _len0 = arguments.length, asyncThunks = new Array(_len0), _key0 = 0; _key0 < _len0; _key0++) {\n    asyncThunks[_key0] = arguments[_key0];\n  }\n  if (asyncThunks.length === 0) {\n    return action => hasExpectedRequestMetadata(action, [\"pending\", \"fulfilled\", \"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isAsyncThunkAction()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.flatMap(asyncThunk => [asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled]));\n}\n\n// src/nanoid.ts\nvar urlAlphabet = \"ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW\";\nvar nanoid = function () {\n  let size = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 21;\n  let id = \"\";\n  let i = size;\n  while (i--) {\n    id += urlAlphabet[Math.random() * 64 | 0];\n  }\n  return id;\n};\n\n// src/createAsyncThunk.ts\nvar commonProperties = [\"name\", \"message\", \"stack\", \"code\"];\nvar RejectWithValue = class RejectWithValue {\n  constructor(payload, meta) {\n    /*\n    type-only property to distinguish between RejectWithValue and FulfillWithMeta\n    does not exist at runtime\n    */\n    _defineProperty(this, \"_type\", void 0);\n    this.payload = payload;\n    this.meta = meta;\n  }\n};\nvar FulfillWithMeta = class FulfillWithMeta {\n  constructor(payload, meta) {\n    /*\n    type-only property to distinguish between RejectWithValue and FulfillWithMeta\n    does not exist at runtime\n    */\n    _defineProperty(this, \"_type\", void 0);\n    this.payload = payload;\n    this.meta = meta;\n  }\n};\nvar miniSerializeError = value => {\n  if (typeof value === \"object\" && value !== null) {\n    const simpleError = {};\n    for (const property of commonProperties) {\n      if (typeof value[property] === \"string\") {\n        simpleError[property] = value[property];\n      }\n    }\n    return simpleError;\n  }\n  return {\n    message: String(value)\n  };\n};\nvar externalAbortMessage = \"External signal was aborted\";\nvar createAsyncThunk = /* @__PURE__ */(() => {\n  function createAsyncThunk2(typePrefix, payloadCreator, options) {\n    const fulfilled = createAction(typePrefix + \"/fulfilled\", (payload, requestId, arg, meta) => ({\n      payload,\n      meta: _objectSpread(_objectSpread({}, meta || {}), {}, {\n        arg,\n        requestId,\n        requestStatus: \"fulfilled\"\n      })\n    }));\n    const pending = createAction(typePrefix + \"/pending\", (requestId, arg, meta) => ({\n      payload: void 0,\n      meta: _objectSpread(_objectSpread({}, meta || {}), {}, {\n        arg,\n        requestId,\n        requestStatus: \"pending\"\n      })\n    }));\n    const rejected = createAction(typePrefix + \"/rejected\", (error, requestId, arg, payload, meta) => ({\n      payload,\n      error: (options && options.serializeError || miniSerializeError)(error || \"Rejected\"),\n      meta: _objectSpread(_objectSpread({}, meta || {}), {}, {\n        arg,\n        requestId,\n        rejectedWithValue: !!payload,\n        requestStatus: \"rejected\",\n        aborted: (error === null || error === void 0 ? void 0 : error.name) === \"AbortError\",\n        condition: (error === null || error === void 0 ? void 0 : error.name) === \"ConditionError\"\n      })\n    }));\n    function actionCreator(arg) {\n      let {\n        signal\n      } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      return (dispatch, getState, extra) => {\n        const requestId = options !== null && options !== void 0 && options.idGenerator ? options.idGenerator(arg) : nanoid();\n        const abortController = new AbortController();\n        let abortHandler;\n        let abortReason;\n        function abort(reason) {\n          abortReason = reason;\n          abortController.abort();\n        }\n        if (signal) {\n          if (signal.aborted) {\n            abort(externalAbortMessage);\n          } else {\n            signal.addEventListener(\"abort\", () => abort(externalAbortMessage), {\n              once: true\n            });\n          }\n        }\n        const promise = async function () {\n          let finalAction;\n          try {\n            var _options$condition, _options$getPendingMe;\n            let conditionResult = options === null || options === void 0 || (_options$condition = options.condition) === null || _options$condition === void 0 ? void 0 : _options$condition.call(options, arg, {\n              getState,\n              extra\n            });\n            if (isThenable(conditionResult)) {\n              conditionResult = await conditionResult;\n            }\n            if (conditionResult === false || abortController.signal.aborted) {\n              throw {\n                name: \"ConditionError\",\n                message: \"Aborted due to condition callback returning false.\"\n              };\n            }\n            const abortedPromise = new Promise((_, reject) => {\n              abortHandler = () => {\n                reject({\n                  name: \"AbortError\",\n                  message: abortReason || \"Aborted\"\n                });\n              };\n              abortController.signal.addEventListener(\"abort\", abortHandler);\n            });\n            dispatch(pending(requestId, arg, options === null || options === void 0 || (_options$getPendingMe = options.getPendingMeta) === null || _options$getPendingMe === void 0 ? void 0 : _options$getPendingMe.call(options, {\n              requestId,\n              arg\n            }, {\n              getState,\n              extra\n            })));\n            finalAction = await Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {\n              dispatch,\n              getState,\n              extra,\n              requestId,\n              signal: abortController.signal,\n              abort,\n              rejectWithValue: (value, meta) => {\n                return new RejectWithValue(value, meta);\n              },\n              fulfillWithValue: (value, meta) => {\n                return new FulfillWithMeta(value, meta);\n              }\n            })).then(result => {\n              if (result instanceof RejectWithValue) {\n                throw result;\n              }\n              if (result instanceof FulfillWithMeta) {\n                return fulfilled(result.payload, requestId, arg, result.meta);\n              }\n              return fulfilled(result, requestId, arg);\n            })]);\n          } catch (err) {\n            finalAction = err instanceof RejectWithValue ? rejected(null, requestId, arg, err.payload, err.meta) : rejected(err, requestId, arg);\n          } finally {\n            if (abortHandler) {\n              abortController.signal.removeEventListener(\"abort\", abortHandler);\n            }\n          }\n          const skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && finalAction.meta.condition;\n          if (!skipDispatch) {\n            dispatch(finalAction);\n          }\n          return finalAction;\n        }();\n        return Object.assign(promise, {\n          abort,\n          requestId,\n          arg,\n          unwrap() {\n            return promise.then(unwrapResult);\n          }\n        });\n      };\n    }\n    return Object.assign(actionCreator, {\n      pending,\n      rejected,\n      fulfilled,\n      settled: isAnyOf(rejected, fulfilled),\n      typePrefix\n    });\n  }\n  createAsyncThunk2.withTypes = () => createAsyncThunk2;\n  return createAsyncThunk2;\n})();\nfunction unwrapResult(action) {\n  if (action.meta && action.meta.rejectedWithValue) {\n    throw action.payload;\n  }\n  if (action.error) {\n    throw action.error;\n  }\n  return action.payload;\n}\nfunction isThenable(value) {\n  return value !== null && typeof value === \"object\" && typeof value.then === \"function\";\n}\n\n// src/createSlice.ts\nvar asyncThunkSymbol = /* @__PURE__ */Symbol.for(\"rtk-slice-createasyncthunk\");\nvar asyncThunkCreator = {\n  [asyncThunkSymbol]: createAsyncThunk\n};\nvar ReducerType = /* @__PURE__ */(ReducerType2 => {\n  ReducerType2[\"reducer\"] = \"reducer\";\n  ReducerType2[\"reducerWithPrepare\"] = \"reducerWithPrepare\";\n  ReducerType2[\"asyncThunk\"] = \"asyncThunk\";\n  return ReducerType2;\n})(ReducerType || {});\nfunction getType(slice, actionKey) {\n  return \"\".concat(slice, \"/\").concat(actionKey);\n}\nfunction buildCreateSlice() {\n  var _creators$asyncThunk;\n  let {\n    creators\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const cAT = creators === null || creators === void 0 || (_creators$asyncThunk = creators.asyncThunk) === null || _creators$asyncThunk === void 0 ? void 0 : _creators$asyncThunk[asyncThunkSymbol];\n  return function createSlice2(options) {\n    const {\n      name,\n      reducerPath = name\n    } = options;\n    if (!name) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(11) : \"`name` is a required option for createSlice\");\n    }\n    if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\n      if (options.initialState === void 0) {\n        console.error(\"You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`\");\n      }\n    }\n    const reducers = (typeof options.reducers === \"function\" ? options.reducers(buildReducerCreators()) : options.reducers) || {};\n    const reducerNames = Object.keys(reducers);\n    const context = {\n      sliceCaseReducersByName: {},\n      sliceCaseReducersByType: {},\n      actionCreators: {},\n      sliceMatchers: []\n    };\n    const contextMethods = {\n      addCase(typeOrActionCreator, reducer2) {\n        const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n        if (!type) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(12) : \"`context.addCase` cannot be called with an empty action type\");\n        }\n        if (type in context.sliceCaseReducersByType) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(13) : \"`context.addCase` cannot be called with two reducers for the same action type: \" + type);\n        }\n        context.sliceCaseReducersByType[type] = reducer2;\n        return contextMethods;\n      },\n      addMatcher(matcher, reducer2) {\n        context.sliceMatchers.push({\n          matcher,\n          reducer: reducer2\n        });\n        return contextMethods;\n      },\n      exposeAction(name2, actionCreator) {\n        context.actionCreators[name2] = actionCreator;\n        return contextMethods;\n      },\n      exposeCaseReducer(name2, reducer2) {\n        context.sliceCaseReducersByName[name2] = reducer2;\n        return contextMethods;\n      }\n    };\n    reducerNames.forEach(reducerName => {\n      const reducerDefinition = reducers[reducerName];\n      const reducerDetails = {\n        reducerName,\n        type: getType(name, reducerName),\n        createNotation: typeof options.reducers === \"function\"\n      };\n      if (isAsyncThunkSliceReducerDefinition(reducerDefinition)) {\n        handleThunkCaseReducerDefinition(reducerDetails, reducerDefinition, contextMethods, cAT);\n      } else {\n        handleNormalReducerDefinition(reducerDetails, reducerDefinition, contextMethods);\n      }\n    });\n    function buildReducer() {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (typeof options.extraReducers === \"object\") {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(14) : \"The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\n        }\n      }\n      const [extraReducers = {}, actionMatchers = [], defaultCaseReducer = void 0] = typeof options.extraReducers === \"function\" ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers];\n      const finalCaseReducers = _objectSpread(_objectSpread({}, extraReducers), context.sliceCaseReducersByType);\n      return createReducer(options.initialState, builder => {\n        for (let key in finalCaseReducers) {\n          builder.addCase(key, finalCaseReducers[key]);\n        }\n        for (let sM of context.sliceMatchers) {\n          builder.addMatcher(sM.matcher, sM.reducer);\n        }\n        for (let m of actionMatchers) {\n          builder.addMatcher(m.matcher, m.reducer);\n        }\n        if (defaultCaseReducer) {\n          builder.addDefaultCase(defaultCaseReducer);\n        }\n      });\n    }\n    const selectSelf = state => state;\n    const injectedSelectorCache = /* @__PURE__ */new Map();\n    const injectedStateCache = /* @__PURE__ */new WeakMap();\n    let _reducer;\n    function reducer(state, action) {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer(state, action);\n    }\n    function getInitialState() {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer.getInitialState();\n    }\n    function makeSelectorProps(reducerPath2) {\n      let injected = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      function selectSlice(state) {\n        let sliceState = state[reducerPath2];\n        if (typeof sliceState === \"undefined\") {\n          if (injected) {\n            sliceState = getOrInsertComputed(injectedStateCache, selectSlice, getInitialState);\n          } else if (process.env.NODE_ENV !== \"production\") {\n            throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(15) : \"selectSlice returned undefined for an uninjected slice reducer\");\n          }\n        }\n        return sliceState;\n      }\n      function getSelectors() {\n        let selectState = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : selectSelf;\n        const selectorCache = getOrInsertComputed(injectedSelectorCache, injected, () => /* @__PURE__ */new WeakMap());\n        return getOrInsertComputed(selectorCache, selectState, () => {\n          const map = {};\n          for (const [name2, selector] of Object.entries((_options$selectors = options.selectors) !== null && _options$selectors !== void 0 ? _options$selectors : {})) {\n            var _options$selectors;\n            map[name2] = wrapSelector(selector, selectState, () => getOrInsertComputed(injectedStateCache, selectState, getInitialState), injected);\n          }\n          return map;\n        });\n      }\n      return {\n        reducerPath: reducerPath2,\n        getSelectors,\n        get selectors() {\n          return getSelectors(selectSlice);\n        },\n        selectSlice\n      };\n    }\n    const slice = _objectSpread(_objectSpread({\n      name,\n      reducer,\n      actions: context.actionCreators,\n      caseReducers: context.sliceCaseReducersByName,\n      getInitialState\n    }, makeSelectorProps(reducerPath)), {}, {\n      injectInto(injectable) {\n        let _ref4 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n          {\n            reducerPath: pathOpt\n          } = _ref4,\n          config = _objectWithoutProperties(_ref4, _excluded);\n        const newReducerPath = pathOpt !== null && pathOpt !== void 0 ? pathOpt : reducerPath;\n        injectable.inject({\n          reducerPath: newReducerPath,\n          reducer\n        }, config);\n        return _objectSpread(_objectSpread({}, slice), makeSelectorProps(newReducerPath, true));\n      }\n    });\n    return slice;\n  };\n}\nfunction wrapSelector(selector, selectState, getInitialState, injected) {\n  function wrapper(rootState) {\n    let sliceState = selectState(rootState);\n    if (typeof sliceState === \"undefined\") {\n      if (injected) {\n        sliceState = getInitialState();\n      } else if (process.env.NODE_ENV !== \"production\") {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(16) : \"selectState returned undefined for an uninjected slice reducer\");\n      }\n    }\n    for (var _len1 = arguments.length, args = new Array(_len1 > 1 ? _len1 - 1 : 0), _key1 = 1; _key1 < _len1; _key1++) {\n      args[_key1 - 1] = arguments[_key1];\n    }\n    return selector(sliceState, ...args);\n  }\n  wrapper.unwrapped = selector;\n  return wrapper;\n}\nvar createSlice = /* @__PURE__ */buildCreateSlice();\nfunction buildReducerCreators() {\n  function asyncThunk(payloadCreator, config) {\n    return _objectSpread({\n      _reducerDefinitionType: \"asyncThunk\" /* asyncThunk */,\n      payloadCreator\n    }, config);\n  }\n  asyncThunk.withTypes = () => asyncThunk;\n  return {\n    reducer(caseReducer) {\n      return Object.assign({\n        // hack so the wrapping function has the same name as the original\n        // we need to create a wrapper so the `reducerDefinitionType` is not assigned to the original\n        [caseReducer.name]() {\n          return caseReducer(...arguments);\n        }\n      }[caseReducer.name], {\n        _reducerDefinitionType: \"reducer\" /* reducer */\n      });\n    },\n    preparedReducer(prepare, reducer) {\n      return {\n        _reducerDefinitionType: \"reducerWithPrepare\" /* reducerWithPrepare */,\n        prepare,\n        reducer\n      };\n    },\n    asyncThunk\n  };\n}\nfunction handleNormalReducerDefinition(_ref5, maybeReducerWithPrepare, context) {\n  let {\n    type,\n    reducerName,\n    createNotation\n  } = _ref5;\n  let caseReducer;\n  let prepareCallback;\n  if (\"reducer\" in maybeReducerWithPrepare) {\n    if (createNotation && !isCaseReducerWithPrepareDefinition(maybeReducerWithPrepare)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(17) : \"Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.\");\n    }\n    caseReducer = maybeReducerWithPrepare.reducer;\n    prepareCallback = maybeReducerWithPrepare.prepare;\n  } else {\n    caseReducer = maybeReducerWithPrepare;\n  }\n  context.addCase(type, caseReducer).exposeCaseReducer(reducerName, caseReducer).exposeAction(reducerName, prepareCallback ? createAction(type, prepareCallback) : createAction(type));\n}\nfunction isAsyncThunkSliceReducerDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"asyncThunk\" /* asyncThunk */;\n}\nfunction isCaseReducerWithPrepareDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"reducerWithPrepare\" /* reducerWithPrepare */;\n}\nfunction handleThunkCaseReducerDefinition(_ref6, reducerDefinition, context, cAT) {\n  let {\n    type,\n    reducerName\n  } = _ref6;\n  if (!cAT) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(18) : \"Cannot use `create.asyncThunk` in the built-in `createSlice`. Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.\");\n  }\n  const {\n    payloadCreator,\n    fulfilled,\n    pending,\n    rejected,\n    settled,\n    options\n  } = reducerDefinition;\n  const thunk = cAT(type, payloadCreator, options);\n  context.exposeAction(reducerName, thunk);\n  if (fulfilled) {\n    context.addCase(thunk.fulfilled, fulfilled);\n  }\n  if (pending) {\n    context.addCase(thunk.pending, pending);\n  }\n  if (rejected) {\n    context.addCase(thunk.rejected, rejected);\n  }\n  if (settled) {\n    context.addMatcher(thunk.settled, settled);\n  }\n  context.exposeCaseReducer(reducerName, {\n    fulfilled: fulfilled || noop,\n    pending: pending || noop,\n    rejected: rejected || noop,\n    settled: settled || noop\n  });\n}\nfunction noop() {}\n\n// src/entities/entity_state.ts\nfunction getInitialEntityState() {\n  return {\n    ids: [],\n    entities: {}\n  };\n}\nfunction createInitialStateFactory(stateAdapter) {\n  function getInitialState() {\n    let additionalState = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let entities = arguments.length > 1 ? arguments[1] : undefined;\n    const state = Object.assign(getInitialEntityState(), additionalState);\n    return entities ? stateAdapter.setAll(state, entities) : state;\n  }\n  return {\n    getInitialState\n  };\n}\n\n// src/entities/state_selectors.ts\nfunction createSelectorsFactory() {\n  function getSelectors(selectState) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const {\n      createSelector: createSelector2 = createDraftSafeSelector\n    } = options;\n    const selectIds = state => state.ids;\n    const selectEntities = state => state.entities;\n    const selectAll = createSelector2(selectIds, selectEntities, (ids, entities) => ids.map(id => entities[id]));\n    const selectId = (_, id) => id;\n    const selectById = (entities, id) => entities[id];\n    const selectTotal = createSelector2(selectIds, ids => ids.length);\n    if (!selectState) {\n      return {\n        selectIds,\n        selectEntities,\n        selectAll,\n        selectTotal,\n        selectById: createSelector2(selectEntities, selectId, selectById)\n      };\n    }\n    const selectGlobalizedEntities = createSelector2(selectState, selectEntities);\n    return {\n      selectIds: createSelector2(selectState, selectIds),\n      selectEntities: selectGlobalizedEntities,\n      selectAll: createSelector2(selectState, selectAll),\n      selectTotal: createSelector2(selectState, selectTotal),\n      selectById: createSelector2(selectGlobalizedEntities, selectId, selectById)\n    };\n  }\n  return {\n    getSelectors\n  };\n}\n\n// src/entities/state_adapter.ts\nimport { produce as createNextState3, isDraft as isDraft3 } from \"immer\";\nvar isDraftTyped = isDraft3;\nfunction createSingleArgumentStateOperator(mutator) {\n  const operator = createStateOperator((_, state) => mutator(state));\n  return function operation(state) {\n    return operator(state, void 0);\n  };\n}\nfunction createStateOperator(mutator) {\n  return function operation(state, arg) {\n    function isPayloadActionArgument(arg2) {\n      return isFSA(arg2);\n    }\n    const runMutator = draft => {\n      if (isPayloadActionArgument(arg)) {\n        mutator(arg.payload, draft);\n      } else {\n        mutator(arg, draft);\n      }\n    };\n    if (isDraftTyped(state)) {\n      runMutator(state);\n      return state;\n    }\n    return createNextState3(state, runMutator);\n  };\n}\n\n// src/entities/utils.ts\nimport { current as current2, isDraft as isDraft4 } from \"immer\";\nfunction selectIdValue(entity, selectId) {\n  const key = selectId(entity);\n  if (process.env.NODE_ENV !== \"production\" && key === void 0) {\n    console.warn(\"The entity passed to the `selectId` implementation returned undefined.\", \"You should probably provide your own `selectId` implementation.\", \"The entity that was passed:\", entity, \"The `selectId` implementation:\", selectId.toString());\n  }\n  return key;\n}\nfunction ensureEntitiesArray(entities) {\n  if (!Array.isArray(entities)) {\n    entities = Object.values(entities);\n  }\n  return entities;\n}\nfunction getCurrent(value) {\n  return isDraft4(value) ? current2(value) : value;\n}\nfunction splitAddedUpdatedEntities(newEntities, selectId, state) {\n  newEntities = ensureEntitiesArray(newEntities);\n  const existingIdsArray = getCurrent(state.ids);\n  const existingIds = new Set(existingIdsArray);\n  const added = [];\n  const addedIds = /* @__PURE__ */new Set([]);\n  const updated = [];\n  for (const entity of newEntities) {\n    const id = selectIdValue(entity, selectId);\n    if (existingIds.has(id) || addedIds.has(id)) {\n      updated.push({\n        id,\n        changes: entity\n      });\n    } else {\n      addedIds.add(id);\n      added.push(entity);\n    }\n  }\n  return [added, updated, existingIdsArray];\n}\n\n// src/entities/unsorted_state_adapter.ts\nfunction createUnsortedStateAdapter(selectId) {\n  function addOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (key in state.entities) {\n      return;\n    }\n    state.ids.push(key);\n    state.entities[key] = entity;\n  }\n  function addManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      addOneMutably(entity, state);\n    }\n  }\n  function setOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (!(key in state.entities)) {\n      state.ids.push(key);\n    }\n    ;\n    state.entities[key] = entity;\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      setOneMutably(entity, state);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.ids = [];\n    state.entities = {};\n    addManyMutably(newEntities, state);\n  }\n  function removeOneMutably(key, state) {\n    return removeManyMutably([key], state);\n  }\n  function removeManyMutably(keys, state) {\n    let didMutate = false;\n    keys.forEach(key => {\n      if (key in state.entities) {\n        delete state.entities[key];\n        didMutate = true;\n      }\n    });\n    if (didMutate) {\n      state.ids = state.ids.filter(id => id in state.entities);\n    }\n  }\n  function removeAllMutably(state) {\n    Object.assign(state, {\n      ids: [],\n      entities: {}\n    });\n  }\n  function takeNewKey(keys, update, state) {\n    const original3 = state.entities[update.id];\n    if (original3 === void 0) {\n      return false;\n    }\n    const updated = Object.assign({}, original3, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    const hasNewKey = newKey !== update.id;\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete state.entities[update.id];\n    }\n    ;\n    state.entities[newKey] = updated;\n    return hasNewKey;\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    const newKeys = {};\n    const updatesPerEntity = {};\n    updates.forEach(update => {\n      if (update.id in state.entities) {\n        var _updatesPerEntity$upd;\n        updatesPerEntity[update.id] = {\n          id: update.id,\n          // Spreads ignore falsy values, so this works even if there isn't\n          // an existing update already at this key\n          changes: _objectSpread(_objectSpread({}, (_updatesPerEntity$upd = updatesPerEntity[update.id]) === null || _updatesPerEntity$upd === void 0 ? void 0 : _updatesPerEntity$upd.changes), update.changes)\n        };\n      }\n    });\n    updates = Object.values(updatesPerEntity);\n    const didMutateEntities = updates.length > 0;\n    if (didMutateEntities) {\n      const didMutateIds = updates.filter(update => takeNewKey(newKeys, update, state)).length > 0;\n      if (didMutateIds) {\n        state.ids = Object.values(state.entities).map(e => selectIdValue(e, selectId));\n      }\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    addManyMutably(added, state);\n    updateManyMutably(updated, state);\n  }\n  return {\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably)\n  };\n}\n\n// src/entities/sorted_state_adapter.ts\nfunction findInsertIndex(sortedItems, item, comparisonFunction) {\n  let lowIndex = 0;\n  let highIndex = sortedItems.length;\n  while (lowIndex < highIndex) {\n    let middleIndex = lowIndex + highIndex >>> 1;\n    const currentItem = sortedItems[middleIndex];\n    const res = comparisonFunction(item, currentItem);\n    if (res >= 0) {\n      lowIndex = middleIndex + 1;\n    } else {\n      highIndex = middleIndex;\n    }\n  }\n  return lowIndex;\n}\nfunction insert(sortedItems, item, comparisonFunction) {\n  const insertAtIndex = findInsertIndex(sortedItems, item, comparisonFunction);\n  sortedItems.splice(insertAtIndex, 0, item);\n  return sortedItems;\n}\nfunction createSortedStateAdapter(selectId, comparer) {\n  const {\n    removeOne,\n    removeMany,\n    removeAll\n  } = createUnsortedStateAdapter(selectId);\n  function addOneMutably(entity, state) {\n    return addManyMutably([entity], state);\n  }\n  function addManyMutably(newEntities, state, existingIds) {\n    newEntities = ensureEntitiesArray(newEntities);\n    const existingKeys = new Set(existingIds !== null && existingIds !== void 0 ? existingIds : getCurrent(state.ids));\n    const models = newEntities.filter(model => !existingKeys.has(selectIdValue(model, selectId)));\n    if (models.length !== 0) {\n      mergeFunction(state, models);\n    }\n  }\n  function setOneMutably(entity, state) {\n    return setManyMutably([entity], state);\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    if (newEntities.length !== 0) {\n      for (const item of newEntities) {\n        delete state.entities[selectId(item)];\n      }\n      mergeFunction(state, newEntities);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.entities = {};\n    state.ids = [];\n    addManyMutably(newEntities, state, []);\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    let appliedUpdates = false;\n    let replacedIds = false;\n    for (let update of updates) {\n      const entity = state.entities[update.id];\n      if (!entity) {\n        continue;\n      }\n      appliedUpdates = true;\n      Object.assign(entity, update.changes);\n      const newId = selectId(entity);\n      if (update.id !== newId) {\n        replacedIds = true;\n        delete state.entities[update.id];\n        const oldIndex = state.ids.indexOf(update.id);\n        state.ids[oldIndex] = newId;\n        state.entities[newId] = entity;\n      }\n    }\n    if (appliedUpdates) {\n      mergeFunction(state, [], appliedUpdates, replacedIds);\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated, existingIdsArray] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    if (added.length) {\n      addManyMutably(added, state, existingIdsArray);\n    }\n    if (updated.length) {\n      updateManyMutably(updated, state);\n    }\n  }\n  function areArraysEqual(a, b) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] === b[i]) {\n        continue;\n      }\n      return false;\n    }\n    return true;\n  }\n  const mergeFunction = (state, addedItems, appliedUpdates, replacedIds) => {\n    const currentEntities = getCurrent(state.entities);\n    const currentIds = getCurrent(state.ids);\n    const stateEntities = state.entities;\n    let ids = currentIds;\n    if (replacedIds) {\n      ids = new Set(currentIds);\n    }\n    let sortedEntities = [];\n    for (const id of ids) {\n      const entity = currentEntities[id];\n      if (entity) {\n        sortedEntities.push(entity);\n      }\n    }\n    const wasPreviouslyEmpty = sortedEntities.length === 0;\n    for (const item of addedItems) {\n      stateEntities[selectId(item)] = item;\n      if (!wasPreviouslyEmpty) {\n        insert(sortedEntities, item, comparer);\n      }\n    }\n    if (wasPreviouslyEmpty) {\n      sortedEntities = addedItems.slice().sort(comparer);\n    } else if (appliedUpdates) {\n      sortedEntities.sort(comparer);\n    }\n    const newSortedIds = sortedEntities.map(selectId);\n    if (!areArraysEqual(currentIds, newSortedIds)) {\n      state.ids = newSortedIds;\n    }\n  };\n  return {\n    removeOne,\n    removeMany,\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably)\n  };\n}\n\n// src/entities/create_adapter.ts\nfunction createEntityAdapter() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    selectId,\n    sortComparer\n  } = _objectSpread({\n    sortComparer: false,\n    selectId: instance => instance.id\n  }, options);\n  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  const stateFactory = createInitialStateFactory(stateAdapter);\n  const selectorsFactory = createSelectorsFactory();\n  return _objectSpread(_objectSpread(_objectSpread({\n    selectId,\n    sortComparer\n  }, stateFactory), selectorsFactory), stateAdapter);\n}\n\n// src/listenerMiddleware/index.ts\nimport { isAction as isAction3 } from \"redux\";\n\n// src/listenerMiddleware/exceptions.ts\nvar task = \"task\";\nvar listener = \"listener\";\nvar completed = \"completed\";\nvar cancelled = \"cancelled\";\nvar taskCancelled = \"task-\".concat(cancelled);\nvar taskCompleted = \"task-\".concat(completed);\nvar listenerCancelled = \"\".concat(listener, \"-\").concat(cancelled);\nvar listenerCompleted = \"\".concat(listener, \"-\").concat(completed);\nvar TaskAbortError = class TaskAbortError {\n  constructor(code) {\n    _defineProperty(this, \"name\", \"TaskAbortError\");\n    _defineProperty(this, \"message\", void 0);\n    this.code = code;\n    this.message = \"\".concat(task, \" \").concat(cancelled, \" (reason: \").concat(code, \")\");\n  }\n};\n\n// src/listenerMiddleware/utils.ts\nvar assertFunction = (func, expected) => {\n  if (typeof func !== \"function\") {\n    throw new TypeError(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(32) : \"\".concat(expected, \" is not a function\"));\n  }\n};\nvar noop2 = () => {};\nvar catchRejection = function (promise) {\n  let onError = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop2;\n  promise.catch(onError);\n  return promise;\n};\nvar addAbortSignalListener = (abortSignal, callback) => {\n  abortSignal.addEventListener(\"abort\", callback, {\n    once: true\n  });\n  return () => abortSignal.removeEventListener(\"abort\", callback);\n};\nvar abortControllerWithReason = (abortController, reason) => {\n  const signal = abortController.signal;\n  if (signal.aborted) {\n    return;\n  }\n  if (!(\"reason\" in signal)) {\n    Object.defineProperty(signal, \"reason\", {\n      enumerable: true,\n      value: reason,\n      configurable: true,\n      writable: true\n    });\n  }\n  ;\n  abortController.abort(reason);\n};\n\n// src/listenerMiddleware/task.ts\nvar validateActive = signal => {\n  if (signal.aborted) {\n    const {\n      reason\n    } = signal;\n    throw new TaskAbortError(reason);\n  }\n};\nfunction raceWithSignal(signal, promise) {\n  let cleanup = noop2;\n  return new Promise((resolve, reject) => {\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason));\n    if (signal.aborted) {\n      notifyRejection();\n      return;\n    }\n    cleanup = addAbortSignalListener(signal, notifyRejection);\n    promise.finally(() => cleanup()).then(resolve, reject);\n  }).finally(() => {\n    cleanup = noop2;\n  });\n}\nvar runTask = async (task2, cleanUp) => {\n  try {\n    await Promise.resolve();\n    const value = await task2();\n    return {\n      status: \"ok\",\n      value\n    };\n  } catch (error) {\n    return {\n      status: error instanceof TaskAbortError ? \"cancelled\" : \"rejected\",\n      error\n    };\n  } finally {\n    cleanUp === null || cleanUp === void 0 || cleanUp();\n  }\n};\nvar createPause = signal => {\n  return promise => {\n    return catchRejection(raceWithSignal(signal, promise).then(output => {\n      validateActive(signal);\n      return output;\n    }));\n  };\n};\nvar createDelay = signal => {\n  const pause = createPause(signal);\n  return timeoutMs => {\n    return pause(new Promise(resolve => setTimeout(resolve, timeoutMs)));\n  };\n};\n\n// src/listenerMiddleware/index.ts\nvar {\n  assign\n} = Object;\nvar INTERNAL_NIL_TOKEN = {};\nvar alm = \"listenerMiddleware\";\nvar createFork = (parentAbortSignal, parentBlockingPromises) => {\n  const linkControllers = controller => addAbortSignalListener(parentAbortSignal, () => abortControllerWithReason(controller, parentAbortSignal.reason));\n  return (taskExecutor, opts) => {\n    assertFunction(taskExecutor, \"taskExecutor\");\n    const childAbortController = new AbortController();\n    linkControllers(childAbortController);\n    const result = runTask(async () => {\n      validateActive(parentAbortSignal);\n      validateActive(childAbortController.signal);\n      const result2 = await taskExecutor({\n        pause: createPause(childAbortController.signal),\n        delay: createDelay(childAbortController.signal),\n        signal: childAbortController.signal\n      });\n      validateActive(childAbortController.signal);\n      return result2;\n    }, () => abortControllerWithReason(childAbortController, taskCompleted));\n    if (opts !== null && opts !== void 0 && opts.autoJoin) {\n      parentBlockingPromises.push(result.catch(noop2));\n    }\n    return {\n      result: createPause(parentAbortSignal)(result),\n      cancel() {\n        abortControllerWithReason(childAbortController, taskCancelled);\n      }\n    };\n  };\n};\nvar createTakePattern = (startListening, signal) => {\n  const take = async (predicate, timeout) => {\n    validateActive(signal);\n    let unsubscribe = () => {};\n    const tuplePromise = new Promise((resolve, reject) => {\n      let stopListening = startListening({\n        predicate,\n        effect: (action, listenerApi) => {\n          listenerApi.unsubscribe();\n          resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);\n        }\n      });\n      unsubscribe = () => {\n        stopListening();\n        reject();\n      };\n    });\n    const promises = [tuplePromise];\n    if (timeout != null) {\n      promises.push(new Promise(resolve => setTimeout(resolve, timeout, null)));\n    }\n    try {\n      const output = await raceWithSignal(signal, Promise.race(promises));\n      validateActive(signal);\n      return output;\n    } finally {\n      unsubscribe();\n    }\n  };\n  return (predicate, timeout) => catchRejection(take(predicate, timeout));\n};\nvar getListenerEntryPropsFrom = options => {\n  let {\n    type,\n    actionCreator,\n    matcher,\n    predicate,\n    effect\n  } = options;\n  if (type) {\n    predicate = createAction(type).match;\n  } else if (actionCreator) {\n    type = actionCreator.type;\n    predicate = actionCreator.match;\n  } else if (matcher) {\n    predicate = matcher;\n  } else if (predicate) {} else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(21) : \"Creating or removing a listener requires one of the known fields for matching an action\");\n  }\n  assertFunction(effect, \"options.listener\");\n  return {\n    predicate,\n    type,\n    effect\n  };\n};\nvar createListenerEntry = /* @__PURE__ */assign(options => {\n  const {\n    type,\n    predicate,\n    effect\n  } = getListenerEntryPropsFrom(options);\n  const entry = {\n    id: nanoid(),\n    effect,\n    type,\n    predicate,\n    pending: /* @__PURE__ */new Set(),\n    unsubscribe: () => {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(22) : \"Unsubscribe not initialized\");\n    }\n  };\n  return entry;\n}, {\n  withTypes: () => createListenerEntry\n});\nvar findListenerEntry = (listenerMap, options) => {\n  const {\n    type,\n    effect,\n    predicate\n  } = getListenerEntryPropsFrom(options);\n  return Array.from(listenerMap.values()).find(entry => {\n    const matchPredicateOrType = typeof type === \"string\" ? entry.type === type : entry.predicate === predicate;\n    return matchPredicateOrType && entry.effect === effect;\n  });\n};\nvar cancelActiveListeners = entry => {\n  entry.pending.forEach(controller => {\n    abortControllerWithReason(controller, listenerCancelled);\n  });\n};\nvar createClearListenerMiddleware = listenerMap => {\n  return () => {\n    listenerMap.forEach(cancelActiveListeners);\n    listenerMap.clear();\n  };\n};\nvar safelyNotifyError = (errorHandler, errorToNotify, errorInfo) => {\n  try {\n    errorHandler(errorToNotify, errorInfo);\n  } catch (errorHandlerError) {\n    setTimeout(() => {\n      throw errorHandlerError;\n    }, 0);\n  }\n};\nvar addListener = /* @__PURE__ */assign(/* @__PURE__ */createAction(\"\".concat(alm, \"/add\")), {\n  withTypes: () => addListener\n});\nvar clearAllListeners = /* @__PURE__ */createAction(\"\".concat(alm, \"/removeAll\"));\nvar removeListener = /* @__PURE__ */assign(/* @__PURE__ */createAction(\"\".concat(alm, \"/remove\")), {\n  withTypes: () => removeListener\n});\nvar defaultErrorHandler = function () {\n  for (var _len10 = arguments.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n    args[_key10] = arguments[_key10];\n  }\n  console.error(\"\".concat(alm, \"/error\"), ...args);\n};\nvar createListenerMiddleware = function () {\n  let middlewareOptions = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const listenerMap = /* @__PURE__ */new Map();\n  const {\n    extra,\n    onError = defaultErrorHandler\n  } = middlewareOptions;\n  assertFunction(onError, \"onError\");\n  const insertEntry = entry => {\n    entry.unsubscribe = () => listenerMap.delete(entry.id);\n    listenerMap.set(entry.id, entry);\n    return cancelOptions => {\n      entry.unsubscribe();\n      if (cancelOptions !== null && cancelOptions !== void 0 && cancelOptions.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    };\n  };\n  const startListening = options => {\n    var _findListenerEntry;\n    const entry = (_findListenerEntry = findListenerEntry(listenerMap, options)) !== null && _findListenerEntry !== void 0 ? _findListenerEntry : createListenerEntry(options);\n    return insertEntry(entry);\n  };\n  assign(startListening, {\n    withTypes: () => startListening\n  });\n  const stopListening = options => {\n    const entry = findListenerEntry(listenerMap, options);\n    if (entry) {\n      entry.unsubscribe();\n      if (options.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    }\n    return !!entry;\n  };\n  assign(stopListening, {\n    withTypes: () => stopListening\n  });\n  const notifyListener = async (entry, action, api, getOriginalState) => {\n    const internalTaskController = new AbortController();\n    const take = createTakePattern(startListening, internalTaskController.signal);\n    const autoJoinPromises = [];\n    try {\n      entry.pending.add(internalTaskController);\n      await Promise.resolve(entry.effect(action,\n      // Use assign() rather than ... to avoid extra helper functions added to bundle\n      assign({}, api, {\n        getOriginalState,\n        condition: (predicate, timeout) => take(predicate, timeout).then(Boolean),\n        take,\n        delay: createDelay(internalTaskController.signal),\n        pause: createPause(internalTaskController.signal),\n        extra,\n        signal: internalTaskController.signal,\n        fork: createFork(internalTaskController.signal, autoJoinPromises),\n        unsubscribe: entry.unsubscribe,\n        subscribe: () => {\n          listenerMap.set(entry.id, entry);\n        },\n        cancelActiveListeners: () => {\n          entry.pending.forEach((controller, _, set) => {\n            if (controller !== internalTaskController) {\n              abortControllerWithReason(controller, listenerCancelled);\n              set.delete(controller);\n            }\n          });\n        },\n        cancel: () => {\n          abortControllerWithReason(internalTaskController, listenerCancelled);\n          entry.pending.delete(internalTaskController);\n        },\n        throwIfCancelled: () => {\n          validateActive(internalTaskController.signal);\n        }\n      })));\n    } catch (listenerError) {\n      if (!(listenerError instanceof TaskAbortError)) {\n        safelyNotifyError(onError, listenerError, {\n          raisedBy: \"effect\"\n        });\n      }\n    } finally {\n      await Promise.all(autoJoinPromises);\n      abortControllerWithReason(internalTaskController, listenerCompleted);\n      entry.pending.delete(internalTaskController);\n    }\n  };\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\n  const middleware = api => next => action => {\n    if (!isAction3(action)) {\n      return next(action);\n    }\n    if (addListener.match(action)) {\n      return startListening(action.payload);\n    }\n    if (clearAllListeners.match(action)) {\n      clearListenerMiddleware();\n      return;\n    }\n    if (removeListener.match(action)) {\n      return stopListening(action.payload);\n    }\n    let originalState = api.getState();\n    const getOriginalState = () => {\n      if (originalState === INTERNAL_NIL_TOKEN) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(23) : \"\".concat(alm, \": getOriginalState can only be called synchronously\"));\n      }\n      return originalState;\n    };\n    let result;\n    try {\n      result = next(action);\n      if (listenerMap.size > 0) {\n        const currentState = api.getState();\n        const listenerEntries = Array.from(listenerMap.values());\n        for (const entry of listenerEntries) {\n          let runListener = false;\n          try {\n            runListener = entry.predicate(action, currentState, originalState);\n          } catch (predicateError) {\n            runListener = false;\n            safelyNotifyError(onError, predicateError, {\n              raisedBy: \"predicate\"\n            });\n          }\n          if (!runListener) {\n            continue;\n          }\n          notifyListener(entry, action, api, getOriginalState);\n        }\n      }\n    } finally {\n      originalState = INTERNAL_NIL_TOKEN;\n    }\n    return result;\n  };\n  return {\n    middleware,\n    startListening,\n    stopListening,\n    clearListeners: clearListenerMiddleware\n  };\n};\n\n// src/dynamicMiddleware/index.ts\nimport { compose as compose3 } from \"redux\";\nvar createMiddlewareEntry = middleware => ({\n  middleware,\n  applied: /* @__PURE__ */new Map()\n});\nvar matchInstance = instanceId => action => {\n  var _action$meta2;\n  return (action === null || action === void 0 || (_action$meta2 = action.meta) === null || _action$meta2 === void 0 ? void 0 : _action$meta2.instanceId) === instanceId;\n};\nvar createDynamicMiddleware = () => {\n  const instanceId = nanoid();\n  const middlewareMap = /* @__PURE__ */new Map();\n  const withMiddleware = Object.assign(createAction(\"dynamicMiddleware/add\", function () {\n    for (var _len11 = arguments.length, middlewares = new Array(_len11), _key11 = 0; _key11 < _len11; _key11++) {\n      middlewares[_key11] = arguments[_key11];\n    }\n    return {\n      payload: middlewares,\n      meta: {\n        instanceId\n      }\n    };\n  }), {\n    withTypes: () => withMiddleware\n  });\n  const addMiddleware = Object.assign(function addMiddleware2() {\n    for (var _len12 = arguments.length, middlewares = new Array(_len12), _key12 = 0; _key12 < _len12; _key12++) {\n      middlewares[_key12] = arguments[_key12];\n    }\n    middlewares.forEach(middleware2 => {\n      getOrInsertComputed(middlewareMap, middleware2, createMiddlewareEntry);\n    });\n  }, {\n    withTypes: () => addMiddleware\n  });\n  const getFinalMiddleware = api => {\n    const appliedMiddleware = Array.from(middlewareMap.values()).map(entry => getOrInsertComputed(entry.applied, api, entry.middleware));\n    return compose3(...appliedMiddleware);\n  };\n  const isWithMiddleware = isAllOf(withMiddleware, matchInstance(instanceId));\n  const middleware = api => next => action => {\n    if (isWithMiddleware(action)) {\n      addMiddleware(...action.payload);\n      return api.dispatch;\n    }\n    return getFinalMiddleware(api)(next)(action);\n  };\n  return {\n    middleware,\n    addMiddleware,\n    withMiddleware,\n    instanceId\n  };\n};\n\n// src/combineSlices.ts\nimport { combineReducers as combineReducers2 } from \"redux\";\nvar isSliceLike = maybeSliceLike => \"reducerPath\" in maybeSliceLike && typeof maybeSliceLike.reducerPath === \"string\";\nvar getReducers = slices => slices.flatMap(sliceOrMap => isSliceLike(sliceOrMap) ? [[sliceOrMap.reducerPath, sliceOrMap.reducer]] : Object.entries(sliceOrMap));\nvar ORIGINAL_STATE = Symbol.for(\"rtk-state-proxy-original\");\nvar isStateProxy = value => !!value && !!value[ORIGINAL_STATE];\nvar stateProxyMap = /* @__PURE__ */new WeakMap();\nvar createStateProxy = (state, reducerMap, initialStateCache) => getOrInsertComputed(stateProxyMap, state, () => new Proxy(state, {\n  get: (target, prop, receiver) => {\n    if (prop === ORIGINAL_STATE) return target;\n    const result = Reflect.get(target, prop, receiver);\n    if (typeof result === \"undefined\") {\n      const cached = initialStateCache[prop];\n      if (typeof cached !== \"undefined\") return cached;\n      const reducer = reducerMap[prop];\n      if (reducer) {\n        const reducerResult = reducer(void 0, {\n          type: nanoid()\n        });\n        if (typeof reducerResult === \"undefined\") {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(24) : \"The slice reducer for key \\\"\".concat(prop.toString(), \"\\\" returned undefined when called for selector(). If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.\"));\n        }\n        initialStateCache[prop] = reducerResult;\n        return reducerResult;\n      }\n    }\n    return result;\n  }\n}));\nvar original = state => {\n  if (!isStateProxy(state)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(25) : \"original must be used on state Proxy\");\n  }\n  return state[ORIGINAL_STATE];\n};\nvar emptyObject = {};\nvar noopReducer = function () {\n  let state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : emptyObject;\n  return state;\n};\nfunction combineSlices() {\n  for (var _len13 = arguments.length, slices = new Array(_len13), _key13 = 0; _key13 < _len13; _key13++) {\n    slices[_key13] = arguments[_key13];\n  }\n  const reducerMap = Object.fromEntries(getReducers(slices));\n  const getReducer = () => Object.keys(reducerMap).length ? combineReducers2(reducerMap) : noopReducer;\n  let reducer = getReducer();\n  function combinedReducer(state, action) {\n    return reducer(state, action);\n  }\n  combinedReducer.withLazyLoadedSlices = () => combinedReducer;\n  const initialStateCache = {};\n  const inject = function (slice) {\n    let config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const {\n      reducerPath,\n      reducer: reducerToInject\n    } = slice;\n    const currentReducer = reducerMap[reducerPath];\n    if (!config.overrideExisting && currentReducer && currentReducer !== reducerToInject) {\n      if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\n        console.error(\"called `inject` to override already-existing reducer \".concat(reducerPath, \" without specifying `overrideExisting: true`\"));\n      }\n      return combinedReducer;\n    }\n    if (config.overrideExisting && currentReducer !== reducerToInject) {\n      delete initialStateCache[reducerPath];\n    }\n    reducerMap[reducerPath] = reducerToInject;\n    reducer = getReducer();\n    return combinedReducer;\n  };\n  const selector = Object.assign(function makeSelector(selectorFn, selectState) {\n    return function selector2(state) {\n      for (var _len14 = arguments.length, args = new Array(_len14 > 1 ? _len14 - 1 : 0), _key14 = 1; _key14 < _len14; _key14++) {\n        args[_key14 - 1] = arguments[_key14];\n      }\n      return selectorFn(createStateProxy(selectState ? selectState(state, ...args) : state, reducerMap, initialStateCache), ...args);\n    };\n  }, {\n    original\n  });\n  return Object.assign(combinedReducer, {\n    inject,\n    selector\n  });\n}\n\n// src/formatProdErrorMessage.ts\nfunction formatProdErrorMessage(code) {\n  return \"Minified Redux Toolkit error #\".concat(code, \"; visit https://redux-toolkit.js.org/Errors?code=\").concat(code, \" for the full message or use the non-minified dev environment for full errors. \");\n}\nexport { ReducerType, SHOULD_AUTOBATCH, TaskAbortError, Tuple, addListener, asyncThunkCreator, autoBatchEnhancer, buildCreateSlice, clearAllListeners, combineSlices, configureStore, createAction, createActionCreatorInvariantMiddleware, createAsyncThunk, createDraftSafeSelector, createDraftSafeSelectorCreator, createDynamicMiddleware, createEntityAdapter, createImmutableStateInvariantMiddleware, createListenerMiddleware, produce as createNextState, createReducer, createSelector, createSelectorCreator2 as createSelectorCreator, createSerializableStateInvariantMiddleware, createSlice, current3 as current, findNonSerializableValue, formatProdErrorMessage, freeze, isActionCreator, isAllOf, isAnyOf, isAsyncThunkAction, isDraft5 as isDraft, isFSA as isFluxStandardAction, isFulfilled, isImmutableDefault, isPending, isPlain, isRejected, isRejectedWithValue, lruMemoize, miniSerializeError, nanoid, original2 as original, prepareAutoBatched, removeListener, unwrapResult, weakMapMemoize2 as weakMapMemoize };", "map": {"version": 3, "names": ["produce", "current", "current3", "freeze", "original", "original2", "isDraft", "isDraft5", "createSelector", "createSelectorCreator", "createSelectorCreator2", "lruMemoize", "weakMapMemoize", "weakMapMemoize2", "createDraftSafeSelectorCreator", "createSelector2", "arguments", "createDraftSafeSelector2", "Object", "assign", "selector", "wrappedSelector", "value", "_len", "length", "rest", "Array", "_key", "withTypes", "createDraftSafeSelector", "applyMiddleware", "createStore", "compose", "compose2", "combineReducers", "isPlainObject", "isPlainObject2", "composeWithDevTools", "window", "__REDUX_DEVTOOLS_EXTENSION_COMPOSE__", "apply", "devToolsEnhancer", "__REDUX_DEVTOOLS_EXTENSION__", "noop3", "thunk", "thunkMiddleware", "withExtraArgument", "isAction", "hasMatchFunction", "v", "match", "createAction", "type", "prepareAction", "actionCreator", "prepared", "Error", "process", "env", "NODE_ENV", "formatProdErrorMessage", "_objectSpread", "payload", "meta", "error", "undefined", "toString", "concat", "action", "isActionCreator", "isFSA", "keys", "every", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "getMessage", "splitType", "split", "actionName", "createActionCreatorInvariantMiddleware", "options", "next", "isActionCreator2", "console", "warn", "createNextState", "isDraftable", "getTimeMeasureUtils", "max<PERSON><PERSON><PERSON>", "fnName", "elapsed", "measureTime", "fn", "started", "Date", "now", "finished", "warnIfExceeded", "<PERSON><PERSON>", "_<PERSON><PERSON>", "constructor", "setPrototypeOf", "prototype", "Symbol", "species", "_len2", "arr", "_key2", "prepend", "_len3", "_key3", "isArray", "freezeDraftable", "val", "getOrInsertComputed", "map", "compute", "has", "get", "set", "isImmutableDefault", "isFrozen", "trackForMutations", "isImmutable", "ignorePaths", "obj", "trackedProperties", "trackProperties", "detectMutations", "path", "checkedObjects", "Set", "tracked", "add", "children", "child<PERSON><PERSON>", "ignoredPaths", "trackedProperty", "sameParentRef", "prevObj", "sameRef", "Number", "isNaN", "wasMutated", "keysToDetect", "hasIgnoredPaths", "nested<PERSON>ath", "hasMatches", "some", "ignored", "RegExp", "test", "result", "createImmutableStateInvariantMiddleware", "stringify2", "stringify", "serializer", "indent", "decycler", "JSON", "getSerialize2", "getSerialize", "stack", "_", "slice", "join", "thisPos", "splice", "push", "Infinity", "call", "warnAfter", "track", "bind", "_ref", "getState", "state", "tracker", "measureUtils", "dispatchedAction", "isAction2", "<PERSON><PERSON><PERSON>", "findNonSerializableValue", "isSerializable", "getEntries", "cache", "foundNestedSerializable", "keyP<PERSON>", "entries", "nestedV<PERSON>ue", "isNestedFrozen", "values", "createSerializableStateInvariantMiddleware", "ignoredActions", "ignoredActionPaths", "ignoreState", "ignoreActions", "disableCache", "WeakSet", "storeAPI", "foundActionNonSerializableValue", "foundStateNonSerializableValue", "isBoolean", "x", "buildGetDefaultMiddleware", "getDefaultMiddleware", "immutableCheck", "serializableCheck", "actionCreatorCheck", "middlewareArray", "extraArgument", "immutableOptions", "unshift", "serializableOptions", "actionCreatorOptions", "SHOULD_AUTOBATCH", "prepareAutoBatched", "createQueueWithTimer", "timeout", "notify", "setTimeout", "autoBatchEnhancer", "store", "notifying", "shouldNotifyAtEndOfTick", "notificationQueued", "listeners", "queue<PERSON>allback", "queueMicrotask", "requestAnimationFrame", "queueNotification", "notifyListeners", "for<PERSON>ach", "l", "subscribe", "listener2", "wrappedListener", "unsubscribe", "delete", "dispatch", "_action$meta", "buildGetDefaultEnhancers", "middlewareEnhancer", "getDefaultEnhancers", "autoBatch", "enhancerArray", "configureStore", "reducer", "middleware", "devTools", "duplicateMiddlewareCheck", "preloadedState", "enhancers", "rootReducer", "finalMiddleware", "item", "middlewareReferences", "middleware2", "finalCompose", "trace", "storeEnhancers", "includes", "composedEnhancer", "createNextState2", "isDraft2", "isDraftable2", "executeReducerBuilderCallback", "builderCallback", "actionsMap", "actionMatchers", "defaultCaseReducer", "builder", "addCase", "typeOrActionCreator", "addMatcher", "matcher", "addDefaultCase", "isStateFunction", "createReducer", "initialState", "mapOrBuilderCallback", "finalActionMatchers", "finalDefaultCaseReducer", "getInitialState", "frozenInitialState", "caseReducers", "filter", "_ref2", "_ref3", "reducer2", "cr", "reduce", "previousState", "caseReducer", "draft", "matches", "isAnyOf", "_len4", "matchers", "_key4", "isAllOf", "_len5", "_key5", "hasExpectedRequestMetadata", "validStatus", "hasValidRequestId", "requestId", "hasValidRequestStatus", "requestStatus", "isAsyncThunkArray", "a", "isPending", "_len6", "asyncThunks", "_key6", "asyncThunk", "pending", "isRejected", "_len7", "_key7", "rejected", "isRejectedWithValue", "hasFlag", "rejectedWithValue", "_len8", "_key8", "isFulfilled", "_len9", "_key9", "fulfilled", "isAsyncThunkAction", "_len0", "_key0", "flatMap", "url<PERSON>l<PERSON><PERSON>", "nanoid", "size", "id", "i", "Math", "random", "commonProperties", "RejectWithValue", "_defineProperty", "FulfillWithMeta", "miniSerializeError", "simpleError", "property", "message", "String", "externalAbortMessage", "createAsyncThunk", "createAsyncThunk2", "typePrefix", "payloadCreator", "arg", "serializeError", "aborted", "name", "condition", "signal", "extra", "idGenerator", "abortController", "AbortController", "abor<PERSON><PERSON><PERSON><PERSON>", "abortReason", "abort", "reason", "addEventListener", "once", "promise", "finalAction", "_options$condition", "_options$getPendingMe", "conditionResult", "isThenable", "abortedPromise", "Promise", "reject", "getPendingMeta", "race", "resolve", "rejectWithValue", "fulfillWithValue", "then", "err", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatchConditionRejection", "unwrap", "unwrapResult", "settled", "asyncThunkSymbol", "for", "asyncThunkCreator", "ReducerType", "ReducerType2", "getType", "action<PERSON>ey", "buildCreateSlice", "_creators$asyncThunk", "creators", "cAT", "createSlice2", "reducerPath", "reducers", "buildReducerCreators", "reducerNames", "context", "sliceCaseReducersByName", "sliceCaseReducersByType", "actionCreators", "sliceMatchers", "contextMethods", "exposeAction", "name2", "exposeCaseReducer", "reducerName", "reducerDefinition", "reducerDetails", "createNotation", "isAsyncThunkSliceReducerDefinition", "handleThunkCaseReducerDefinition", "handleNormalReducerDefinition", "buildReducer", "extraReducers", "finalCaseReducers", "sM", "m", "selectSelf", "injectedSelectorCache", "Map", "injectedStateCache", "WeakMap", "_reducer", "makeSelectorProps", "reducerPath2", "injected", "selectSlice", "sliceState", "getSelectors", "selectState", "selectorCache", "_options$selectors", "selectors", "wrapSelector", "actions", "injectInto", "injectable", "_ref4", "pathOpt", "config", "_objectWithoutProperties", "_excluded", "newReducerPath", "inject", "wrapper", "rootState", "_len1", "args", "_key1", "unwrapped", "createSlice", "_reducerDefinitionType", "preparedReducer", "prepare", "_ref5", "maybeReducerWithPrepare", "prepareCallback", "isCaseReducerWithPrepareDefinition", "_ref6", "noop", "getInitialEntityState", "ids", "entities", "createInitialStateFactory", "stateAdapter", "additionalState", "setAll", "createSelectorsFactory", "selectIds", "selectEntities", "selectAll", "selectId", "selectById", "selectTotal", "selectGlobalizedEntities", "createNextState3", "isDraft3", "isDraftTyped", "createSingleArgumentStateOperator", "mutator", "operator", "createStateOperator", "operation", "isPayloadActionArgument", "arg2", "runMutator", "current2", "isDraft4", "selectIdValue", "entity", "ensureEntitiesArray", "get<PERSON>urrent", "splitAddedUpdatedEntities", "newEntities", "existingIdsArray", "existingIds", "added", "addedIds", "updated", "changes", "createUnsortedStateAdapter", "addOneMutably", "addManyMutably", "setOneMutably", "setManyMutably", "setAllMutably", "removeOneMutably", "removeManyMutably", "didMutate", "removeAllMutably", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update", "original3", "new<PERSON>ey", "has<PERSON>ew<PERSON><PERSON>", "updateOneMutably", "updateManyMutably", "updates", "newKeys", "updatesPerEntity", "_updatesPerEntity$upd", "didMutateEntities", "didMutateIds", "e", "upsertOneMutably", "upsertManyMutably", "removeAll", "addOne", "addMany", "setOne", "setMany", "updateOne", "updateMany", "upsertOne", "upsertMany", "removeOne", "remove<PERSON>any", "findInsertIndex", "sortedItems", "comparisonFunction", "lowIndex", "highIndex", "middleIndex", "currentItem", "res", "insert", "insertAtIndex", "createSortedStateAdapter", "comparer", "existingKeys", "models", "model", "mergeFunction", "appliedUpdates", "replacedIds", "newId", "oldIndex", "areArraysEqual", "b", "addedItems", "currentEntities", "currentIds", "stateEntities", "sortedEntities", "was<PERSON>revious<PERSON><PERSON><PERSON><PERSON>", "sort", "newSortedIds", "createEntityAdapter", "sortComparer", "instance", "stateFactory", "selectorsFactory", "isAction3", "task", "listener", "completed", "cancelled", "taskCancelled", "taskCompleted", "listenerCancelled", "listenerCompleted", "TaskAbortError", "code", "assertFunction", "func", "expected", "TypeError", "noop2", "catchRejection", "onError", "catch", "addAbortSignalListener", "abortSignal", "callback", "abortControllerWithReason", "defineProperty", "enumerable", "configurable", "writable", "validateActive", "raceWithSignal", "cleanup", "notifyRejection", "finally", "runTask", "task2", "cleanUp", "status", "createPause", "output", "createDelay", "pause", "timeoutMs", "INTERNAL_NIL_TOKEN", "alm", "createFork", "parentAbortSignal", "parentBlockingPromises", "linkControllers", "controller", "taskExecutor", "opts", "childAbortController", "result2", "delay", "autoJoin", "cancel", "createTakePattern", "startListening", "take", "predicate", "tuplePromise", "stopListening", "effect", "listenerApi", "getOriginalState", "promises", "getListenerEntryPropsFrom", "createListenerEntry", "entry", "findListenerEntry", "listenerMap", "from", "find", "matchPredicateOrType", "cancelActiveListeners", "createClearListenerMiddleware", "clear", "safelyNotifyError", "<PERSON><PERSON><PERSON><PERSON>", "errorToNotify", "errorInfo", "errorHandlerError", "addListener", "clearAllListeners", "removeListener", "defaultErrorHandler", "_len10", "_key10", "createListenerMiddleware", "middlewareOptions", "insertEntry", "cancelOptions", "cancelActive", "_findListenerEntry", "notifyL<PERSON>ener", "api", "internalTaskController", "autoJoinPromises", "Boolean", "fork", "throwIfCancelled", "listenerError", "<PERSON><PERSON><PERSON>", "all", "clearListenerMiddleware", "originalState", "currentState", "listenerEntries", "runListener", "predicateError", "clearListeners", "compose3", "createMiddlewareEntry", "applied", "matchInstance", "instanceId", "_action$meta2", "createDynamicMiddleware", "middlewareMap", "withMiddleware", "_len11", "middlewares", "_key11", "addMiddleware", "addMiddleware2", "_len12", "_key12", "getFinalMiddleware", "appliedMiddleware", "isWithMiddleware", "combineReducers2", "isSliceLike", "maybeSliceLike", "getReducers", "slices", "sliceOrMap", "ORIGINAL_STATE", "isStateProxy", "stateProxyMap", "createStateProxy", "reducerMap", "initialStateCache", "Proxy", "target", "prop", "receiver", "Reflect", "cached", "reducerResult", "emptyObject", "noopReducer", "combineSlices", "_len13", "_key13", "fromEntries", "getReducer", "combinedReducer", "withLazyLoadedSlices", "reducerToInject", "currentReducer", "overrideExisting", "makeSelector", "selectorFn", "selector2", "_len14", "_key14"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/index.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/createDraftSafeSelector.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/configureStore.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/devtoolsExtension.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/getDefaultMiddleware.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/createAction.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/tsHelpers.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/actionCreatorInvariantMiddleware.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/utils.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/immutableStateInvariantMiddleware.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/serializableStateInvariantMiddleware.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/autoBatchEnhancer.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/getDefaultEnhancers.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/createReducer.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/mapBuilders.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/matchers.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/nanoid.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/createAsyncThunk.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/createSlice.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/entities/entity_state.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/entities/state_selectors.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/entities/state_adapter.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/entities/utils.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/entities/unsorted_state_adapter.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/entities/sorted_state_adapter.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/entities/create_adapter.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/listenerMiddleware/index.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/listenerMiddleware/exceptions.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/listenerMiddleware/utils.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/listenerMiddleware/task.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/dynamicMiddleware/index.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/combineSlices.ts", "/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@reduxjs/toolkit/src/formatProdErrorMessage.ts"], "sourcesContent": ["// This must remain here so that the `mangleErrors.cjs` build script\n// does not have to import this into each source file it rewrites.\nimport { formatProdErrorMessage } from './formatProdErrorMessage';\nexport * from 'redux';\nexport { produce as createNextState, current, freeze, original, isDraft } from 'immer';\nexport type { Draft } from 'immer';\nexport { createSelector, createSelectorCreator, lruMemoize, weakMapMemoize } from 'reselect';\nexport type { Selector, OutputSelector } from 'reselect';\nexport { createDraftSafeSelector, createDraftSafeSelectorCreator } from './createDraftSafeSelector';\nexport type { ThunkAction, ThunkDispatch, ThunkMiddleware } from 'redux-thunk';\nexport {\n// js\nconfigureStore } from './configureStore';\nexport type {\n// types\nConfigureStoreOptions, EnhancedStore } from './configureStore';\nexport type { DevToolsEnhancerOptions } from './devtoolsExtension';\nexport {\n// js\ncreateAction, isActionCreator, isFSA as isFluxStandardAction } from './createAction';\nexport type {\n// types\nPayloadAction, PayloadActionCreator, ActionCreatorWithNonInferrablePayload, ActionCreatorWithOptionalPayload, ActionCreatorWithPayload, ActionCreatorWithoutPayload, ActionCreatorWithPreparedPayload, PrepareAction } from './createAction';\nexport {\n// js\ncreateReducer } from './createReducer';\nexport type {\n// types\nActions, CaseReducer, CaseReducers } from './createReducer';\nexport {\n// js\ncreateSlice, buildCreateSlice, asyncThunkCreator, ReducerType } from './createSlice';\nexport type {\n// types\nCreateSliceOptions, Slice, CaseReducerActions, SliceCaseReducers, ValidateSliceCaseReducers, CaseReducerWithPrepare, ReducerCreators, SliceSelectors } from './createSlice';\nexport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware';\nexport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware';\nexport {\n// js\ncreateImmutableStateInvariantMiddleware, isImmutableDefault } from './immutableStateInvariantMiddleware';\nexport type {\n// types\nImmutableStateInvariantMiddlewareOptions } from './immutableStateInvariantMiddleware';\nexport {\n// js\ncreateSerializableStateInvariantMiddleware, findNonSerializableValue, isPlain } from './serializableStateInvariantMiddleware';\nexport type {\n// types\nSerializableStateInvariantMiddlewareOptions } from './serializableStateInvariantMiddleware';\nexport type {\n// types\nActionReducerMapBuilder } from './mapBuilders';\nexport { Tuple } from './utils';\nexport { createEntityAdapter } from './entities/create_adapter';\nexport type { EntityState, EntityAdapter, EntitySelectors, EntityStateAdapter, EntityId, Update, IdSelector, Comparer } from './entities/models';\nexport { createAsyncThunk, unwrapResult, miniSerializeError } from './createAsyncThunk';\nexport type { AsyncThunk, AsyncThunkOptions, AsyncThunkAction, AsyncThunkPayloadCreatorReturnValue, AsyncThunkPayloadCreator, GetState, GetThunkAPI, SerializedError, CreateAsyncThunkFunction } from './createAsyncThunk';\nexport {\n// js\nisAllOf, isAnyOf, isPending, isRejected, isFulfilled, isAsyncThunkAction, isRejectedWithValue } from './matchers';\nexport type {\n// types\nActionMatchingAllOf, ActionMatchingAnyOf } from './matchers';\nexport { nanoid } from './nanoid';\nexport type { ListenerEffect, ListenerMiddleware, ListenerEffectAPI, ListenerMiddlewareInstance, CreateListenerMiddlewareOptions, ListenerErrorHandler, TypedStartListening, TypedAddListener, TypedStopListening, TypedRemoveListener, UnsubscribeListener, UnsubscribeListenerOptions, ForkedTaskExecutor, ForkedTask, ForkedTaskAPI, AsyncTaskExecutor, SyncTaskExecutor, TaskCancelled, TaskRejected, TaskResolved, TaskResult } from './listenerMiddleware/index';\nexport type { AnyListenerPredicate } from './listenerMiddleware/types';\nexport { createListenerMiddleware, addListener, removeListener, clearAllListeners, TaskAbortError } from './listenerMiddleware/index';\nexport type { AddMiddleware, DynamicDispatch, DynamicMiddlewareInstance, GetDispatchType as GetDispatch, MiddlewareApiConfig } from './dynamicMiddleware/types';\nexport { createDynamicMiddleware } from './dynamicMiddleware/index';\nexport { SHOULD_AUTOBATCH, prepareAutoBatched, autoBatchEnhancer } from './autoBatchEnhancer';\nexport type { AutoBatchOptions } from './autoBatchEnhancer';\nexport { combineSlices } from './combineSlices';\nexport type { CombinedSliceReducer, WithSlice } from './combineSlices';\nexport type { ExtractDispatchExtensions as TSHelpersExtractDispatchExtensions, SafePromise } from './tsHelpers';\nexport { formatProdErrorMessage } from './formatProdErrorMessage';", "import { current, isDraft } from 'immer';\nimport { createSelectorCreator, weakMapMemoize } from 'reselect';\nexport const createDraftSafeSelectorCreator: typeof createSelectorCreator = (...args: unknown[]) => {\n  const createSelector = (createSelectorCreator as any)(...args);\n  const createDraftSafeSelector = Object.assign((...args: unknown[]) => {\n    const selector = createSelector(...args);\n    const wrappedSelector = (value: unknown, ...rest: unknown[]) => selector(isDraft(value) ? current(value) : value, ...rest);\n    Object.assign(wrappedSelector, selector);\n    return wrappedSelector as any;\n  }, {\n    withTypes: () => createDraftSafeSelector\n  });\n  return createDraftSafeSelector;\n};\n\n/**\n * \"Draft-Safe\" version of `reselect`'s `createSelector`:\n * If an `immer`-drafted object is passed into the resulting selector's first argument,\n * the selector will act on the current draft value, instead of returning a cached value\n * that might be possibly outdated if the draft has been modified since.\n * @public\n */\nexport const createDraftSafeSelector = /* @__PURE__ */\ncreateDraftSafeSelectorCreator(weakMapMemoize);", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3, formatProdErrorMessage as _formatProdErrorMessage4, formatProdErrorMessage as _formatProdErrorMessage5, formatProdErrorMessage as _formatProdErrorMessage6, formatProdErrorMessage as _formatProdErrorMessage7, formatProdErrorMessage as _formatProdErrorMessage8 } from \"@reduxjs/toolkit\";\nimport type { Reducer, ReducersMapObject, Middleware, Action, StoreEnhancer, Store, UnknownAction } from 'redux';\nimport { applyMiddleware, createStore, compose, combineReducers, isPlainObject } from 'redux';\nimport type { DevToolsEnhancerOptions as DevToolsOptions } from './devtoolsExtension';\nimport { composeWithDevTools } from './devtoolsExtension';\nimport type { ThunkMiddlewareFor, GetDefaultMiddleware } from './getDefaultMiddleware';\nimport { buildGetDefaultMiddleware } from './getDefaultMiddleware';\nimport type { ExtractDispatchExtensions, ExtractStoreExtensions, ExtractStateExtensions, UnknownIfNonSpecific } from './tsHelpers';\nimport type { Tuple } from './utils';\nimport type { GetDefaultEnhancers } from './getDefaultEnhancers';\nimport { buildGetDefaultEnhancers } from './getDefaultEnhancers';\n\n/**\n * Options for `configureStore()`.\n *\n * @public\n */\nexport interface ConfigureStoreOptions<S = any, A extends Action = UnknownAction, M extends Tuple<Middlewares<S>> = Tuple<Middlewares<S>>, E extends Tuple<Enhancers> = Tuple<Enhancers>, P = S> {\n  /**\n   * A single reducer function that will be used as the root reducer, or an\n   * object of slice reducers that will be passed to `combineReducers()`.\n   */\n  reducer: Reducer<S, A, P> | ReducersMapObject<S, A, P>;\n\n  /**\n   * An array of Redux middleware to install, or a callback receiving `getDefaultMiddleware` and returning a Tuple of middleware.\n   * If not supplied, defaults to the set of middleware returned by `getDefaultMiddleware()`.\n   *\n   * @example `middleware: (gDM) => gDM().concat(logger, apiMiddleware, yourCustomMiddleware)`\n   * @see https://redux-toolkit.js.org/api/getDefaultMiddleware#intended-usage\n   */\n  middleware?: (getDefaultMiddleware: GetDefaultMiddleware<S>) => M;\n\n  /**\n   * Whether to enable Redux DevTools integration. Defaults to `true`.\n   *\n   * Additional configuration can be done by passing Redux DevTools options\n   */\n  devTools?: boolean | DevToolsOptions;\n\n  /**\n   * Whether to check for duplicate middleware instances. Defaults to `true`.\n   */\n  duplicateMiddlewareCheck?: boolean;\n\n  /**\n   * The initial state, same as Redux's createStore.\n   * You may optionally specify it to hydrate the state\n   * from the server in universal apps, or to restore a previously serialized\n   * user session. If you use `combineReducers()` to produce the root reducer\n   * function (either directly or indirectly by passing an object as `reducer`),\n   * this must be an object with the same shape as the reducer map keys.\n   */\n  // we infer here, and instead complain if the reducer doesn't match\n  preloadedState?: P;\n\n  /**\n   * The store enhancers to apply. See Redux's `createStore()`.\n   * All enhancers will be included before the DevTools Extension enhancer.\n   * If you need to customize the order of enhancers, supply a callback\n   * function that will receive a `getDefaultEnhancers` function that returns a Tuple,\n   * and should return a Tuple of enhancers (such as `getDefaultEnhancers().concat(offline)`).\n   * If you only need to add middleware, you can use the `middleware` parameter instead.\n   */\n  enhancers?: (getDefaultEnhancers: GetDefaultEnhancers<M>) => E;\n}\nexport type Middlewares<S> = ReadonlyArray<Middleware<{}, S>>;\ntype Enhancers = ReadonlyArray<StoreEnhancer>;\n\n/**\n * A Redux store returned by `configureStore()`. Supports dispatching\n * side-effectful _thunks_ in addition to plain actions.\n *\n * @public\n */\nexport type EnhancedStore<S = any, A extends Action = UnknownAction, E extends Enhancers = Enhancers> = ExtractStoreExtensions<E> & Store<S, A, UnknownIfNonSpecific<ExtractStateExtensions<E>>>;\n\n/**\n * A friendly abstraction over the standard Redux `createStore()` function.\n *\n * @param options The store configuration.\n * @returns A configured Redux store.\n *\n * @public\n */\nexport function configureStore<S = any, A extends Action = UnknownAction, M extends Tuple<Middlewares<S>> = Tuple<[ThunkMiddlewareFor<S>]>, E extends Tuple<Enhancers> = Tuple<[StoreEnhancer<{\n  dispatch: ExtractDispatchExtensions<M>;\n}>, StoreEnhancer]>, P = S>(options: ConfigureStoreOptions<S, A, M, E, P>): EnhancedStore<S, A, E> {\n  const getDefaultMiddleware = buildGetDefaultMiddleware<S>();\n  const {\n    reducer = undefined,\n    middleware,\n    devTools = true,\n    duplicateMiddlewareCheck = true,\n    preloadedState = undefined,\n    enhancers = undefined\n  } = options || {};\n  let rootReducer: Reducer<S, A, P>;\n  if (typeof reducer === 'function') {\n    rootReducer = reducer;\n  } else if (isPlainObject(reducer)) {\n    rootReducer = combineReducers(reducer) as unknown as Reducer<S, A, P>;\n  } else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(1) : '`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers');\n  }\n  if (process.env.NODE_ENV !== 'production' && middleware && typeof middleware !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(2) : '`middleware` field must be a callback');\n  }\n  let finalMiddleware: Tuple<Middlewares<S>>;\n  if (typeof middleware === 'function') {\n    finalMiddleware = middleware(getDefaultMiddleware);\n    if (process.env.NODE_ENV !== 'production' && !Array.isArray(finalMiddleware)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(3) : 'when using a middleware builder function, an array of middleware must be returned');\n    }\n  } else {\n    finalMiddleware = getDefaultMiddleware();\n  }\n  if (process.env.NODE_ENV !== 'production' && finalMiddleware.some((item: any) => typeof item !== 'function')) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(4) : 'each middleware provided to configureStore must be a function');\n  }\n  if (process.env.NODE_ENV !== 'production' && duplicateMiddlewareCheck) {\n    let middlewareReferences = new Set<Middleware<any, S>>();\n    finalMiddleware.forEach(middleware => {\n      if (middlewareReferences.has(middleware)) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(42) : 'Duplicate middleware references found when creating the store. Ensure that each middleware is only included once.');\n      }\n      middlewareReferences.add(middleware);\n    });\n  }\n  let finalCompose = compose;\n  if (devTools) {\n    finalCompose = composeWithDevTools({\n      // Enable capture of stack traces for dispatched Redux actions\n      trace: process.env.NODE_ENV !== 'production',\n      ...(typeof devTools === 'object' && devTools)\n    });\n  }\n  const middlewareEnhancer = applyMiddleware(...finalMiddleware);\n  const getDefaultEnhancers = buildGetDefaultEnhancers<M>(middlewareEnhancer);\n  if (process.env.NODE_ENV !== 'production' && enhancers && typeof enhancers !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(5) : '`enhancers` field must be a callback');\n  }\n  let storeEnhancers = typeof enhancers === 'function' ? enhancers(getDefaultEnhancers) : getDefaultEnhancers();\n  if (process.env.NODE_ENV !== 'production' && !Array.isArray(storeEnhancers)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage7(6) : '`enhancers` callback must return an array');\n  }\n  if (process.env.NODE_ENV !== 'production' && storeEnhancers.some((item: any) => typeof item !== 'function')) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage8(7) : 'each enhancer provided to configureStore must be a function');\n  }\n  if (process.env.NODE_ENV !== 'production' && finalMiddleware.length && !storeEnhancers.includes(middlewareEnhancer)) {\n    console.error('middlewares were provided, but middleware enhancer was not included in final enhancers - make sure to call `getDefaultEnhancers`');\n  }\n  const composedEnhancer: StoreEnhancer<any> = finalCompose(...storeEnhancers);\n  return createStore(rootReducer, preloadedState as P, composedEnhancer);\n}", "import type { Action, ActionCreator, StoreEnhancer } from 'redux';\nimport { compose } from 'redux';\n\n/**\r\n * @public\r\n */\nexport interface DevToolsEnhancerOptions {\n  /**\r\n   * the instance name to be showed on the monitor page. Default value is `document.title`.\r\n   * If not specified and there's no document title, it will consist of `tabId` and `instanceId`.\r\n   */\n  name?: string;\n  /**\r\n   * action creators functions to be available in the Dispatcher.\r\n   */\n  actionCreators?: ActionCreator<any>[] | {\n    [key: string]: ActionCreator<any>;\n  };\n  /**\r\n   * if more than one action is dispatched in the indicated interval, all new actions will be collected and sent at once.\r\n   * It is the joint between performance and speed. When set to `0`, all actions will be sent instantly.\r\n   * Set it to a higher value when experiencing perf issues (also `maxAge` to a lower value).\r\n   *\r\n   * @default 500 ms.\r\n   */\n  latency?: number;\n  /**\r\n   * (> 1) - maximum allowed actions to be stored in the history tree. The oldest actions are removed once maxAge is reached. It's critical for performance.\r\n   *\r\n   * @default 50\r\n   */\n  maxAge?: number;\n  /**\r\n   * Customizes how actions and state are serialized and deserialized. Can be a boolean or object. If given a boolean, the behavior is the same as if you\r\n   * were to pass an object and specify `options` as a boolean. Giving an object allows fine-grained customization using the `replacer` and `reviver`\r\n   * functions.\r\n   */\n  serialize?: boolean | {\n    /**\r\n     * - `undefined` - will use regular `JSON.stringify` to send data (it's the fast mode).\r\n     * - `false` - will handle also circular references.\r\n     * - `true` - will handle also date, regex, undefined, error objects, symbols, maps, sets and functions.\r\n     * - object, which contains `date`, `regex`, `undefined`, `error`, `symbol`, `map`, `set` and `function` keys.\r\n     *   For each of them you can indicate if to include (by setting as `true`).\r\n     *   For `function` key you can also specify a custom function which handles serialization.\r\n     *   See [`jsan`](https://github.com/kolodny/jsan) for more details.\r\n     */\n    options?: undefined | boolean | {\n      date?: true;\n      regex?: true;\n      undefined?: true;\n      error?: true;\n      symbol?: true;\n      map?: true;\n      set?: true;\n      function?: true | ((fn: (...args: any[]) => any) => string);\n    };\n    /**\r\n     * [JSON replacer function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#The_replacer_parameter) used for both actions and states stringify.\r\n     * In addition, you can specify a data type by adding a [`__serializedType__`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/helpers/index.js#L4)\r\n     * key. So you can deserialize it back while importing or persisting data.\r\n     * Moreover, it will also [show a nice preview showing the provided custom type](https://cloud.githubusercontent.com/assets/7957859/21814330/a17d556a-d761-11e6-85ef-159dd12f36c5.png):\r\n     */\n    replacer?: (key: string, value: unknown) => any;\n    /**\r\n     * [JSON `reviver` function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse#Using_the_reviver_parameter)\r\n     * used for parsing the imported actions and states. See [`remotedev-serialize`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/immutable/serialize.js#L8-L41)\r\n     * as an example on how to serialize special data types and get them back.\r\n     */\n    reviver?: (key: string, value: unknown) => any;\n    /**\r\n     * Automatically serialize/deserialize immutablejs via [remotedev-serialize](https://github.com/zalmoxisus/remotedev-serialize).\r\n     * Just pass the Immutable library. It will support all ImmutableJS structures. You can even export them into a file and get them back.\r\n     * The only exception is `Record` class, for which you should pass this in addition the references to your classes in `refs`.\r\n     */\n    immutable?: any;\n    /**\r\n     * ImmutableJS `Record` classes used to make possible restore its instances back when importing, persisting...\r\n     */\n    refs?: any;\n  };\n  /**\r\n   * function which takes `action` object and id number as arguments, and should return `action` object back.\r\n   */\n  actionSanitizer?: <A extends Action>(action: A, id: number) => A;\n  /**\r\n   * function which takes `state` object and index as arguments, and should return `state` object back.\r\n   */\n  stateSanitizer?: <S>(state: S, index: number) => S;\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\n  actionsDenylist?: string | string[];\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\n  actionsAllowlist?: string | string[];\n  /**\r\n   * called for every action before sending, takes `state` and `action` object, and returns `true` in case it allows sending the current data to the monitor.\r\n   * Use it as a more advanced version of `actionsDenylist`/`actionsAllowlist` parameters.\r\n   */\n  predicate?: <S, A extends Action>(state: S, action: A) => boolean;\n  /**\r\n   * if specified as `false`, it will not record the changes till clicking on `Start recording` button.\r\n   * Available only for Redux enhancer, for others use `autoPause`.\r\n   *\r\n   * @default true\r\n   */\n  shouldRecordChanges?: boolean;\n  /**\r\n   * if specified, whenever clicking on `Pause recording` button and there are actions in the history log, will add this action type.\r\n   * If not specified, will commit when paused. Available only for Redux enhancer.\r\n   *\r\n   * @default \"@@PAUSED\"\"\r\n   */\n  pauseActionType?: string;\n  /**\r\n   * auto pauses when the extension’s window is not opened, and so has zero impact on your app when not in use.\r\n   * Not available for Redux enhancer (as it already does it but storing the data to be sent).\r\n   *\r\n   * @default false\r\n   */\n  autoPause?: boolean;\n  /**\r\n   * if specified as `true`, it will not allow any non-monitor actions to be dispatched till clicking on `Unlock changes` button.\r\n   * Available only for Redux enhancer.\r\n   *\r\n   * @default false\r\n   */\n  shouldStartLocked?: boolean;\n  /**\r\n   * if set to `false`, will not recompute the states on hot reloading (or on replacing the reducers). Available only for Redux enhancer.\r\n   *\r\n   * @default true\r\n   */\n  shouldHotReload?: boolean;\n  /**\r\n   * if specified as `true`, whenever there's an exception in reducers, the monitors will show the error message, and next actions will not be dispatched.\r\n   *\r\n   * @default false\r\n   */\n  shouldCatchErrors?: boolean;\n  /**\r\n   * If you want to restrict the extension, specify the features you allow.\r\n   * If not specified, all of the features are enabled. When set as an object, only those included as `true` will be allowed.\r\n   * Note that except `true`/`false`, `import` and `export` can be set as `custom` (which is by default for Redux enhancer), meaning that the importing/exporting occurs on the client side.\r\n   * Otherwise, you'll get/set the data right from the monitor part.\r\n   */\n  features?: {\n    /**\r\n     * start/pause recording of dispatched actions\r\n     */\n    pause?: boolean;\n    /**\r\n     * lock/unlock dispatching actions and side effects\r\n     */\n    lock?: boolean;\n    /**\r\n     * persist states on page reloading\r\n     */\n    persist?: boolean;\n    /**\r\n     * export history of actions in a file\r\n     */\n    export?: boolean | 'custom';\n    /**\r\n     * import history of actions from a file\r\n     */\n    import?: boolean | 'custom';\n    /**\r\n     * jump back and forth (time travelling)\r\n     */\n    jump?: boolean;\n    /**\r\n     * skip (cancel) actions\r\n     */\n    skip?: boolean;\n    /**\r\n     * drag and drop actions in the history list\r\n     */\n    reorder?: boolean;\n    /**\r\n     * dispatch custom actions or action creators\r\n     */\n    dispatch?: boolean;\n    /**\r\n     * generate tests for the selected actions\r\n     */\n    test?: boolean;\n  };\n  /**\r\n   * Set to true or a stacktrace-returning function to record call stack traces for dispatched actions.\r\n   * Defaults to false.\r\n   */\n  trace?: boolean | (<A extends Action>(action: A) => string);\n  /**\r\n   * The maximum number of stack trace entries to record per action. Defaults to 10.\r\n   */\n  traceLimit?: number;\n}\ntype Compose = typeof compose;\ninterface ComposeWithDevTools {\n  (options: DevToolsEnhancerOptions): Compose;\n  <StoreExt extends {}>(...funcs: StoreEnhancer<StoreExt>[]): StoreEnhancer<StoreExt>;\n}\n\n/**\r\n * @public\r\n */\nexport const composeWithDevTools: ComposeWithDevTools = typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function () {\n  if (arguments.length === 0) return undefined;\n  if (typeof arguments[0] === 'object') return compose;\n  return compose.apply(null, arguments as any as Function[]);\n};\n\n/**\r\n * @public\r\n */\nexport const devToolsEnhancer: {\n  (options: DevToolsEnhancerOptions): StoreEnhancer<any>;\n} = typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION__ ? (window as any).__REDUX_DEVTOOLS_EXTENSION__ : function () {\n  return function (noop) {\n    return noop;\n  };\n};", "import type { Middleware, UnknownAction } from 'redux';\nimport type { ThunkMiddleware } from 'redux-thunk';\nimport { thunk as thunkMiddleware, withExtraArgument } from 'redux-thunk';\nimport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware';\nimport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware';\nimport type { ImmutableStateInvariantMiddlewareOptions } from './immutableStateInvariantMiddleware';\n/* PROD_START_REMOVE_UMD */\nimport { createImmutableStateInvariantMiddleware } from './immutableStateInvariantMiddleware';\n/* PROD_STOP_REMOVE_UMD */\n\nimport type { SerializableStateInvariantMiddlewareOptions } from './serializableStateInvariantMiddleware';\nimport { createSerializableStateInvariantMiddleware } from './serializableStateInvariantMiddleware';\nimport type { ExcludeFromTuple } from './tsHelpers';\nimport { Tuple } from './utils';\nfunction isBoolean(x: any): x is boolean {\n  return typeof x === 'boolean';\n}\ninterface ThunkOptions<E = any> {\n  extraArgument: E;\n}\ninterface GetDefaultMiddlewareOptions {\n  thunk?: boolean | ThunkOptions;\n  immutableCheck?: boolean | ImmutableStateInvariantMiddlewareOptions;\n  serializableCheck?: boolean | SerializableStateInvariantMiddlewareOptions;\n  actionCreatorCheck?: boolean | ActionCreatorInvariantMiddlewareOptions;\n}\nexport type ThunkMiddlewareFor<S, O extends GetDefaultMiddlewareOptions = {}> = O extends {\n  thunk: false;\n} ? never : O extends {\n  thunk: {\n    extraArgument: infer E;\n  };\n} ? ThunkMiddleware<S, UnknownAction, E> : ThunkMiddleware<S, UnknownAction>;\nexport type GetDefaultMiddleware<S = any> = <O extends GetDefaultMiddlewareOptions = {\n  thunk: true;\n  immutableCheck: true;\n  serializableCheck: true;\n  actionCreatorCheck: true;\n}>(options?: O) => Tuple<ExcludeFromTuple<[ThunkMiddlewareFor<S, O>], never>>;\nexport const buildGetDefaultMiddleware = <S = any,>(): GetDefaultMiddleware<S> => function getDefaultMiddleware(options) {\n  const {\n    thunk = true,\n    immutableCheck = true,\n    serializableCheck = true,\n    actionCreatorCheck = true\n  } = options ?? {};\n  let middlewareArray = new Tuple<Middleware[]>();\n  if (thunk) {\n    if (isBoolean(thunk)) {\n      middlewareArray.push(thunkMiddleware);\n    } else {\n      middlewareArray.push(withExtraArgument(thunk.extraArgument));\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (immutableCheck) {\n      /* PROD_START_REMOVE_UMD */\n      let immutableOptions: ImmutableStateInvariantMiddlewareOptions = {};\n      if (!isBoolean(immutableCheck)) {\n        immutableOptions = immutableCheck;\n      }\n      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\n      /* PROD_STOP_REMOVE_UMD */\n    }\n    if (serializableCheck) {\n      let serializableOptions: SerializableStateInvariantMiddlewareOptions = {};\n      if (!isBoolean(serializableCheck)) {\n        serializableOptions = serializableCheck;\n      }\n      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\n    }\n    if (actionCreatorCheck) {\n      let actionCreatorOptions: ActionCreatorInvariantMiddlewareOptions = {};\n      if (!isBoolean(actionCreatorCheck)) {\n        actionCreatorOptions = actionCreatorCheck;\n      }\n      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\n    }\n  }\n  return middlewareArray as any;\n};", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport { isAction } from 'redux';\nimport type { IsUnknownOrNonInferrable, IfMaybeUndefined, IfVoid, IsAny } from './tsHelpers';\nimport { hasMatchFunction } from './tsHelpers';\n\n/**\n * An action with a string type and an associated payload. This is the\n * type of action returned by `createAction()` action creators.\n *\n * @template P The type of the action's payload.\n * @template T the type used for the action type.\n * @template M The type of the action's meta (optional)\n * @template E The type of the action's error (optional)\n *\n * @public\n */\nexport type PayloadAction<P = void, T extends string = string, M = never, E = never> = {\n  payload: P;\n  type: T;\n} & ([M] extends [never] ? {} : {\n  meta: M;\n}) & ([E] extends [never] ? {} : {\n  error: E;\n});\n\n/**\n * A \"prepare\" method to be used as the second parameter of `createAction`.\n * Takes any number of arguments and returns a Flux Standard Action without\n * type (will be added later) that *must* contain a payload (might be undefined).\n *\n * @public\n */\nexport type PrepareAction<P> = ((...args: any[]) => {\n  payload: P;\n}) | ((...args: any[]) => {\n  payload: P;\n  meta: any;\n}) | ((...args: any[]) => {\n  payload: P;\n  error: any;\n}) | ((...args: any[]) => {\n  payload: P;\n  meta: any;\n  error: any;\n});\n\n/**\n * Internal version of `ActionCreatorWithPreparedPayload`. Not to be used externally.\n *\n * @internal\n */\nexport type _ActionCreatorWithPreparedPayload<PA extends PrepareAction<any> | void, T extends string = string> = PA extends PrepareAction<infer P> ? ActionCreatorWithPreparedPayload<Parameters<PA>, P, T, ReturnType<PA> extends {\n  error: infer E;\n} ? E : never, ReturnType<PA> extends {\n  meta: infer M;\n} ? M : never> : void;\n\n/**\n * Basic type for all action creators.\n *\n * @inheritdoc {redux#ActionCreator}\n */\nexport type BaseActionCreator<P, T extends string, M = never, E = never> = {\n  type: T;\n  match: (action: unknown) => action is PayloadAction<P, T, M, E>;\n};\n\n/**\n * An action creator that takes multiple arguments that are passed\n * to a `PrepareAction` method to create the final Action.\n * @typeParam Args arguments for the action creator function\n * @typeParam P `payload` type\n * @typeParam T `type` name\n * @typeParam E optional `error` type\n * @typeParam M optional `meta` type\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithPreparedPayload<Args extends unknown[], P, T extends string = string, E = never, M = never> extends BaseActionCreator<P, T, M, E> {\n  /**\n   * Calling this {@link redux#ActionCreator} with `Args` will return\n   * an Action with a payload of type `P` and (depending on the `PrepareAction`\n   * method used) a `meta`- and `error` property of types `M` and `E` respectively.\n   */\n  (...args: Args): PayloadAction<P, T, M, E>;\n}\n\n/**\n * An action creator of type `T` that takes an optional payload of type `P`.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithOptionalPayload<P, T extends string = string> extends BaseActionCreator<P, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} with an argument will\n   * return a {@link PayloadAction} of type `T` with a payload of `P`.\n   * Calling it without an argument will return a PayloadAction with a payload of `undefined`.\n   */\n  (payload?: P): PayloadAction<P, T>;\n}\n\n/**\n * An action creator of type `T` that takes no payload.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithoutPayload<T extends string = string> extends BaseActionCreator<undefined, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} will\n   * return a {@link PayloadAction} of type `T` with a payload of `undefined`\n   */\n  (noArgument: void): PayloadAction<undefined, T>;\n}\n\n/**\n * An action creator of type `T` that requires a payload of type P.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithPayload<P, T extends string = string> extends BaseActionCreator<P, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} with an argument will\n   * return a {@link PayloadAction} of type `T` with a payload of `P`\n   */\n  (payload: P): PayloadAction<P, T>;\n}\n\n/**\n * An action creator of type `T` whose `payload` type could not be inferred. Accepts everything as `payload`.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithNonInferrablePayload<T extends string = string> extends BaseActionCreator<unknown, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} with an argument will\n   * return a {@link PayloadAction} of type `T` with a payload\n   * of exactly the type of the argument.\n   */\n  <PT extends unknown>(payload: PT): PayloadAction<PT, T>;\n}\n\n/**\n * An action creator that produces actions with a `payload` attribute.\n *\n * @typeParam P the `payload` type\n * @typeParam T the `type` of the resulting action\n * @typeParam PA if the resulting action is preprocessed by a `prepare` method, the signature of said method.\n *\n * @public\n */\nexport type PayloadActionCreator<P = void, T extends string = string, PA extends PrepareAction<P> | void = void> = IfPrepareActionMethodProvided<PA, _ActionCreatorWithPreparedPayload<PA, T>,\n// else\nIsAny<P, ActionCreatorWithPayload<any, T>, IsUnknownOrNonInferrable<P, ActionCreatorWithNonInferrablePayload<T>,\n// else\nIfVoid<P, ActionCreatorWithoutPayload<T>,\n// else\nIfMaybeUndefined<P, ActionCreatorWithOptionalPayload<P, T>,\n// else\nActionCreatorWithPayload<P, T>>>>>>;\n\n/**\n * A utility function to create an action creator for the given action type\n * string. The action creator accepts a single argument, which will be included\n * in the action object as a field called payload. The action creator function\n * will also have its toString() overridden so that it returns the action type.\n *\n * @param type The action type to use for created actions.\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\n *\n * @public\n */\nexport function createAction<P = void, T extends string = string>(type: T): PayloadActionCreator<P, T>;\n\n/**\n * A utility function to create an action creator for the given action type\n * string. The action creator accepts a single argument, which will be included\n * in the action object as a field called payload. The action creator function\n * will also have its toString() overridden so that it returns the action type.\n *\n * @param type The action type to use for created actions.\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\n *\n * @public\n */\nexport function createAction<PA extends PrepareAction<any>, T extends string = string>(type: T, prepareAction: PA): PayloadActionCreator<ReturnType<PA>['payload'], T, PA>;\nexport function createAction(type: string, prepareAction?: Function): any {\n  function actionCreator(...args: any[]) {\n    if (prepareAction) {\n      let prepared = prepareAction(...args);\n      if (!prepared) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(0) : 'prepareAction did not return an object');\n      }\n      return {\n        type,\n        payload: prepared.payload,\n        ...('meta' in prepared && {\n          meta: prepared.meta\n        }),\n        ...('error' in prepared && {\n          error: prepared.error\n        })\n      };\n    }\n    return {\n      type,\n      payload: args[0]\n    };\n  }\n  actionCreator.toString = () => `${type}`;\n  actionCreator.type = type;\n  actionCreator.match = (action: unknown): action is PayloadAction => isAction(action) && action.type === type;\n  return actionCreator;\n}\n\n/**\n * Returns true if value is an RTK-like action creator, with a static type property and match method.\n */\nexport function isActionCreator(action: unknown): action is BaseActionCreator<unknown, string> & Function {\n  return typeof action === 'function' && 'type' in action &&\n  // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\n  hasMatchFunction(action as any);\n}\n\n/**\n * Returns true if value is an action with a string type and valid Flux Standard Action keys.\n */\nexport function isFSA(action: unknown): action is {\n  type: string;\n  payload?: unknown;\n  error?: unknown;\n  meta?: unknown;\n} {\n  return isAction(action) && Object.keys(action).every(isValidKey);\n}\nfunction isValidKey(key: string) {\n  return ['type', 'payload', 'error', 'meta'].indexOf(key) > -1;\n}\n\n// helper types for more readable typings\n\ntype IfPrepareActionMethodProvided<PA extends PrepareAction<any> | void, True, False> = PA extends ((...args: any[]) => any) ? True : False;", "import type { Middleware, StoreEnhancer } from 'redux';\nimport type { Tuple } from './utils';\nexport function safeAssign<T extends object>(target: T, ...args: Array<Partial<NoInfer<T>>>) {\n  Object.assign(target, ...args);\n}\n\n/**\n * return True if T is `any`, otherwise return False\n * taken from https://github.com/joonhocho/tsdef\n *\n * @internal\n */\nexport type IsAny<T, True, False = never> =\n// test if we are going the left AND right path in the condition\ntrue | false extends (T extends never ? true : false) ? True : False;\nexport type CastAny<T, CastTo> = IsAny<T, CastTo, T>;\n\n/**\n * return True if T is `unknown`, otherwise return False\n * taken from https://github.com/joonhocho/tsdef\n *\n * @internal\n */\nexport type IsUnknown<T, True, False = never> = unknown extends T ? IsAny<T, False, True> : False;\nexport type FallbackIfUnknown<T, Fallback> = IsUnknown<T, Fallback, T>;\n\n/**\n * @internal\n */\nexport type IfMaybeUndefined<P, True, False> = [undefined] extends [P] ? True : False;\n\n/**\n * @internal\n */\nexport type IfVoid<P, True, False> = [void] extends [P] ? True : False;\n\n/**\n * @internal\n */\nexport type IsEmptyObj<T, True, False = never> = T extends any ? keyof T extends never ? IsUnknown<T, False, IfMaybeUndefined<T, False, IfVoid<T, False, True>>> : False : never;\n\n/**\n * returns True if TS version is above 3.5, False if below.\n * uses feature detection to detect TS version >= 3.5\n * * versions below 3.5 will return `{}` for unresolvable interference\n * * versions above will return `unknown`\n *\n * @internal\n */\nexport type AtLeastTS35<True, False> = [True, False][IsUnknown<ReturnType<<T>() => T>, 0, 1>];\n\n/**\n * @internal\n */\nexport type IsUnknownOrNonInferrable<T, True, False> = AtLeastTS35<IsUnknown<T, True, False>, IsEmptyObj<T, True, IsUnknown<T, True, False>>>;\n\n/**\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\n */\nexport type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never;\n\n// Appears to have a convenient side effect of ignoring `never` even if that's not what you specified\nexport type ExcludeFromTuple<T, E, Acc extends unknown[] = []> = T extends [infer Head, ...infer Tail] ? ExcludeFromTuple<Tail, E, [...Acc, ...([Head] extends [E] ? [] : [Head])]> : Acc;\ntype ExtractDispatchFromMiddlewareTuple<MiddlewareTuple extends readonly any[], Acc extends {}> = MiddlewareTuple extends [infer Head, ...infer Tail] ? ExtractDispatchFromMiddlewareTuple<Tail, Acc & (Head extends Middleware<infer D> ? IsAny<D, {}, D> : {})> : Acc;\nexport type ExtractDispatchExtensions<M> = M extends Tuple<infer MiddlewareTuple> ? ExtractDispatchFromMiddlewareTuple<MiddlewareTuple, {}> : M extends ReadonlyArray<Middleware> ? ExtractDispatchFromMiddlewareTuple<[...M], {}> : never;\ntype ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple extends readonly any[], Acc extends {}> = EnhancerTuple extends [infer Head, ...infer Tail] ? ExtractStoreExtensionsFromEnhancerTuple<Tail, Acc & (Head extends StoreEnhancer<infer Ext> ? IsAny<Ext, {}, Ext> : {})> : Acc;\nexport type ExtractStoreExtensions<E> = E extends Tuple<infer EnhancerTuple> ? ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple, {}> : E extends ReadonlyArray<StoreEnhancer> ? UnionToIntersection<E[number] extends StoreEnhancer<infer Ext> ? Ext extends {} ? IsAny<Ext, {}, Ext> : {} : {}> : never;\ntype ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple extends readonly any[], Acc extends {}> = EnhancerTuple extends [infer Head, ...infer Tail] ? ExtractStateExtensionsFromEnhancerTuple<Tail, Acc & (Head extends StoreEnhancer<any, infer StateExt> ? IsAny<StateExt, {}, StateExt> : {})> : Acc;\nexport type ExtractStateExtensions<E> = E extends Tuple<infer EnhancerTuple> ? ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple, {}> : E extends ReadonlyArray<StoreEnhancer> ? UnionToIntersection<E[number] extends StoreEnhancer<any, infer StateExt> ? StateExt extends {} ? IsAny<StateExt, {}, StateExt> : {} : {}> : never;\n\n/**\n * Helper type. Passes T out again, but boxes it in a way that it cannot\n * \"widen\" the type by accident if it is a generic that should be inferred\n * from elsewhere.\n *\n * @internal\n */\nexport type NoInfer<T> = [T][T extends any ? 0 : never];\nexport type NonUndefined<T> = T extends undefined ? never : T;\nexport type WithRequiredProp<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;\nexport type WithOptionalProp<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\nexport interface TypeGuard<T> {\n  (value: any): value is T;\n}\nexport interface HasMatchFunction<T> {\n  match: TypeGuard<T>;\n}\nexport const hasMatchFunction = <T,>(v: Matcher<T>): v is HasMatchFunction<T> => {\n  return v && typeof (v as HasMatchFunction<T>).match === 'function';\n};\n\n/** @public */\nexport type Matcher<T> = HasMatchFunction<T> | TypeGuard<T>;\n\n/** @public */\nexport type ActionFromMatcher<M extends Matcher<any>> = M extends Matcher<infer T> ? T : never;\nexport type Id<T> = { [K in keyof T]: T[K] } & {};\nexport type Tail<T extends any[]> = T extends [any, ...infer Tail] ? Tail : never;\nexport type UnknownIfNonSpecific<T> = {} extends T ? unknown : T;\n\n/**\n * A Promise that will never reject.\n * @see https://github.com/reduxjs/redux-toolkit/issues/4101\n */\nexport type SafePromise<T> = Promise<T> & {\n  __linterBrands: 'SafePromise';\n};\n\n/**\n * Properly wraps a Promise as a {@link SafePromise} with .catch(fallback).\n */\nexport function asSafePromise<Resolved, Rejected>(promise: Promise<Resolved>, fallback: (error: unknown) => Rejected) {\n  return promise.catch(fallback) as SafePromise<Resolved | Rejected>;\n}", "import type { Middleware } from 'redux';\nimport { isActionCreator as isRTKAction } from './createAction';\nexport interface ActionCreatorInvariantMiddlewareOptions {\n  /**\n   * The function to identify whether a value is an action creator.\n   * The default checks for a function with a static type property and match method.\n   */\n  isActionCreator?: (action: unknown) => action is Function & {\n    type?: unknown;\n  };\n}\nexport function getMessage(type?: unknown) {\n  const splitType = type ? `${type}`.split('/') : [];\n  const actionName = splitType[splitType.length - 1] || 'actionCreator';\n  return `Detected an action creator with type \"${type || 'unknown'}\" being dispatched. \nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`;\n}\nexport function createActionCreatorInvariantMiddleware(options: ActionCreatorInvariantMiddlewareOptions = {}): Middleware {\n  if (process.env.NODE_ENV === 'production') {\n    return () => next => action => next(action);\n  }\n  const {\n    isActionCreator = isRTKAction\n  } = options;\n  return () => next => action => {\n    if (isActionCreator(action)) {\n      console.warn(getMessage(action.type));\n    }\n    return next(action);\n  };\n}", "import { produce as createNextState, isDraftable } from 'immer';\nexport function getTimeMeasureUtils(maxDelay: number, fnName: string) {\n  let elapsed = 0;\n  return {\n    measureTime<T>(fn: () => T): T {\n      const started = Date.now();\n      try {\n        return fn();\n      } finally {\n        const finished = Date.now();\n        elapsed += finished - started;\n      }\n    },\n    warnIfExceeded() {\n      if (elapsed > maxDelay) {\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\nIt is disabled in production builds, so you don't need to worry about that.`);\n      }\n    }\n  };\n}\nexport function delay(ms: number) {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\nexport class Tuple<Items extends ReadonlyArray<unknown> = []> extends Array<Items[number]> {\n  constructor(length: number);\n  constructor(...items: Items);\n  constructor(...items: any[]) {\n    super(...items);\n    Object.setPrototypeOf(this, Tuple.prototype);\n  }\n  static override get [Symbol.species]() {\n    return Tuple as any;\n  }\n  override concat<AdditionalItems extends ReadonlyArray<unknown>>(items: Tuple<AdditionalItems>): Tuple<[...Items, ...AdditionalItems]>;\n  override concat<AdditionalItems extends ReadonlyArray<unknown>>(items: AdditionalItems): Tuple<[...Items, ...AdditionalItems]>;\n  override concat<AdditionalItems extends ReadonlyArray<unknown>>(...items: AdditionalItems): Tuple<[...Items, ...AdditionalItems]>;\n  override concat(...arr: any[]) {\n    return super.concat.apply(this, arr);\n  }\n  prepend<AdditionalItems extends ReadonlyArray<unknown>>(items: Tuple<AdditionalItems>): Tuple<[...AdditionalItems, ...Items]>;\n  prepend<AdditionalItems extends ReadonlyArray<unknown>>(items: AdditionalItems): Tuple<[...AdditionalItems, ...Items]>;\n  prepend<AdditionalItems extends ReadonlyArray<unknown>>(...items: AdditionalItems): Tuple<[...AdditionalItems, ...Items]>;\n  prepend(...arr: any[]) {\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new Tuple(...arr[0].concat(this));\n    }\n    return new Tuple(...arr.concat(this));\n  }\n}\nexport function freezeDraftable<T>(val: T) {\n  return isDraftable(val) ? createNextState(val, () => {}) : val;\n}\nexport function getOrInsert<K extends object, V>(map: WeakMap<K, V>, key: K, value: V): V;\nexport function getOrInsert<K, V>(map: Map<K, V>, key: K, value: V): V;\nexport function getOrInsert<K extends object, V>(map: Map<K, V> | WeakMap<K, V>, key: K, value: V): V {\n  if (map.has(key)) return map.get(key) as V;\n  return map.set(key, value).get(key) as V;\n}\nexport function getOrInsertComputed<K extends object, V>(map: WeakMap<K, V>, key: K, compute: (key: K) => V): V;\nexport function getOrInsertComputed<K, V>(map: Map<K, V>, key: K, compute: (key: K) => V): V;\nexport function getOrInsertComputed<K extends object, V>(map: Map<K, V> | WeakMap<K, V>, key: K, compute: (key: K) => V): V {\n  if (map.has(key)) return map.get(key) as V;\n  return map.set(key, compute(key)).get(key) as V;\n}\nexport function promiseWithResolvers<T>(): {\n  promise: Promise<T>;\n  resolve: (value: T | PromiseLike<T>) => void;\n  reject: (reason?: any) => void;\n} {\n  let resolve: any;\n  let reject: any;\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return {\n    promise,\n    resolve,\n    reject\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2 } from \"@reduxjs/toolkit\";\nimport type { Middleware } from 'redux';\nimport type { IgnorePaths } from './serializableStateInvariantMiddleware';\nimport { getTimeMeasureUtils } from './utils';\ntype EntryProcessor = (key: string, value: any) => any;\n\n/**\n * The default `isImmutable` function.\n *\n * @public\n */\nexport function isImmutableDefault(value: unknown): boolean {\n  return typeof value !== 'object' || value == null || Object.isFrozen(value);\n}\nexport function trackForMutations(isImmutable: IsImmutableFunc, ignorePaths: IgnorePaths | undefined, obj: any) {\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\n  return {\n    detectMutations() {\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\n    }\n  };\n}\ninterface TrackedProperty {\n  value: any;\n  children: Record<string, any>;\n}\nfunction trackProperties(isImmutable: IsImmutableFunc, ignorePaths: IgnorePaths = [], obj: Record<string, any>, path: string = '', checkedObjects: Set<Record<string, any>> = new Set()) {\n  const tracked: Partial<TrackedProperty> = {\n    value: obj\n  };\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\n    checkedObjects.add(obj);\n    tracked.children = {};\n    for (const key in obj) {\n      const childPath = path ? path + '.' + key : key;\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\n        continue;\n      }\n      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\n    }\n  }\n  return tracked as TrackedProperty;\n}\nfunction detectMutations(isImmutable: IsImmutableFunc, ignoredPaths: IgnorePaths = [], trackedProperty: TrackedProperty, obj: any, sameParentRef: boolean = false, path: string = ''): {\n  wasMutated: boolean;\n  path?: string;\n} {\n  const prevObj = trackedProperty ? trackedProperty.value : undefined;\n  const sameRef = prevObj === obj;\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\n    return {\n      wasMutated: true,\n      path\n    };\n  }\n  if (isImmutable(prevObj) || isImmutable(obj)) {\n    return {\n      wasMutated: false\n    };\n  }\n\n  // Gather all keys from prev (tracked) and after objs\n  const keysToDetect: Record<string, boolean> = {};\n  for (let key in trackedProperty.children) {\n    keysToDetect[key] = true;\n  }\n  for (let key in obj) {\n    keysToDetect[key] = true;\n  }\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (let key in keysToDetect) {\n    const nestedPath = path ? path + '.' + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some(ignored => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    const result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\n    if (result.wasMutated) {\n      return result;\n    }\n  }\n  return {\n    wasMutated: false\n  };\n}\ntype IsImmutableFunc = (value: any) => boolean;\n\n/**\n * Options for `createImmutableStateInvariantMiddleware()`.\n *\n * @public\n */\nexport interface ImmutableStateInvariantMiddlewareOptions {\n  /**\n    Callback function to check if a value is considered to be immutable.\n    This function is applied recursively to every value contained in the state.\n    The default implementation will return true for primitive types\n    (like numbers, strings, booleans, null and undefined).\n   */\n  isImmutable?: IsImmutableFunc;\n  /**\n    An array of dot-separated path strings that match named nodes from\n    the root state to ignore when checking for immutability.\n    Defaults to undefined\n   */\n  ignoredPaths?: IgnorePaths;\n  /** Print a warning if checks take longer than N ms. Default: 32ms */\n  warnAfter?: number;\n}\n\n/**\n * Creates a middleware that checks whether any state was mutated in between\n * dispatches or during a dispatch. If any mutations are detected, an error is\n * thrown.\n *\n * @param options Middleware options.\n *\n * @public\n */\nexport function createImmutableStateInvariantMiddleware(options: ImmutableStateInvariantMiddlewareOptions = {}): Middleware {\n  if (process.env.NODE_ENV === 'production') {\n    return () => next => action => next(action);\n  } else {\n    function stringify(obj: any, serializer?: EntryProcessor, indent?: string | number, decycler?: EntryProcessor): string {\n      return JSON.stringify(obj, getSerialize(serializer, decycler), indent);\n    }\n    function getSerialize(serializer?: EntryProcessor, decycler?: EntryProcessor): EntryProcessor {\n      let stack: any[] = [],\n        keys: any[] = [];\n      if (!decycler) decycler = function (_: string, value: any) {\n        if (stack[0] === value) return '[Circular ~]';\n        return '[Circular ~.' + keys.slice(0, stack.indexOf(value)).join('.') + ']';\n      };\n      return function (this: any, key: string, value: any) {\n        if (stack.length > 0) {\n          var thisPos = stack.indexOf(this);\n          ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n          ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n          if (~stack.indexOf(value)) value = decycler!.call(this, key, value);\n        } else stack.push(value);\n        return serializer == null ? value : serializer.call(this, key, value);\n      };\n    }\n    let {\n      isImmutable = isImmutableDefault,\n      ignoredPaths,\n      warnAfter = 32\n    } = options;\n    const track = trackForMutations.bind(null, isImmutable, ignoredPaths);\n    return ({\n      getState\n    }) => {\n      let state = getState();\n      let tracker = track(state);\n      let result;\n      return next => action => {\n        const measureUtils = getTimeMeasureUtils(warnAfter, 'ImmutableStateInvariantMiddleware');\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          // Track before potentially not meeting the invariant\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(19) : `A state mutation was detected between dispatches, in the path '${result.path || ''}'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        const dispatchedAction = next(action);\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          // Track before potentially not meeting the invariant\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(20) : `A state mutation was detected inside a dispatch, in the path: ${result.path || ''}. Take a look at the reducer(s) handling the action ${stringify(action)}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n        return dispatchedAction;\n      };\n    };\n  }\n}", "import type { Middleware } from 'redux';\nimport { isAction, isPlainObject } from 'redux';\nimport { getTimeMeasureUtils } from './utils';\n\n/**\n * Returns true if the passed value is \"plain\", i.e. a value that is either\n * directly JSON-serializable (boolean, number, string, array, plain object)\n * or `undefined`.\n *\n * @param val The value to check.\n *\n * @public\n */\nexport function isPlain(val: any) {\n  const type = typeof val;\n  return val == null || type === 'string' || type === 'boolean' || type === 'number' || Array.isArray(val) || isPlainObject(val);\n}\ninterface NonSerializableValue {\n  keyPath: string;\n  value: unknown;\n}\nexport type IgnorePaths = readonly (string | RegExp)[];\n\n/**\n * @public\n */\nexport function findNonSerializableValue(value: unknown, path: string = '', isSerializable: (value: unknown) => boolean = isPlain, getEntries?: (value: unknown) => [string, any][], ignoredPaths: IgnorePaths = [], cache?: WeakSet<object>): NonSerializableValue | false {\n  let foundNestedSerializable: NonSerializableValue | false;\n  if (!isSerializable(value)) {\n    return {\n      keyPath: path || '<root>',\n      value: value\n    };\n  }\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n  if (cache?.has(value)) return false;\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value);\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (const [key, nestedValue] of entries) {\n    const nestedPath = path ? path + '.' + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some(ignored => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    if (!isSerializable(nestedValue)) {\n      return {\n        keyPath: nestedPath,\n        value: nestedValue\n      };\n    }\n    if (typeof nestedValue === 'object') {\n      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\n      if (foundNestedSerializable) {\n        return foundNestedSerializable;\n      }\n    }\n  }\n  if (cache && isNestedFrozen(value)) cache.add(value);\n  return false;\n}\nexport function isNestedFrozen(value: object) {\n  if (!Object.isFrozen(value)) return false;\n  for (const nestedValue of Object.values(value)) {\n    if (typeof nestedValue !== 'object' || nestedValue === null) continue;\n    if (!isNestedFrozen(nestedValue)) return false;\n  }\n  return true;\n}\n\n/**\n * Options for `createSerializableStateInvariantMiddleware()`.\n *\n * @public\n */\nexport interface SerializableStateInvariantMiddlewareOptions {\n  /**\n   * The function to check if a value is considered serializable. This\n   * function is applied recursively to every value contained in the\n   * state. Defaults to `isPlain()`.\n   */\n  isSerializable?: (value: any) => boolean;\n  /**\n   * The function that will be used to retrieve entries from each\n   * value.  If unspecified, `Object.entries` will be used. Defaults\n   * to `undefined`.\n   */\n  getEntries?: (value: any) => [string, any][];\n\n  /**\n   * An array of action types to ignore when checking for serializability.\n   * Defaults to []\n   */\n  ignoredActions?: string[];\n\n  /**\n   * An array of dot-separated path strings or regular expressions to ignore\n   * when checking for serializability, Defaults to\n   * ['meta.arg', 'meta.baseQueryMeta']\n   */\n  ignoredActionPaths?: (string | RegExp)[];\n\n  /**\n   * An array of dot-separated path strings or regular expressions to ignore\n   * when checking for serializability, Defaults to []\n   */\n  ignoredPaths?: (string | RegExp)[];\n  /**\n   * Execution time warning threshold. If the middleware takes longer\n   * than `warnAfter` ms, a warning will be displayed in the console.\n   * Defaults to 32ms.\n   */\n  warnAfter?: number;\n\n  /**\n   * Opt out of checking state. When set to `true`, other state-related params will be ignored.\n   */\n  ignoreState?: boolean;\n\n  /**\n   * Opt out of checking actions. When set to `true`, other action-related params will be ignored.\n   */\n  ignoreActions?: boolean;\n\n  /**\n   * Opt out of caching the results. The cache uses a WeakSet and speeds up repeated checking processes.\n   * The cache is automatically disabled if no browser support for WeakSet is present.\n   */\n  disableCache?: boolean;\n}\n\n/**\n * Creates a middleware that, after every state change, checks if the new\n * state is serializable. If a non-serializable value is found within the\n * state, an error is printed to the console.\n *\n * @param options Middleware options.\n *\n * @public\n */\nexport function createSerializableStateInvariantMiddleware(options: SerializableStateInvariantMiddlewareOptions = {}): Middleware {\n  if (process.env.NODE_ENV === 'production') {\n    return () => next => action => next(action);\n  } else {\n    const {\n      isSerializable = isPlain,\n      getEntries,\n      ignoredActions = [],\n      ignoredActionPaths = ['meta.arg', 'meta.baseQueryMeta'],\n      ignoredPaths = [],\n      warnAfter = 32,\n      ignoreState = false,\n      ignoreActions = false,\n      disableCache = false\n    } = options;\n    const cache: WeakSet<object> | undefined = !disableCache && WeakSet ? new WeakSet() : undefined;\n    return storeAPI => next => action => {\n      if (!isAction(action)) {\n        return next(action);\n      }\n      const result = next(action);\n      const measureUtils = getTimeMeasureUtils(warnAfter, 'SerializableStateInvariantMiddleware');\n      if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type as any) !== -1)) {\n        measureUtils.measureTime(() => {\n          const foundActionNonSerializableValue = findNonSerializableValue(action, '', isSerializable, getEntries, ignoredActionPaths, cache);\n          if (foundActionNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundActionNonSerializableValue;\n            console.error(`A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`, value, '\\nTake a look at the logic that dispatched this action: ', action, '\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)', '\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)');\n          }\n        });\n      }\n      if (!ignoreState) {\n        measureUtils.measureTime(() => {\n          const state = storeAPI.getState();\n          const foundStateNonSerializableValue = findNonSerializableValue(state, '', isSerializable, getEntries, ignoredPaths, cache);\n          if (foundStateNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundStateNonSerializableValue;\n            console.error(`A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`, value, `\nTake a look at the reducer(s) handling this action type: ${action.type}.\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n      }\n      return result;\n    };\n  }\n}", "import type { StoreEnhancer } from 'redux';\nexport const SHOULD_AUTOBATCH = 'RTK_autoBatch';\nexport const prepareAutoBatched = <T,>() => (payload: T): {\n  payload: T;\n  meta: unknown;\n} => ({\n  payload,\n  meta: {\n    [SHOULD_AUTOBATCH]: true\n  }\n});\nconst createQueueWithTimer = (timeout: number) => {\n  return (notify: () => void) => {\n    setTimeout(notify, timeout);\n  };\n};\nexport type AutoBatchOptions = {\n  type: 'tick';\n} | {\n  type: 'timer';\n  timeout: number;\n} | {\n  type: 'raf';\n} | {\n  type: 'callback';\n  queueNotification: (notify: () => void) => void;\n};\n\n/**\n * A Redux store enhancer that watches for \"low-priority\" actions, and delays\n * notifying subscribers until either the queued callback executes or the\n * next \"standard-priority\" action is dispatched.\n *\n * This allows dispatching multiple \"low-priority\" actions in a row with only\n * a single subscriber notification to the UI after the sequence of actions\n * is finished, thus improving UI re-render performance.\n *\n * Watches for actions with the `action.meta[SHOULD_AUTOBATCH]` attribute.\n * This can be added to `action.meta` manually, or by using the\n * `prepareAutoBatched` helper.\n *\n * By default, it will queue a notification for the end of the event loop tick.\n * However, you can pass several other options to configure the behavior:\n * - `{type: 'tick'}`: queues using `queueMicrotask`\n * - `{type: 'timer', timeout: number}`: queues using `setTimeout`\n * - `{type: 'raf'}`: queues using `requestAnimationFrame` (default)\n * - `{type: 'callback', queueNotification: (notify: () => void) => void}`: lets you provide your own callback\n *\n *\n */\nexport const autoBatchEnhancer = (options: AutoBatchOptions = {\n  type: 'raf'\n}): StoreEnhancer => next => (...args) => {\n  const store = next(...args);\n  let notifying = true;\n  let shouldNotifyAtEndOfTick = false;\n  let notificationQueued = false;\n  const listeners = new Set<() => void>();\n  const queueCallback = options.type === 'tick' ? queueMicrotask : options.type === 'raf' ?\n  // requestAnimationFrame won't exist in SSR environments. Fall back to a vague approximation just to keep from erroring.\n  typeof window !== 'undefined' && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10) : options.type === 'callback' ? options.queueNotification : createQueueWithTimer(options.timeout);\n  const notifyListeners = () => {\n    // We're running at the end of the event loop tick.\n    // Run the real listener callbacks to actually update the UI.\n    notificationQueued = false;\n    if (shouldNotifyAtEndOfTick) {\n      shouldNotifyAtEndOfTick = false;\n      listeners.forEach(l => l());\n    }\n  };\n  return Object.assign({}, store, {\n    // Override the base `store.subscribe` method to keep original listeners\n    // from running if we're delaying notifications\n    subscribe(listener: () => void) {\n      // Each wrapped listener will only call the real listener if\n      // the `notifying` flag is currently active when it's called.\n      // This lets the base store work as normal, while the actual UI\n      // update becomes controlled by this enhancer.\n      const wrappedListener: typeof listener = () => notifying && listener();\n      const unsubscribe = store.subscribe(wrappedListener);\n      listeners.add(listener);\n      return () => {\n        unsubscribe();\n        listeners.delete(listener);\n      };\n    },\n    // Override the base `store.dispatch` method so that we can check actions\n    // for the `shouldAutoBatch` flag and determine if batching is active\n    dispatch(action: any) {\n      try {\n        // If the action does _not_ have the `shouldAutoBatch` flag,\n        // we resume/continue normal notify-after-each-dispatch behavior\n        notifying = !action?.meta?.[SHOULD_AUTOBATCH];\n        // If a `notifyListeners` microtask was queued, you can't cancel it.\n        // Instead, we set a flag so that it's a no-op when it does run\n        shouldNotifyAtEndOfTick = !notifying;\n        if (shouldNotifyAtEndOfTick) {\n          // We've seen at least 1 action with `SHOULD_AUTOBATCH`. Try to queue\n          // a microtask to notify listeners at the end of the event loop tick.\n          // Make sure we only enqueue this _once_ per tick.\n          if (!notificationQueued) {\n            notificationQueued = true;\n            queueCallback(notifyListeners);\n          }\n        }\n        // Go ahead and process the action as usual, including reducers.\n        // If normal notification behavior is enabled, the store will notify\n        // all of its own listeners, and the wrapper callbacks above will\n        // see `notifying` is true and pass on to the real listener callbacks.\n        // If we're \"batching\" behavior, then the wrapped callbacks will\n        // bail out, causing the base store notification behavior to be no-ops.\n        return store.dispatch(action);\n      } finally {\n        // Assume we're back to normal behavior after each action\n        notifying = true;\n      }\n    }\n  });\n};", "import type { StoreEnhancer } from 'redux';\nimport type { AutoBatchOptions } from './autoBatchEnhancer';\nimport { autoBatchEnhancer } from './autoBatchEnhancer';\nimport { Tuple } from './utils';\nimport type { Middlewares } from './configureStore';\nimport type { ExtractDispatchExtensions } from './tsHelpers';\ntype GetDefaultEnhancersOptions = {\n  autoBatch?: boolean | AutoBatchOptions;\n};\nexport type GetDefaultEnhancers<M extends Middlewares<any>> = (options?: GetDefaultEnhancersOptions) => Tuple<[StoreEnhancer<{\n  dispatch: ExtractDispatchExtensions<M>;\n}>]>;\nexport const buildGetDefaultEnhancers = <M extends Middlewares<any>,>(middlewareEnhancer: StoreEnhancer<{\n  dispatch: ExtractDispatchExtensions<M>;\n}>): GetDefaultEnhancers<M> => function getDefaultEnhancers(options) {\n  const {\n    autoBatch = true\n  } = options ?? {};\n  let enhancerArray = new Tuple<StoreEnhancer[]>(middlewareEnhancer);\n  if (autoBatch) {\n    enhancerArray.push(autoBatchEnhancer(typeof autoBatch === 'object' ? autoBatch : undefined));\n  }\n  return enhancerArray as any;\n};", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { Draft } from 'immer';\nimport { produce as createNextState, isDraft, isDraftable } from 'immer';\nimport type { Action, Reducer, UnknownAction } from 'redux';\nimport type { ActionReducerMapBuilder } from './mapBuilders';\nimport { executeReducerBuilderCallback } from './mapBuilders';\nimport type { NoInfer, TypeGuard } from './tsHelpers';\nimport { freezeDraftable } from './utils';\n\n/**\n * Defines a mapping from action types to corresponding action object shapes.\n *\n * @deprecated This should not be used manually - it is only used for internal\n *             inference purposes and should not have any further value.\n *             It might be removed in the future.\n * @public\n */\nexport type Actions<T extends keyof any = string> = Record<T, Action>;\nexport type ActionMatcherDescription<S, A extends Action> = {\n  matcher: TypeGuard<A>;\n  reducer: CaseReducer<S, NoInfer<A>>;\n};\nexport type ReadonlyActionMatcherDescriptionCollection<S> = ReadonlyArray<ActionMatcherDescription<S, any>>;\nexport type ActionMatcherDescriptionCollection<S> = Array<ActionMatcherDescription<S, any>>;\n\n/**\n * A *case reducer* is a reducer function for a specific action type. Case\n * reducers can be composed to full reducers using `createReducer()`.\n *\n * Unlike a normal Redux reducer, a case reducer is never called with an\n * `undefined` state to determine the initial state. Instead, the initial\n * state is explicitly specified as an argument to `createReducer()`.\n *\n * In addition, a case reducer can choose to mutate the passed-in `state`\n * value directly instead of returning a new state. This does not actually\n * cause the store state to be mutated directly; instead, thanks to\n * [immer](https://github.com/mweststrate/immer), the mutations are\n * translated to copy operations that result in a new state.\n *\n * @public\n */\nexport type CaseReducer<S = any, A extends Action = UnknownAction> = (state: Draft<S>, action: A) => NoInfer<S> | void | Draft<NoInfer<S>>;\n\n/**\n * A mapping from action types to case reducers for `createReducer()`.\n *\n * @deprecated This should not be used manually - it is only used\n *             for internal inference purposes and using it manually\n *             would lead to type erasure.\n *             It might be removed in the future.\n * @public\n */\nexport type CaseReducers<S, AS extends Actions> = { [T in keyof AS]: AS[T] extends Action ? CaseReducer<S, AS[T]> : void };\nexport type NotFunction<T> = T extends Function ? never : T;\nfunction isStateFunction<S>(x: unknown): x is () => S {\n  return typeof x === 'function';\n}\nexport type ReducerWithInitialState<S extends NotFunction<any>> = Reducer<S> & {\n  getInitialState: () => S;\n};\n\n/**\n * A utility function that allows defining a reducer as a mapping from action\n * type to *case reducer* functions that handle these action types. The\n * reducer's initial state is passed as the first argument.\n *\n * @remarks\n * The body of every case reducer is implicitly wrapped with a call to\n * `produce()` from the [immer](https://github.com/mweststrate/immer) library.\n * This means that rather than returning a new state object, you can also\n * mutate the passed-in state object directly; these mutations will then be\n * automatically and efficiently translated into copies, giving you both\n * convenience and immutability.\n *\n * @overloadSummary\n * This function accepts a callback that receives a `builder` object as its argument.\n * That builder provides `addCase`, `addMatcher` and `addDefaultCase` functions that may be\n * called to define what actions this reducer will handle.\n *\n * @param initialState - `State | (() => State)`: The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\n * @param builderCallback - `(builder: Builder) => void` A callback that receives a *builder* object to define\n *   case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\n * @example\n```ts\nimport {\n  createAction,\n  createReducer,\n  UnknownAction,\n  PayloadAction,\n} from \"@reduxjs/toolkit\";\n\nconst increment = createAction<number>(\"increment\");\nconst decrement = createAction<number>(\"decrement\");\n\nfunction isActionWithNumberPayload(\n  action: UnknownAction\n): action is PayloadAction<number> {\n  return typeof action.payload === \"number\";\n}\n\nconst reducer = createReducer(\n  {\n    counter: 0,\n    sumOfNumberPayloads: 0,\n    unhandledActions: 0,\n  },\n  (builder) => {\n    builder\n      .addCase(increment, (state, action) => {\n        // action is inferred correctly here\n        state.counter += action.payload;\n      })\n      // You can chain calls, or have separate `builder.addCase()` lines each time\n      .addCase(decrement, (state, action) => {\n        state.counter -= action.payload;\n      })\n      // You can apply a \"matcher function\" to incoming actions\n      .addMatcher(isActionWithNumberPayload, (state, action) => {})\n      // and provide a default case if no other handlers matched\n      .addDefaultCase((state, action) => {});\n  }\n);\n```\n * @public\n */\nexport function createReducer<S extends NotFunction<any>>(initialState: S | (() => S), mapOrBuilderCallback: (builder: ActionReducerMapBuilder<S>) => void): ReducerWithInitialState<S> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof mapOrBuilderCallback === 'object') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(8) : \"The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\n    }\n  }\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] = executeReducerBuilderCallback(mapOrBuilderCallback);\n\n  // Ensure the initial state gets frozen either way (if draftable)\n  let getInitialState: () => S;\n  if (isStateFunction(initialState)) {\n    getInitialState = () => freezeDraftable(initialState());\n  } else {\n    const frozenInitialState = freezeDraftable(initialState);\n    getInitialState = () => frozenInitialState;\n  }\n  function reducer(state = getInitialState(), action: any): S {\n    let caseReducers = [actionsMap[action.type], ...finalActionMatchers.filter(({\n      matcher\n    }) => matcher(action)).map(({\n      reducer\n    }) => reducer)];\n    if (caseReducers.filter(cr => !!cr).length === 0) {\n      caseReducers = [finalDefaultCaseReducer];\n    }\n    return caseReducers.reduce((previousState, caseReducer): S => {\n      if (caseReducer) {\n        if (isDraft(previousState)) {\n          // If it's already a draft, we must already be inside a `createNextState` call,\n          // likely because this is being wrapped in `createReducer`, `createSlice`, or nested\n          // inside an existing draft. It's safe to just pass the draft to the mutator.\n          const draft = previousState as Draft<S>; // We can assume this is already a draft\n          const result = caseReducer(draft, action);\n          if (result === undefined) {\n            return previousState;\n          }\n          return result as S;\n        } else if (!isDraftable(previousState)) {\n          // If state is not draftable (ex: a primitive, such as 0), we want to directly\n          // return the caseReducer func and not wrap it with produce.\n          const result = caseReducer(previousState as any, action);\n          if (result === undefined) {\n            if (previousState === null) {\n              return previousState;\n            }\n            throw Error('A case reducer on a non-draftable value must not return undefined');\n          }\n          return result as S;\n        } else {\n          // @ts-ignore createNextState() produces an Immutable<Draft<S>> rather\n          // than an Immutable<S>, and TypeScript cannot find out how to reconcile\n          // these two types.\n          return createNextState(previousState, (draft: Draft<S>) => {\n            return caseReducer(draft, action);\n          });\n        }\n      }\n      return previousState;\n    }, state);\n  }\n  reducer.getInitialState = getInitialState;\n  return reducer as ReducerWithInitialState<S>;\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3, formatProdErrorMessage as _formatProdErrorMessage4, formatProdErrorMessage as _formatProdErrorMessage5, formatProdErrorMessage as _formatProdErrorMessage6 } from \"@reduxjs/toolkit\";\nimport type { Action } from 'redux';\nimport type { CaseReducer, CaseReducers, ActionMatcherDescriptionCollection } from './createReducer';\nimport type { TypeGuard } from './tsHelpers';\nexport type TypedActionCreator<Type extends string> = {\n  (...args: any[]): Action<Type>;\n  type: Type;\n};\n\n/**\n * A builder for an action <-> reducer map.\n *\n * @public\n */\nexport interface ActionReducerMapBuilder<State> {\n  /**\n   * Adds a case reducer to handle a single exact action type.\n   * @remarks\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<ActionCreator extends TypedActionCreator<string>>(actionCreator: ActionCreator, reducer: CaseReducer<State, ReturnType<ActionCreator>>): ActionReducerMapBuilder<State>;\n  /**\n   * Adds a case reducer to handle a single exact action type.\n   * @remarks\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<Type extends string, A extends Action<Type>>(type: Type, reducer: CaseReducer<State, A>): ActionReducerMapBuilder<State>;\n\n  /**\n   * Allows you to match your incoming actions against your own filter function instead of only the `action.type` property.\n   * @remarks\n   * If multiple matcher reducers match, all of them will be executed in the order\n   * they were defined in - even if a case reducer already matched.\n   * All calls to `builder.addMatcher` must come after any calls to `builder.addCase` and before any calls to `builder.addDefaultCase`.\n   * @param matcher - A matcher function. In TypeScript, this should be a [type predicate](https://www.typescriptlang.org/docs/handbook/2/narrowing.html#using-type-predicates)\n   *   function\n   * @param reducer - The actual case reducer function.\n   *\n   * @example\n  ```ts\n  import {\n  createAction,\n  createReducer,\n  AsyncThunk,\n  UnknownAction,\n  } from \"@reduxjs/toolkit\";\n  type GenericAsyncThunk = AsyncThunk<unknown, unknown, any>;\n  type PendingAction = ReturnType<GenericAsyncThunk[\"pending\"]>;\n  type RejectedAction = ReturnType<GenericAsyncThunk[\"rejected\"]>;\n  type FulfilledAction = ReturnType<GenericAsyncThunk[\"fulfilled\"]>;\n  const initialState: Record<string, string> = {};\n  const resetAction = createAction(\"reset-tracked-loading-state\");\n  function isPendingAction(action: UnknownAction): action is PendingAction {\n  return typeof action.type === \"string\" && action.type.endsWith(\"/pending\");\n  }\n  const reducer = createReducer(initialState, (builder) => {\n  builder\n    .addCase(resetAction, () => initialState)\n    // matcher can be defined outside as a type predicate function\n    .addMatcher(isPendingAction, (state, action) => {\n      state[action.meta.requestId] = \"pending\";\n    })\n    .addMatcher(\n      // matcher can be defined inline as a type predicate function\n      (action): action is RejectedAction => action.type.endsWith(\"/rejected\"),\n      (state, action) => {\n        state[action.meta.requestId] = \"rejected\";\n      }\n    )\n    // matcher can just return boolean and the matcher can receive a generic argument\n    .addMatcher<FulfilledAction>(\n      (action) => action.type.endsWith(\"/fulfilled\"),\n      (state, action) => {\n        state[action.meta.requestId] = \"fulfilled\";\n      }\n    );\n  });\n  ```\n   */\n  addMatcher<A>(matcher: TypeGuard<A> | ((action: any) => boolean), reducer: CaseReducer<State, A extends Action ? A : A & Action>): Omit<ActionReducerMapBuilder<State>, 'addCase'>;\n\n  /**\n   * Adds a \"default case\" reducer that is executed if no case reducer and no matcher\n   * reducer was executed for this action.\n   * @param reducer - The fallback \"default case\" reducer function.\n   *\n   * @example\n  ```ts\n  import { createReducer } from '@reduxjs/toolkit'\n  const initialState = { otherActions: 0 }\n  const reducer = createReducer(initialState, builder => {\n  builder\n    // .addCase(...)\n    // .addMatcher(...)\n    .addDefaultCase((state, action) => {\n      state.otherActions++\n    })\n  })\n  ```\n   */\n  addDefaultCase(reducer: CaseReducer<State, Action>): {};\n}\nexport function executeReducerBuilderCallback<S>(builderCallback: (builder: ActionReducerMapBuilder<S>) => void): [CaseReducers<S, any>, ActionMatcherDescriptionCollection<S>, CaseReducer<S, Action> | undefined] {\n  const actionsMap: CaseReducers<S, any> = {};\n  const actionMatchers: ActionMatcherDescriptionCollection<S> = [];\n  let defaultCaseReducer: CaseReducer<S, Action> | undefined;\n  const builder = {\n    addCase(typeOrActionCreator: string | TypedActionCreator<any>, reducer: CaseReducer<S>) {\n      if (process.env.NODE_ENV !== 'production') {\n        /*\n         to keep the definition by the user in line with actual behavior,\n         we enforce `addCase` to always be called before calling `addMatcher`\n         as matching cases take precedence over matchers\n         */\n        if (actionMatchers.length > 0) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(26) : '`builder.addCase` should only be called before calling `builder.addMatcher`');\n        }\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(27) : '`builder.addCase` should only be called before calling `builder.addDefaultCase`');\n        }\n      }\n      const type = typeof typeOrActionCreator === 'string' ? typeOrActionCreator : typeOrActionCreator.type;\n      if (!type) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(28) : '`builder.addCase` cannot be called with an empty action type');\n      }\n      if (type in actionsMap) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(29) : '`builder.addCase` cannot be called with two reducers for the same action type ' + `'${type}'`);\n      }\n      actionsMap[type] = reducer;\n      return builder;\n    },\n    addMatcher<A>(matcher: TypeGuard<A>, reducer: CaseReducer<S, A extends Action ? A : A & Action>) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(30) : '`builder.addMatcher` should only be called before calling `builder.addDefaultCase`');\n        }\n      }\n      actionMatchers.push({\n        matcher,\n        reducer\n      });\n      return builder;\n    },\n    addDefaultCase(reducer: CaseReducer<S, Action>) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(31) : '`builder.addDefaultCase` can only be called once');\n        }\n      }\n      defaultCaseReducer = reducer;\n      return builder;\n    }\n  };\n  builderCallback(builder);\n  return [actionsMap, actionMatchers, defaultCaseReducer];\n}", "import type { Action<PERSON>romMatcher, Matcher, UnionToIntersection } from './tsHelpers';\nimport { hasMatchFunction } from './tsHelpers';\nimport type { AsyncThunk, AsyncThunkFulfilledActionCreator, AsyncThunkPendingActionCreator, AsyncThunkRejectedActionCreator } from './createAsyncThunk';\n\n/** @public */\nexport type ActionMatchingAnyOf<Matchers extends Matcher<any>[]> = ActionFromMatcher<Matchers[number]>;\n\n/** @public */\nexport type ActionMatchingAllOf<Matchers extends Matcher<any>[]> = UnionToIntersection<ActionMatchingAnyOf<Matchers>>;\nconst matches = (matcher: Matcher<any>, action: any) => {\n  if (hasMatchFunction(matcher)) {\n    return matcher.match(action);\n  } else {\n    return matcher(action);\n  }\n};\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action matches any one of the supplied type guards or action\n * creators.\n *\n * @param matchers The type guards or action creators to match against.\n *\n * @public\n */\nexport function isAnyOf<Matchers extends Matcher<any>[]>(...matchers: Matchers) {\n  return (action: any): action is ActionMatchingAnyOf<Matchers> => {\n    return matchers.some(matcher => matches(matcher, action));\n  };\n}\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action matches all of the supplied type guards or action\n * creators.\n *\n * @param matchers The type guards or action creators to match against.\n *\n * @public\n */\nexport function isAllOf<Matchers extends Matcher<any>[]>(...matchers: Matchers) {\n  return (action: any): action is ActionMatchingAllOf<Matchers> => {\n    return matchers.every(matcher => matches(matcher, action));\n  };\n}\n\n/**\n * @param action A redux action\n * @param validStatus An array of valid meta.requestStatus values\n *\n * @internal\n */\nexport function hasExpectedRequestMetadata(action: any, validStatus: readonly string[]) {\n  if (!action || !action.meta) return false;\n  const hasValidRequestId = typeof action.meta.requestId === 'string';\n  const hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\n  return hasValidRequestId && hasValidRequestStatus;\n}\nfunction isAsyncThunkArray(a: [any] | AnyAsyncThunk[]): a is AnyAsyncThunk[] {\n  return typeof a[0] === 'function' && 'pending' in a[0] && 'fulfilled' in a[0] && 'rejected' in a[0];\n}\nexport type UnknownAsyncThunkPendingAction = ReturnType<AsyncThunkPendingActionCreator<unknown>>;\nexport type PendingActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['pending']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is pending.\n *\n * @public\n */\nexport function isPending(): (action: any) => action is UnknownAsyncThunkPendingAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is pending.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isPending<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is PendingActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a pending thunk action\n * @public\n */\nexport function isPending(action: any): action is UnknownAsyncThunkPendingAction;\nexport function isPending<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['pending']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isPending()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.pending));\n}\nexport type UnknownAsyncThunkRejectedAction = ReturnType<AsyncThunkRejectedActionCreator<unknown, unknown>>;\nexport type RejectedActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['rejected']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is rejected.\n *\n * @public\n */\nexport function isRejected(): (action: any) => action is UnknownAsyncThunkRejectedAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is rejected.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isRejected<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is RejectedActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a rejected thunk action\n * @public\n */\nexport function isRejected(action: any): action is UnknownAsyncThunkRejectedAction;\nexport function isRejected<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['rejected']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejected()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.rejected));\n}\nexport type UnknownAsyncThunkRejectedWithValueAction = ReturnType<AsyncThunkRejectedActionCreator<unknown, unknown>>;\nexport type RejectedWithValueActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['rejected']> & (T extends AsyncThunk<any, any, {\n  rejectValue: infer RejectedValue;\n}> ? {\n  payload: RejectedValue;\n} : unknown);\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is rejected with value.\n *\n * @public\n */\nexport function isRejectedWithValue(): (action: any) => action is UnknownAsyncThunkRejectedAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is rejected with value.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isRejectedWithValue<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is RejectedWithValueActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a rejected thunk action with value\n * @public\n */\nexport function isRejectedWithValue(action: any): action is UnknownAsyncThunkRejectedAction;\nexport function isRejectedWithValue<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  const hasFlag = (action: any): action is any => {\n    return action && action.meta && action.meta.rejectedWithValue;\n  };\n  if (asyncThunks.length === 0) {\n    return isAllOf(isRejected(...asyncThunks), hasFlag);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejectedWithValue()(asyncThunks[0]);\n  }\n  return isAllOf(isRejected(...asyncThunks), hasFlag);\n}\nexport type UnknownAsyncThunkFulfilledAction = ReturnType<AsyncThunkFulfilledActionCreator<unknown, unknown>>;\nexport type FulfilledActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['fulfilled']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is fulfilled.\n *\n * @public\n */\nexport function isFulfilled(): (action: any) => action is UnknownAsyncThunkFulfilledAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is fulfilled.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isFulfilled<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is FulfilledActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a fulfilled thunk action\n * @public\n */\nexport function isFulfilled(action: any): action is UnknownAsyncThunkFulfilledAction;\nexport function isFulfilled<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['fulfilled']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isFulfilled()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.fulfilled));\n}\nexport type UnknownAsyncThunkAction = UnknownAsyncThunkPendingAction | UnknownAsyncThunkRejectedAction | UnknownAsyncThunkFulfilledAction;\nexport type AnyAsyncThunk = {\n  pending: {\n    match: (action: any) => action is any;\n  };\n  fulfilled: {\n    match: (action: any) => action is any;\n  };\n  rejected: {\n    match: (action: any) => action is any;\n  };\n};\nexport type ActionsFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['pending']> | ActionFromMatcher<T['fulfilled']> | ActionFromMatcher<T['rejected']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator.\n *\n * @public\n */\nexport function isAsyncThunkAction(): (action: any) => action is UnknownAsyncThunkAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isAsyncThunkAction<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is ActionsFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a thunk action\n * @public\n */\nexport function isAsyncThunkAction(action: any): action is UnknownAsyncThunkAction;\nexport function isAsyncThunkAction<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['pending', 'fulfilled', 'rejected']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isAsyncThunkAction()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.flatMap(asyncThunk => [asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled]));\n}", "// Borrowed from https://github.com/ai/nanoid/blob/3.0.2/non-secure/index.js\n// This alphabet uses `A-Za-z0-9_-` symbols. A genetic algorithm helped\n// optimize the gzip compression for this alphabet.\nlet urlAlphabet = 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';\n\n/**\r\n *\r\n * @public\r\n */\nexport let nanoid = (size = 21) => {\n  let id = '';\n  // A compact alternative for `for (var i = 0; i < step; i++)`.\n  let i = size;\n  while (i--) {\n    // `| 0` is more compact and faster than `Math.floor()`.\n    id += urlAlphabet[Math.random() * 64 | 0];\n  }\n  return id;\n};", "import type { Dispatch, UnknownAction } from 'redux';\nimport type { ThunkDispatch } from 'redux-thunk';\nimport type { ActionCreatorWithPreparedPayload } from './createAction';\nimport { createAction } from './createAction';\nimport { isAnyOf } from './matchers';\nimport { nanoid } from './nanoid';\nimport type { FallbackIfUnknown, Id, IsAny, IsUnknown, SafePromise } from './tsHelpers';\nexport type BaseThunkAPI<S, E, D extends Dispatch = Dispatch, RejectedValue = unknown, RejectedMeta = unknown, FulfilledMeta = unknown> = {\n  dispatch: D;\n  getState: () => S;\n  extra: E;\n  requestId: string;\n  signal: AbortSignal;\n  abort: (reason?: string) => void;\n  rejectWithValue: IsUnknown<RejectedMeta, (value: RejectedValue) => RejectWithValue<RejectedValue, RejectedMeta>, (value: RejectedValue, meta: RejectedMeta) => RejectWithValue<RejectedValue, RejectedMeta>>;\n  fulfillWithValue: IsUnknown<FulfilledMeta, <FulfilledValue>(value: FulfilledValue) => FulfilledValue, <FulfilledValue>(value: FulfilledValue, meta: FulfilledMeta) => FulfillWithMeta<FulfilledValue, FulfilledMeta>>;\n};\n\n/**\n * @public\n */\nexport interface SerializedError {\n  name?: string;\n  message?: string;\n  stack?: string;\n  code?: string;\n}\nconst commonProperties: Array<keyof SerializedError> = ['name', 'message', 'stack', 'code'];\nclass RejectWithValue<Payload, RejectedMeta> {\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  private readonly _type!: 'RejectWithValue';\n  constructor(public readonly payload: Payload, public readonly meta: RejectedMeta) {}\n}\nclass FulfillWithMeta<Payload, FulfilledMeta> {\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  private readonly _type!: 'FulfillWithMeta';\n  constructor(public readonly payload: Payload, public readonly meta: FulfilledMeta) {}\n}\n\n/**\n * Serializes an error into a plain object.\n * Reworked from https://github.com/sindresorhus/serialize-error\n *\n * @public\n */\nexport const miniSerializeError = (value: any): SerializedError => {\n  if (typeof value === 'object' && value !== null) {\n    const simpleError: SerializedError = {};\n    for (const property of commonProperties) {\n      if (typeof value[property] === 'string') {\n        simpleError[property] = value[property];\n      }\n    }\n    return simpleError;\n  }\n  return {\n    message: String(value)\n  };\n};\nexport type AsyncThunkConfig = {\n  state?: unknown;\n  dispatch?: ThunkDispatch<unknown, unknown, UnknownAction>;\n  extra?: unknown;\n  rejectValue?: unknown;\n  serializedErrorType?: unknown;\n  pendingMeta?: unknown;\n  fulfilledMeta?: unknown;\n  rejectedMeta?: unknown;\n};\nexport type GetState<ThunkApiConfig> = ThunkApiConfig extends {\n  state: infer State;\n} ? State : unknown;\ntype GetExtra<ThunkApiConfig> = ThunkApiConfig extends {\n  extra: infer Extra;\n} ? Extra : unknown;\ntype GetDispatch<ThunkApiConfig> = ThunkApiConfig extends {\n  dispatch: infer Dispatch;\n} ? FallbackIfUnknown<Dispatch, ThunkDispatch<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, UnknownAction>> : ThunkDispatch<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, UnknownAction>;\nexport type GetThunkAPI<ThunkApiConfig> = BaseThunkAPI<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, GetDispatch<ThunkApiConfig>, GetRejectValue<ThunkApiConfig>, GetRejectedMeta<ThunkApiConfig>, GetFulfilledMeta<ThunkApiConfig>>;\ntype GetRejectValue<ThunkApiConfig> = ThunkApiConfig extends {\n  rejectValue: infer RejectValue;\n} ? RejectValue : unknown;\ntype GetPendingMeta<ThunkApiConfig> = ThunkApiConfig extends {\n  pendingMeta: infer PendingMeta;\n} ? PendingMeta : unknown;\ntype GetFulfilledMeta<ThunkApiConfig> = ThunkApiConfig extends {\n  fulfilledMeta: infer FulfilledMeta;\n} ? FulfilledMeta : unknown;\ntype GetRejectedMeta<ThunkApiConfig> = ThunkApiConfig extends {\n  rejectedMeta: infer RejectedMeta;\n} ? RejectedMeta : unknown;\ntype GetSerializedErrorType<ThunkApiConfig> = ThunkApiConfig extends {\n  serializedErrorType: infer GetSerializedErrorType;\n} ? GetSerializedErrorType : SerializedError;\ntype MaybePromise<T> = T | Promise<T> | (T extends any ? Promise<T> : never);\n\n/**\n * A type describing the return value of the `payloadCreator` argument to `createAsyncThunk`.\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\n *\n * @public\n */\nexport type AsyncThunkPayloadCreatorReturnValue<Returned, ThunkApiConfig extends AsyncThunkConfig> = MaybePromise<IsUnknown<GetFulfilledMeta<ThunkApiConfig>, Returned, FulfillWithMeta<Returned, GetFulfilledMeta<ThunkApiConfig>>> | RejectWithValue<GetRejectValue<ThunkApiConfig>, GetRejectedMeta<ThunkApiConfig>>>;\n/**\n * A type describing the `payloadCreator` argument to `createAsyncThunk`.\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\n *\n * @public\n */\nexport type AsyncThunkPayloadCreator<Returned, ThunkArg = void, ThunkApiConfig extends AsyncThunkConfig = {}> = (arg: ThunkArg, thunkAPI: GetThunkAPI<ThunkApiConfig>) => AsyncThunkPayloadCreatorReturnValue<Returned, ThunkApiConfig>;\n\n/**\n * A ThunkAction created by `createAsyncThunk`.\n * Dispatching it returns a Promise for either a\n * fulfilled or rejected action.\n * Also, the returned value contains an `abort()` method\n * that allows the asyncAction to be cancelled from the outside.\n *\n * @public\n */\nexport type AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig> = (dispatch: NonNullable<GetDispatch<ThunkApiConfig>>, getState: () => GetState<ThunkApiConfig>, extra: GetExtra<ThunkApiConfig>) => SafePromise<ReturnType<AsyncThunkFulfilledActionCreator<Returned, ThunkArg>> | ReturnType<AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>>> & {\n  abort: (reason?: string) => void;\n  requestId: string;\n  arg: ThunkArg;\n  unwrap: () => Promise<Returned>;\n};\n\n/**\n * Config provided when calling the async thunk action creator.\n */\nexport interface AsyncThunkDispatchConfig {\n  /**\n   * An external `AbortSignal` that will be tracked by the internal `AbortSignal`.\n   */\n  signal?: AbortSignal;\n}\ntype AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig> = IsAny<ThunkArg,\n// any handling\n(arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\n// unknown handling\nunknown extends ThunkArg ? (arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument not specified or specified as void or undefined\n: [ThunkArg] extends [void] | [undefined] ? (arg?: undefined, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains void\n: [void] extends [ThunkArg] // make optional\n? (arg?: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains undefined\n: [undefined] extends [ThunkArg] ? WithStrictNullChecks<\n// with strict nullChecks: make optional\n(arg?: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\n// without strict null checks this will match everything, so don't make it optional\n(arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>> // default case: normal argument\n: (arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>>;\n\n/**\n * Options object for `createAsyncThunk`.\n *\n * @public\n */\nexport type AsyncThunkOptions<ThunkArg = void, ThunkApiConfig extends AsyncThunkConfig = {}> = {\n  /**\n   * A method to control whether the asyncThunk should be executed. Has access to the\n   * `arg`, `api.getState()` and `api.extra` arguments.\n   *\n   * @returns `false` if it should be skipped\n   */\n  condition?(arg: ThunkArg, api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>): MaybePromise<boolean | undefined>;\n  /**\n   * If `condition` returns `false`, the asyncThunk will be skipped.\n   * This option allows you to control whether a `rejected` action with `meta.condition == false`\n   * will be dispatched or not.\n   *\n   * @default `false`\n   */\n  dispatchConditionRejection?: boolean;\n  serializeError?: (x: unknown) => GetSerializedErrorType<ThunkApiConfig>;\n\n  /**\n   * A function to use when generating the `requestId` for the request sequence.\n   *\n   * @default `nanoid`\n   */\n  idGenerator?: (arg: ThunkArg) => string;\n} & IsUnknown<GetPendingMeta<ThunkApiConfig>, {\n  /**\n   * A method to generate additional properties to be added to `meta` of the pending action.\n   *\n   * Using this optional overload will not modify the types correctly, this overload is only in place to support JavaScript users.\n   * Please use the `ThunkApiConfig` parameter `pendingMeta` to get access to a correctly typed overload\n   */\n  getPendingMeta?(base: {\n    arg: ThunkArg;\n    requestId: string;\n  }, api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>): GetPendingMeta<ThunkApiConfig>;\n}, {\n  /**\n   * A method to generate additional properties to be added to `meta` of the pending action.\n   */\n  getPendingMeta(base: {\n    arg: ThunkArg;\n    requestId: string;\n  }, api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>): GetPendingMeta<ThunkApiConfig>;\n}>;\nexport type AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig = {}> = ActionCreatorWithPreparedPayload<[string, ThunkArg, GetPendingMeta<ThunkApiConfig>?], undefined, string, never, {\n  arg: ThunkArg;\n  requestId: string;\n  requestStatus: 'pending';\n} & GetPendingMeta<ThunkApiConfig>>;\nexport type AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig = {}> = ActionCreatorWithPreparedPayload<[Error | null, string, ThunkArg, GetRejectValue<ThunkApiConfig>?, GetRejectedMeta<ThunkApiConfig>?], GetRejectValue<ThunkApiConfig> | undefined, string, GetSerializedErrorType<ThunkApiConfig>, {\n  arg: ThunkArg;\n  requestId: string;\n  requestStatus: 'rejected';\n  aborted: boolean;\n  condition: boolean;\n} & (({\n  rejectedWithValue: false;\n} & { [K in keyof GetRejectedMeta<ThunkApiConfig>]?: undefined }) | ({\n  rejectedWithValue: true;\n} & GetRejectedMeta<ThunkApiConfig>))>;\nexport type AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig = {}> = ActionCreatorWithPreparedPayload<[Returned, string, ThunkArg, GetFulfilledMeta<ThunkApiConfig>?], Returned, string, never, {\n  arg: ThunkArg;\n  requestId: string;\n  requestStatus: 'fulfilled';\n} & GetFulfilledMeta<ThunkApiConfig>>;\n\n/**\n * A type describing the return value of `createAsyncThunk`.\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\n *\n * @public\n */\nexport type AsyncThunk<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig> = AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig> & {\n  pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig>;\n  rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>;\n  fulfilled: AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig>;\n  // matchSettled?\n  settled: (action: any) => action is ReturnType<AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig> | AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig>>;\n  typePrefix: string;\n};\nexport type OverrideThunkApiConfigs<OldConfig, NewConfig> = Id<NewConfig & Omit<OldConfig, keyof NewConfig>>;\nexport type CreateAsyncThunkFunction<CurriedThunkApiConfig extends AsyncThunkConfig> = {\n  /**\n   *\n   * @param typePrefix\n   * @param payloadCreator\n   * @param options\n   *\n   * @public\n   */\n  // separate signature without `AsyncThunkConfig` for better inference\n  <Returned, ThunkArg = void>(typePrefix: string, payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, CurriedThunkApiConfig>, options?: AsyncThunkOptions<ThunkArg, CurriedThunkApiConfig>): AsyncThunk<Returned, ThunkArg, CurriedThunkApiConfig>;\n\n  /**\n   *\n   * @param typePrefix\n   * @param payloadCreator\n   * @param options\n   *\n   * @public\n   */\n  <Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig>(typePrefix: string, payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>, options?: AsyncThunkOptions<ThunkArg, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>): AsyncThunk<Returned, ThunkArg, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>;\n};\ntype CreateAsyncThunk<CurriedThunkApiConfig extends AsyncThunkConfig> = CreateAsyncThunkFunction<CurriedThunkApiConfig> & {\n  withTypes<ThunkApiConfig extends AsyncThunkConfig>(): CreateAsyncThunk<OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>;\n};\nconst externalAbortMessage = 'External signal was aborted';\nexport const createAsyncThunk = /* @__PURE__ */(() => {\n  function createAsyncThunk<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig>(typePrefix: string, payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, ThunkApiConfig>, options?: AsyncThunkOptions<ThunkArg, ThunkApiConfig>): AsyncThunk<Returned, ThunkArg, ThunkApiConfig> {\n    type RejectedValue = GetRejectValue<ThunkApiConfig>;\n    type PendingMeta = GetPendingMeta<ThunkApiConfig>;\n    type FulfilledMeta = GetFulfilledMeta<ThunkApiConfig>;\n    type RejectedMeta = GetRejectedMeta<ThunkApiConfig>;\n    const fulfilled: AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig> = createAction(typePrefix + '/fulfilled', (payload: Returned, requestId: string, arg: ThunkArg, meta?: FulfilledMeta) => ({\n      payload,\n      meta: {\n        ...(meta as any || {}),\n        arg,\n        requestId,\n        requestStatus: 'fulfilled' as const\n      }\n    }));\n    const pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig> = createAction(typePrefix + '/pending', (requestId: string, arg: ThunkArg, meta?: PendingMeta) => ({\n      payload: undefined,\n      meta: {\n        ...(meta as any || {}),\n        arg,\n        requestId,\n        requestStatus: 'pending' as const\n      }\n    }));\n    const rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig> = createAction(typePrefix + '/rejected', (error: Error | null, requestId: string, arg: ThunkArg, payload?: RejectedValue, meta?: RejectedMeta) => ({\n      payload,\n      error: (options && options.serializeError || miniSerializeError)(error || 'Rejected') as GetSerializedErrorType<ThunkApiConfig>,\n      meta: {\n        ...(meta as any || {}),\n        arg,\n        requestId,\n        rejectedWithValue: !!payload,\n        requestStatus: 'rejected' as const,\n        aborted: error?.name === 'AbortError',\n        condition: error?.name === 'ConditionError'\n      }\n    }));\n    function actionCreator(arg: ThunkArg, {\n      signal\n    }: AsyncThunkDispatchConfig = {}): AsyncThunkAction<Returned, ThunkArg, Required<ThunkApiConfig>> {\n      return (dispatch, getState, extra) => {\n        const requestId = options?.idGenerator ? options.idGenerator(arg) : nanoid();\n        const abortController = new AbortController();\n        let abortHandler: (() => void) | undefined;\n        let abortReason: string | undefined;\n        function abort(reason?: string) {\n          abortReason = reason;\n          abortController.abort();\n        }\n        if (signal) {\n          if (signal.aborted) {\n            abort(externalAbortMessage);\n          } else {\n            signal.addEventListener('abort', () => abort(externalAbortMessage), {\n              once: true\n            });\n          }\n        }\n        const promise = async function () {\n          let finalAction: ReturnType<typeof fulfilled | typeof rejected>;\n          try {\n            let conditionResult = options?.condition?.(arg, {\n              getState,\n              extra\n            });\n            if (isThenable(conditionResult)) {\n              conditionResult = await conditionResult;\n            }\n            if (conditionResult === false || abortController.signal.aborted) {\n              // eslint-disable-next-line no-throw-literal\n              throw {\n                name: 'ConditionError',\n                message: 'Aborted due to condition callback returning false.'\n              };\n            }\n            const abortedPromise = new Promise<never>((_, reject) => {\n              abortHandler = () => {\n                reject({\n                  name: 'AbortError',\n                  message: abortReason || 'Aborted'\n                });\n              };\n              abortController.signal.addEventListener('abort', abortHandler);\n            });\n            dispatch(pending(requestId, arg, options?.getPendingMeta?.({\n              requestId,\n              arg\n            }, {\n              getState,\n              extra\n            })) as any);\n            finalAction = await Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {\n              dispatch,\n              getState,\n              extra,\n              requestId,\n              signal: abortController.signal,\n              abort,\n              rejectWithValue: ((value: RejectedValue, meta?: RejectedMeta) => {\n                return new RejectWithValue(value, meta);\n              }) as any,\n              fulfillWithValue: ((value: unknown, meta?: FulfilledMeta) => {\n                return new FulfillWithMeta(value, meta);\n              }) as any\n            })).then(result => {\n              if (result instanceof RejectWithValue) {\n                throw result;\n              }\n              if (result instanceof FulfillWithMeta) {\n                return fulfilled(result.payload, requestId, arg, result.meta);\n              }\n              return fulfilled(result as any, requestId, arg);\n            })]);\n          } catch (err) {\n            finalAction = err instanceof RejectWithValue ? rejected(null, requestId, arg, err.payload, err.meta) : rejected(err as any, requestId, arg);\n          } finally {\n            if (abortHandler) {\n              abortController.signal.removeEventListener('abort', abortHandler);\n            }\n          }\n          // We dispatch the result action _after_ the catch, to avoid having any errors\n          // here get swallowed by the try/catch block,\n          // per https://twitter.com/dan_abramov/status/770914221638942720\n          // and https://github.com/reduxjs/redux-toolkit/blob/e85eb17b39a2118d859f7b7746e0f3fee523e089/docs/tutorials/advanced-tutorial.md#async-error-handling-logic-in-thunks\n\n          const skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && (finalAction as any).meta.condition;\n          if (!skipDispatch) {\n            dispatch(finalAction as any);\n          }\n          return finalAction;\n        }();\n        return Object.assign(promise as SafePromise<any>, {\n          abort,\n          requestId,\n          arg,\n          unwrap() {\n            return promise.then<any>(unwrapResult);\n          }\n        });\n      };\n    }\n    return Object.assign(actionCreator as AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig>, {\n      pending,\n      rejected,\n      fulfilled,\n      settled: isAnyOf(rejected, fulfilled),\n      typePrefix\n    });\n  }\n  createAsyncThunk.withTypes = () => createAsyncThunk;\n  return createAsyncThunk as CreateAsyncThunk<AsyncThunkConfig>;\n})();\ninterface UnwrappableAction {\n  payload: any;\n  meta?: any;\n  error?: any;\n}\ntype UnwrappedActionPayload<T extends UnwrappableAction> = Exclude<T, {\n  error: any;\n}>['payload'];\n\n/**\n * @public\n */\nexport function unwrapResult<R extends UnwrappableAction>(action: R): UnwrappedActionPayload<R> {\n  if (action.meta && action.meta.rejectedWithValue) {\n    throw action.payload;\n  }\n  if (action.error) {\n    throw action.error;\n  }\n  return action.payload;\n}\ntype WithStrictNullChecks<True, False> = undefined extends boolean ? False : True;\nfunction isThenable(value: any): value is PromiseLike<any> {\n  return value !== null && typeof value === 'object' && typeof value.then === 'function';\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3, formatProdErrorMessage as _formatProdErrorMessage4, formatProdErrorMessage as _formatProdErrorMessage5, formatProdErrorMessage as _formatProdErrorMessage6, formatProdErrorMessage as _formatProdErrorMessage7, formatProdErrorMessage as _formatProdErrorMessage8 } from \"@reduxjs/toolkit\";\nimport type { Action, Reducer, UnknownAction } from 'redux';\nimport type { Selector } from 'reselect';\nimport type { InjectConfig } from './combineSlices';\nimport type { ActionCreatorWithoutPayload, PayloadAction, PayloadActionCreator, PrepareAction, _ActionCreatorWithPreparedPayload } from './createAction';\nimport { createAction } from './createAction';\nimport type { AsyncThunk, AsyncThunkConfig, AsyncThunkOptions, AsyncThunkPayloadCreator, OverrideThunkApiConfigs } from './createAsyncThunk';\nimport { createAsyncThunk as _createAsyncThunk } from './createAsyncThunk';\nimport type { ActionMatcherDescriptionCollection, CaseReducer, ReducerWithInitialState } from './createReducer';\nimport { createReducer } from './createReducer';\nimport type { ActionReducerMapBuilder, TypedActionCreator } from './mapBuilders';\nimport { executeReducerBuilderCallback } from './mapBuilders';\nimport type { Id, TypeGuard } from './tsHelpers';\nimport { getOrInsertComputed } from './utils';\nconst asyncThunkSymbol = /* @__PURE__ */Symbol.for('rtk-slice-createasyncthunk');\n// type is annotated because it's too long to infer\nexport const asyncThunkCreator: {\n  [asyncThunkSymbol]: typeof _createAsyncThunk;\n} = {\n  [asyncThunkSymbol]: _createAsyncThunk\n};\ntype InjectIntoConfig<NewReducerPath extends string> = InjectConfig & {\n  reducerPath?: NewReducerPath;\n};\n\n/**\n * The return value of `createSlice`\n *\n * @public\n */\nexport interface Slice<State = any, CaseReducers extends SliceCaseReducers<State> = SliceCaseReducers<State>, Name extends string = string, ReducerPath extends string = Name, Selectors extends SliceSelectors<State> = SliceSelectors<State>> {\n  /**\n   * The slice name.\n   */\n  name: Name;\n\n  /**\n   *  The slice reducer path.\n   */\n  reducerPath: ReducerPath;\n\n  /**\n   * The slice's reducer.\n   */\n  reducer: Reducer<State>;\n\n  /**\n   * Action creators for the types of actions that are handled by the slice\n   * reducer.\n   */\n  actions: CaseReducerActions<CaseReducers, Name>;\n\n  /**\n   * The individual case reducer functions that were passed in the `reducers` parameter.\n   * This enables reuse and testing if they were defined inline when calling `createSlice`.\n   */\n  caseReducers: SliceDefinedCaseReducers<CaseReducers>;\n\n  /**\n   * Provides access to the initial state value given to the slice.\n   * If a lazy state initializer was provided, it will be called and a fresh value returned.\n   */\n  getInitialState: () => State;\n\n  /**\n   * Get localised slice selectors (expects to be called with *just* the slice's state as the first parameter)\n   */\n  getSelectors(): Id<SliceDefinedSelectors<State, Selectors, State>>;\n\n  /**\n   * Get globalised slice selectors (`selectState` callback is expected to receive first parameter and return slice state)\n   */\n  getSelectors<RootState>(selectState: (rootState: RootState) => State): Id<SliceDefinedSelectors<State, Selectors, RootState>>;\n\n  /**\n   * Selectors that assume the slice's state is `rootState[slice.reducerPath]` (which is usually the case)\n   *\n   * Equivalent to `slice.getSelectors((state: RootState) => state[slice.reducerPath])`.\n   */\n  get selectors(): Id<SliceDefinedSelectors<State, Selectors, { [K in ReducerPath]: State }>>;\n\n  /**\n   * Inject slice into provided reducer (return value from `combineSlices`), and return injected slice.\n   */\n  injectInto<NewReducerPath extends string = ReducerPath>(this: this, injectable: {\n    inject: (slice: {\n      reducerPath: string;\n      reducer: Reducer;\n    }, config?: InjectConfig) => void;\n  }, config?: InjectIntoConfig<NewReducerPath>): InjectedSlice<State, CaseReducers, Name, NewReducerPath, Selectors>;\n\n  /**\n   * Select the slice state, using the slice's current reducerPath.\n   *\n   * Will throw an error if slice is not found.\n   */\n  selectSlice(state: { [K in ReducerPath]: State }): State;\n}\n\n/**\n * A slice after being called with `injectInto(reducer)`.\n *\n * Selectors can now be called with an `undefined` value, in which case they use the slice's initial state.\n */\ntype InjectedSlice<State = any, CaseReducers extends SliceCaseReducers<State> = SliceCaseReducers<State>, Name extends string = string, ReducerPath extends string = Name, Selectors extends SliceSelectors<State> = SliceSelectors<State>> = Omit<Slice<State, CaseReducers, Name, ReducerPath, Selectors>, 'getSelectors' | 'selectors'> & {\n  /**\n   * Get localised slice selectors (expects to be called with *just* the slice's state as the first parameter)\n   */\n  getSelectors(): Id<SliceDefinedSelectors<State, Selectors, State | undefined>>;\n\n  /**\n   * Get globalised slice selectors (`selectState` callback is expected to receive first parameter and return slice state)\n   */\n  getSelectors<RootState>(selectState: (rootState: RootState) => State | undefined): Id<SliceDefinedSelectors<State, Selectors, RootState>>;\n\n  /**\n   * Selectors that assume the slice's state is `rootState[slice.name]` (which is usually the case)\n   *\n   * Equivalent to `slice.getSelectors((state: RootState) => state[slice.name])`.\n   */\n  get selectors(): Id<SliceDefinedSelectors<State, Selectors, { [K in ReducerPath]?: State | undefined }>>;\n\n  /**\n   * Select the slice state, using the slice's current reducerPath.\n   *\n   * Returns initial state if slice is not found.\n   */\n  selectSlice(state: { [K in ReducerPath]?: State | undefined }): State;\n};\n\n/**\n * Options for `createSlice()`.\n *\n * @public\n */\nexport interface CreateSliceOptions<State = any, CR extends SliceCaseReducers<State> = SliceCaseReducers<State>, Name extends string = string, ReducerPath extends string = Name, Selectors extends SliceSelectors<State> = SliceSelectors<State>> {\n  /**\n   * The slice's name. Used to namespace the generated action types.\n   */\n  name: Name;\n\n  /**\n   * The slice's reducer path. Used when injecting into a combined slice reducer.\n   */\n  reducerPath?: ReducerPath;\n\n  /**\n   * The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\n   */\n  initialState: State | (() => State);\n\n  /**\n   * A mapping from action types to action-type-specific *case reducer*\n   * functions. For every action type, a matching action creator will be\n   * generated using `createAction()`.\n   */\n  reducers: ValidateSliceCaseReducers<State, CR> | ((creators: ReducerCreators<State>) => CR);\n\n  /**\n   * A callback that receives a *builder* object to define\n   * case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\n   *\n   *\n   * @example\n  ```ts\n  import { createAction, createSlice, Action } from '@reduxjs/toolkit'\n  const incrementBy = createAction<number>('incrementBy')\n  const decrement = createAction('decrement')\n  interface RejectedAction extends Action {\n  error: Error\n  }\n  function isRejectedAction(action: Action): action is RejectedAction {\n  return action.type.endsWith('rejected')\n  }\n  createSlice({\n  name: 'counter',\n  initialState: 0,\n  reducers: {},\n  extraReducers: builder => {\n    builder\n      .addCase(incrementBy, (state, action) => {\n        // action is inferred correctly here if using TS\n      })\n      // You can chain calls, or have separate `builder.addCase()` lines each time\n      .addCase(decrement, (state, action) => {})\n      // You can match a range of action types\n      .addMatcher(\n        isRejectedAction,\n        // `action` will be inferred as a RejectedAction due to isRejectedAction being defined as a type guard\n        (state, action) => {}\n      )\n      // and provide a default case if no other handlers matched\n      .addDefaultCase((state, action) => {})\n    }\n  })\n  ```\n   */\n  extraReducers?: (builder: ActionReducerMapBuilder<State>) => void;\n\n  /**\n   * A map of selectors that receive the slice's state and any additional arguments, and return a result.\n   */\n  selectors?: Selectors;\n}\nexport enum ReducerType {\n  reducer = 'reducer',\n  reducerWithPrepare = 'reducerWithPrepare',\n  asyncThunk = 'asyncThunk',\n}\ntype ReducerDefinition<T extends ReducerType = ReducerType> = {\n  _reducerDefinitionType: T;\n};\nexport type CaseReducerDefinition<S = any, A extends Action = UnknownAction> = CaseReducer<S, A> & ReducerDefinition<ReducerType.reducer>;\n\n/**\n * A CaseReducer with a `prepare` method.\n *\n * @public\n */\nexport type CaseReducerWithPrepare<State, Action extends PayloadAction> = {\n  reducer: CaseReducer<State, Action>;\n  prepare: PrepareAction<Action['payload']>;\n};\nexport interface CaseReducerWithPrepareDefinition<State, Action extends PayloadAction> extends CaseReducerWithPrepare<State, Action>, ReducerDefinition<ReducerType.reducerWithPrepare> {}\ntype AsyncThunkSliceReducerConfig<State, ThunkArg extends any, Returned = unknown, ThunkApiConfig extends AsyncThunkConfig = {}> = {\n  pending?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['pending']>>;\n  rejected?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['rejected']>>;\n  fulfilled?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['fulfilled']>>;\n  settled?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['rejected' | 'fulfilled']>>;\n  options?: AsyncThunkOptions<ThunkArg, ThunkApiConfig>;\n};\ntype AsyncThunkSliceReducerDefinition<State, ThunkArg extends any, Returned = unknown, ThunkApiConfig extends AsyncThunkConfig = {}> = AsyncThunkSliceReducerConfig<State, ThunkArg, Returned, ThunkApiConfig> & ReducerDefinition<ReducerType.asyncThunk> & {\n  payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, ThunkApiConfig>;\n};\n\n/**\n * Providing these as part of the config would cause circular types, so we disallow passing them\n */\ntype PreventCircular<ThunkApiConfig> = { [K in keyof ThunkApiConfig]: K extends 'state' | 'dispatch' ? never : ThunkApiConfig[K] };\ninterface AsyncThunkCreator<State, CurriedThunkApiConfig extends PreventCircular<AsyncThunkConfig> = PreventCircular<AsyncThunkConfig>> {\n  <Returned, ThunkArg = void>(payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, CurriedThunkApiConfig>, config?: AsyncThunkSliceReducerConfig<State, ThunkArg, Returned, CurriedThunkApiConfig>): AsyncThunkSliceReducerDefinition<State, ThunkArg, Returned, CurriedThunkApiConfig>;\n  <Returned, ThunkArg, ThunkApiConfig extends PreventCircular<AsyncThunkConfig> = {}>(payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, ThunkApiConfig>, config?: AsyncThunkSliceReducerConfig<State, ThunkArg, Returned, ThunkApiConfig>): AsyncThunkSliceReducerDefinition<State, ThunkArg, Returned, ThunkApiConfig>;\n  withTypes<ThunkApiConfig extends PreventCircular<AsyncThunkConfig>>(): AsyncThunkCreator<State, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>;\n}\nexport interface ReducerCreators<State> {\n  reducer(caseReducer: CaseReducer<State, PayloadAction>): CaseReducerDefinition<State, PayloadAction>;\n  reducer<Payload>(caseReducer: CaseReducer<State, PayloadAction<Payload>>): CaseReducerDefinition<State, PayloadAction<Payload>>;\n  asyncThunk: AsyncThunkCreator<State>;\n  preparedReducer<Prepare extends PrepareAction<any>>(prepare: Prepare, reducer: CaseReducer<State, ReturnType<_ActionCreatorWithPreparedPayload<Prepare>>>): {\n    _reducerDefinitionType: ReducerType.reducerWithPrepare;\n    prepare: Prepare;\n    reducer: CaseReducer<State, ReturnType<_ActionCreatorWithPreparedPayload<Prepare>>>;\n  };\n}\n\n/**\n * The type describing a slice's `reducers` option.\n *\n * @public\n */\nexport type SliceCaseReducers<State> = Record<string, ReducerDefinition> | Record<string, CaseReducer<State, PayloadAction<any>> | CaseReducerWithPrepare<State, PayloadAction<any, string, any, any>>>;\n\n/**\n * The type describing a slice's `selectors` option.\n */\nexport type SliceSelectors<State> = {\n  [K: string]: (sliceState: State, ...args: any[]) => any;\n};\ntype SliceActionType<SliceName extends string, ActionName extends keyof any> = ActionName extends string | number ? `${SliceName}/${ActionName}` : string;\n\n/**\n * Derives the slice's `actions` property from the `reducers` options\n *\n * @public\n */\nexport type CaseReducerActions<CaseReducers extends SliceCaseReducers<any>, SliceName extends string> = { [Type in keyof CaseReducers]: CaseReducers[Type] extends infer Definition ? Definition extends {\n  prepare: any;\n} ? ActionCreatorForCaseReducerWithPrepare<Definition, SliceActionType<SliceName, Type>> : Definition extends AsyncThunkSliceReducerDefinition<any, infer ThunkArg, infer Returned, infer ThunkApiConfig> ? AsyncThunk<Returned, ThunkArg, ThunkApiConfig> : Definition extends {\n  reducer: any;\n} ? ActionCreatorForCaseReducer<Definition['reducer'], SliceActionType<SliceName, Type>> : ActionCreatorForCaseReducer<Definition, SliceActionType<SliceName, Type>> : never };\n\n/**\n * Get a `PayloadActionCreator` type for a passed `CaseReducerWithPrepare`\n *\n * @internal\n */\ntype ActionCreatorForCaseReducerWithPrepare<CR extends {\n  prepare: any;\n}, Type extends string> = _ActionCreatorWithPreparedPayload<CR['prepare'], Type>;\n\n/**\n * Get a `PayloadActionCreator` type for a passed `CaseReducer`\n *\n * @internal\n */\ntype ActionCreatorForCaseReducer<CR, Type extends string> = CR extends ((state: any, action: infer Action) => any) ? Action extends {\n  payload: infer P;\n} ? PayloadActionCreator<P, Type> : ActionCreatorWithoutPayload<Type> : ActionCreatorWithoutPayload<Type>;\n\n/**\n * Extracts the CaseReducers out of a `reducers` object, even if they are\n * tested into a `CaseReducerWithPrepare`.\n *\n * @internal\n */\ntype SliceDefinedCaseReducers<CaseReducers extends SliceCaseReducers<any>> = { [Type in keyof CaseReducers]: CaseReducers[Type] extends infer Definition ? Definition extends AsyncThunkSliceReducerDefinition<any, any, any> ? Id<Pick<Required<Definition>, 'fulfilled' | 'rejected' | 'pending' | 'settled'>> : Definition extends {\n  reducer: infer Reducer;\n} ? Reducer : Definition : never };\ntype RemappedSelector<S extends Selector, NewState> = S extends Selector<any, infer R, infer P> ? Selector<NewState, R, P> & {\n  unwrapped: S;\n} : never;\n\n/**\n * Extracts the final selector type from the `selectors` object.\n *\n * Removes the `string` index signature from the default value.\n */\ntype SliceDefinedSelectors<State, Selectors extends SliceSelectors<State>, RootState> = { [K in keyof Selectors as string extends K ? never : K]: RemappedSelector<Selectors[K], RootState> };\n\n/**\n * Used on a SliceCaseReducers object.\n * Ensures that if a CaseReducer is a `CaseReducerWithPrepare`, that\n * the `reducer` and the `prepare` function use the same type of `payload`.\n *\n * Might do additional such checks in the future.\n *\n * This type is only ever useful if you want to write your own wrapper around\n * `createSlice`. Please don't use it otherwise!\n *\n * @public\n */\nexport type ValidateSliceCaseReducers<S, ACR extends SliceCaseReducers<S>> = ACR & { [T in keyof ACR]: ACR[T] extends {\n  reducer(s: S, action?: infer A): any;\n} ? {\n  prepare(...a: never[]): Omit<A, 'type'>;\n} : {} };\nfunction getType(slice: string, actionKey: string): string {\n  return `${slice}/${actionKey}`;\n}\ninterface BuildCreateSliceConfig {\n  creators?: {\n    asyncThunk?: typeof asyncThunkCreator;\n  };\n}\nexport function buildCreateSlice({\n  creators\n}: BuildCreateSliceConfig = {}) {\n  const cAT = creators?.asyncThunk?.[asyncThunkSymbol];\n  return function createSlice<State, CaseReducers extends SliceCaseReducers<State>, Name extends string, Selectors extends SliceSelectors<State>, ReducerPath extends string = Name>(options: CreateSliceOptions<State, CaseReducers, Name, ReducerPath, Selectors>): Slice<State, CaseReducers, Name, ReducerPath, Selectors> {\n    const {\n      name,\n      reducerPath = name as unknown as ReducerPath\n    } = options;\n    if (!name) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(11) : '`name` is a required option for createSlice');\n    }\n    if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n      if (options.initialState === undefined) {\n        console.error('You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`');\n      }\n    }\n    const reducers = (typeof options.reducers === 'function' ? options.reducers(buildReducerCreators<State>()) : options.reducers) || {};\n    const reducerNames = Object.keys(reducers);\n    const context: ReducerHandlingContext<State> = {\n      sliceCaseReducersByName: {},\n      sliceCaseReducersByType: {},\n      actionCreators: {},\n      sliceMatchers: []\n    };\n    const contextMethods: ReducerHandlingContextMethods<State> = {\n      addCase(typeOrActionCreator: string | TypedActionCreator<any>, reducer: CaseReducer<State>) {\n        const type = typeof typeOrActionCreator === 'string' ? typeOrActionCreator : typeOrActionCreator.type;\n        if (!type) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(12) : '`context.addCase` cannot be called with an empty action type');\n        }\n        if (type in context.sliceCaseReducersByType) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(13) : '`context.addCase` cannot be called with two reducers for the same action type: ' + type);\n        }\n        context.sliceCaseReducersByType[type] = reducer;\n        return contextMethods;\n      },\n      addMatcher(matcher, reducer) {\n        context.sliceMatchers.push({\n          matcher,\n          reducer\n        });\n        return contextMethods;\n      },\n      exposeAction(name, actionCreator) {\n        context.actionCreators[name] = actionCreator;\n        return contextMethods;\n      },\n      exposeCaseReducer(name, reducer) {\n        context.sliceCaseReducersByName[name] = reducer;\n        return contextMethods;\n      }\n    };\n    reducerNames.forEach(reducerName => {\n      const reducerDefinition = reducers[reducerName];\n      const reducerDetails: ReducerDetails = {\n        reducerName,\n        type: getType(name, reducerName),\n        createNotation: typeof options.reducers === 'function'\n      };\n      if (isAsyncThunkSliceReducerDefinition<State>(reducerDefinition)) {\n        handleThunkCaseReducerDefinition(reducerDetails, reducerDefinition, contextMethods, cAT);\n      } else {\n        handleNormalReducerDefinition<State>(reducerDetails, reducerDefinition as any, contextMethods);\n      }\n    });\n    function buildReducer() {\n      if (process.env.NODE_ENV !== 'production') {\n        if (typeof options.extraReducers === 'object') {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(14) : \"The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\n        }\n      }\n      const [extraReducers = {}, actionMatchers = [], defaultCaseReducer = undefined] = typeof options.extraReducers === 'function' ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers];\n      const finalCaseReducers = {\n        ...extraReducers,\n        ...context.sliceCaseReducersByType\n      };\n      return createReducer(options.initialState, builder => {\n        for (let key in finalCaseReducers) {\n          builder.addCase(key, finalCaseReducers[key] as CaseReducer<any>);\n        }\n        for (let sM of context.sliceMatchers) {\n          builder.addMatcher(sM.matcher, sM.reducer);\n        }\n        for (let m of actionMatchers) {\n          builder.addMatcher(m.matcher, m.reducer);\n        }\n        if (defaultCaseReducer) {\n          builder.addDefaultCase(defaultCaseReducer);\n        }\n      });\n    }\n    const selectSelf = (state: State) => state;\n    const injectedSelectorCache = new Map<boolean, WeakMap<(rootState: any) => State | undefined, Record<string, (rootState: any) => any>>>();\n    const injectedStateCache = new WeakMap<(rootState: any) => State, State>();\n    let _reducer: ReducerWithInitialState<State>;\n    function reducer(state: State | undefined, action: UnknownAction) {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer(state, action);\n    }\n    function getInitialState() {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer.getInitialState();\n    }\n    function makeSelectorProps<CurrentReducerPath extends string = ReducerPath>(reducerPath: CurrentReducerPath, injected = false): Pick<Slice<State, CaseReducers, Name, CurrentReducerPath, Selectors>, 'getSelectors' | 'selectors' | 'selectSlice' | 'reducerPath'> {\n      function selectSlice(state: { [K in CurrentReducerPath]: State }) {\n        let sliceState = state[reducerPath];\n        if (typeof sliceState === 'undefined') {\n          if (injected) {\n            sliceState = getOrInsertComputed(injectedStateCache, selectSlice, getInitialState);\n          } else if (process.env.NODE_ENV !== 'production') {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(15) : 'selectSlice returned undefined for an uninjected slice reducer');\n          }\n        }\n        return sliceState;\n      }\n      function getSelectors(selectState: (rootState: any) => State = selectSelf) {\n        const selectorCache = getOrInsertComputed(injectedSelectorCache, injected, () => new WeakMap());\n        return getOrInsertComputed(selectorCache, selectState, () => {\n          const map: Record<string, Selector<any, any>> = {};\n          for (const [name, selector] of Object.entries(options.selectors ?? {})) {\n            map[name] = wrapSelector(selector, selectState, () => getOrInsertComputed(injectedStateCache, selectState, getInitialState), injected);\n          }\n          return map;\n        }) as any;\n      }\n      return {\n        reducerPath,\n        getSelectors,\n        get selectors() {\n          return getSelectors(selectSlice);\n        },\n        selectSlice\n      };\n    }\n    const slice: Slice<State, CaseReducers, Name, ReducerPath, Selectors> = {\n      name,\n      reducer,\n      actions: context.actionCreators as any,\n      caseReducers: context.sliceCaseReducersByName as any,\n      getInitialState,\n      ...makeSelectorProps(reducerPath),\n      injectInto(injectable, {\n        reducerPath: pathOpt,\n        ...config\n      } = {}) {\n        const newReducerPath = pathOpt ?? reducerPath;\n        injectable.inject({\n          reducerPath: newReducerPath,\n          reducer\n        }, config);\n        return {\n          ...slice,\n          ...makeSelectorProps(newReducerPath, true)\n        } as any;\n      }\n    };\n    return slice;\n  };\n}\nfunction wrapSelector<State, NewState, S extends Selector<State>>(selector: S, selectState: Selector<NewState, State>, getInitialState: () => State, injected?: boolean) {\n  function wrapper(rootState: NewState, ...args: any[]) {\n    let sliceState = selectState(rootState);\n    if (typeof sliceState === 'undefined') {\n      if (injected) {\n        sliceState = getInitialState();\n      } else if (process.env.NODE_ENV !== 'production') {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(16) : 'selectState returned undefined for an uninjected slice reducer');\n      }\n    }\n    return selector(sliceState, ...args);\n  }\n  wrapper.unwrapped = selector;\n  return wrapper as RemappedSelector<S, NewState>;\n}\n\n/**\n * A function that accepts an initial state, an object full of reducer\n * functions, and a \"slice name\", and automatically generates\n * action creators and action types that correspond to the\n * reducers and state.\n *\n * @public\n */\nexport const createSlice = /* @__PURE__ */buildCreateSlice();\ninterface ReducerHandlingContext<State> {\n  sliceCaseReducersByName: Record<string, CaseReducer<State, any> | Pick<AsyncThunkSliceReducerDefinition<State, any, any, any>, 'fulfilled' | 'rejected' | 'pending' | 'settled'>>;\n  sliceCaseReducersByType: Record<string, CaseReducer<State, any>>;\n  sliceMatchers: ActionMatcherDescriptionCollection<State>;\n  actionCreators: Record<string, Function>;\n}\ninterface ReducerHandlingContextMethods<State> {\n  /**\n   * Adds a case reducer to handle a single action type.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<ActionCreator extends TypedActionCreator<string>>(actionCreator: ActionCreator, reducer: CaseReducer<State, ReturnType<ActionCreator>>): ReducerHandlingContextMethods<State>;\n  /**\n   * Adds a case reducer to handle a single action type.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<Type extends string, A extends Action<Type>>(type: Type, reducer: CaseReducer<State, A>): ReducerHandlingContextMethods<State>;\n\n  /**\n   * Allows you to match incoming actions against your own filter function instead of only the `action.type` property.\n   * @remarks\n   * If multiple matcher reducers match, all of them will be executed in the order\n   * they were defined in - even if a case reducer already matched.\n   * All calls to `builder.addMatcher` must come after any calls to `builder.addCase` and before any calls to `builder.addDefaultCase`.\n   * @param matcher - A matcher function. In TypeScript, this should be a [type predicate](https://www.typescriptlang.org/docs/handbook/2/narrowing.html#using-type-predicates)\n   *   function\n   * @param reducer - The actual case reducer function.\n   *\n   */\n  addMatcher<A>(matcher: TypeGuard<A>, reducer: CaseReducer<State, A extends Action ? A : A & Action>): ReducerHandlingContextMethods<State>;\n  /**\n   * Add an action to be exposed under the final `slice.actions` key.\n   * @param name The key to be exposed as.\n   * @param actionCreator The action to expose.\n   * @example\n   * context.exposeAction(\"addPost\", createAction<Post>(\"addPost\"));\n   *\n   * export const { addPost } = slice.actions\n   *\n   * dispatch(addPost(post))\n   */\n  exposeAction(name: string, actionCreator: Function): ReducerHandlingContextMethods<State>;\n  /**\n   * Add a case reducer to be exposed under the final `slice.caseReducers` key.\n   * @param name The key to be exposed as.\n   * @param reducer The reducer to expose.\n   * @example\n   * context.exposeCaseReducer(\"addPost\", (state, action: PayloadAction<Post>) => {\n   *   state.push(action.payload)\n   * })\n   *\n   * slice.caseReducers.addPost([], addPost(post))\n   */\n  exposeCaseReducer(name: string, reducer: CaseReducer<State, any> | Pick<AsyncThunkSliceReducerDefinition<State, any, any, any>, 'fulfilled' | 'rejected' | 'pending' | 'settled'>): ReducerHandlingContextMethods<State>;\n}\ninterface ReducerDetails {\n  /** The key the reducer was defined under */\n  reducerName: string;\n  /** The predefined action type, i.e. `${slice.name}/${reducerName}` */\n  type: string;\n  /** Whether create. notation was used when defining reducers */\n  createNotation: boolean;\n}\nfunction buildReducerCreators<State>(): ReducerCreators<State> {\n  function asyncThunk(payloadCreator: AsyncThunkPayloadCreator<any, any>, config: AsyncThunkSliceReducerConfig<State, any>): AsyncThunkSliceReducerDefinition<State, any> {\n    return {\n      _reducerDefinitionType: ReducerType.asyncThunk,\n      payloadCreator,\n      ...config\n    };\n  }\n  asyncThunk.withTypes = () => asyncThunk;\n  return {\n    reducer(caseReducer: CaseReducer<State, any>) {\n      return Object.assign({\n        // hack so the wrapping function has the same name as the original\n        // we need to create a wrapper so the `reducerDefinitionType` is not assigned to the original\n        [caseReducer.name](...args: Parameters<typeof caseReducer>) {\n          return caseReducer(...args);\n        }\n      }[caseReducer.name], {\n        _reducerDefinitionType: ReducerType.reducer\n      } as const);\n    },\n    preparedReducer(prepare, reducer) {\n      return {\n        _reducerDefinitionType: ReducerType.reducerWithPrepare,\n        prepare,\n        reducer\n      };\n    },\n    asyncThunk: asyncThunk as any\n  };\n}\nfunction handleNormalReducerDefinition<State>({\n  type,\n  reducerName,\n  createNotation\n}: ReducerDetails, maybeReducerWithPrepare: CaseReducer<State, {\n  payload: any;\n  type: string;\n}> | CaseReducerWithPrepare<State, PayloadAction<any, string, any, any>>, context: ReducerHandlingContextMethods<State>) {\n  let caseReducer: CaseReducer<State, any>;\n  let prepareCallback: PrepareAction<any> | undefined;\n  if ('reducer' in maybeReducerWithPrepare) {\n    if (createNotation && !isCaseReducerWithPrepareDefinition(maybeReducerWithPrepare)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage7(17) : 'Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.');\n    }\n    caseReducer = maybeReducerWithPrepare.reducer;\n    prepareCallback = maybeReducerWithPrepare.prepare;\n  } else {\n    caseReducer = maybeReducerWithPrepare;\n  }\n  context.addCase(type, caseReducer).exposeCaseReducer(reducerName, caseReducer).exposeAction(reducerName, prepareCallback ? createAction(type, prepareCallback) : createAction(type));\n}\nfunction isAsyncThunkSliceReducerDefinition<State>(reducerDefinition: any): reducerDefinition is AsyncThunkSliceReducerDefinition<State, any, any, any> {\n  return reducerDefinition._reducerDefinitionType === ReducerType.asyncThunk;\n}\nfunction isCaseReducerWithPrepareDefinition<State>(reducerDefinition: any): reducerDefinition is CaseReducerWithPrepareDefinition<State, any> {\n  return reducerDefinition._reducerDefinitionType === ReducerType.reducerWithPrepare;\n}\nfunction handleThunkCaseReducerDefinition<State>({\n  type,\n  reducerName\n}: ReducerDetails, reducerDefinition: AsyncThunkSliceReducerDefinition<State, any, any, any>, context: ReducerHandlingContextMethods<State>, cAT: typeof _createAsyncThunk | undefined) {\n  if (!cAT) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage8(18) : 'Cannot use `create.asyncThunk` in the built-in `createSlice`. ' + 'Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.');\n  }\n  const {\n    payloadCreator,\n    fulfilled,\n    pending,\n    rejected,\n    settled,\n    options\n  } = reducerDefinition;\n  const thunk = cAT(type, payloadCreator, options as any);\n  context.exposeAction(reducerName, thunk);\n  if (fulfilled) {\n    context.addCase(thunk.fulfilled, fulfilled);\n  }\n  if (pending) {\n    context.addCase(thunk.pending, pending);\n  }\n  if (rejected) {\n    context.addCase(thunk.rejected, rejected);\n  }\n  if (settled) {\n    context.addMatcher(thunk.settled, settled);\n  }\n  context.exposeCaseReducer(reducerName, {\n    fulfilled: fulfilled || noop,\n    pending: pending || noop,\n    rejected: rejected || noop,\n    settled: settled || noop\n  });\n}\nfunction noop() {}", "import type { EntityId, EntityState, EntityStateAdapter, EntityStateFactory } from './models';\nexport function getInitialEntityState<T, Id extends EntityId>(): EntityState<T, Id> {\n  return {\n    ids: [],\n    entities: {} as Record<Id, T>\n  };\n}\nexport function createInitialStateFactory<T, Id extends EntityId>(stateAdapter: EntityStateAdapter<T, Id>): EntityStateFactory<T, Id> {\n  function getInitialState(state?: undefined, entities?: readonly T[] | Record<Id, T>): EntityState<T, Id>;\n  function getInitialState<S extends object>(additionalState: S, entities?: readonly T[] | Record<Id, T>): EntityState<T, Id> & S;\n  function getInitialState(additionalState: any = {}, entities?: readonly T[] | Record<Id, T>): any {\n    const state = Object.assign(getInitialEntityState(), additionalState);\n    return entities ? stateAdapter.setAll(state, entities) : state;\n  }\n  return {\n    getInitialState\n  };\n}", "import type { CreateSelectorFunction, Selector } from 'reselect';\nimport { createDraftSafeSelector } from '../createDraftSafeSelector';\nimport type { EntityId, EntitySelectors, EntityState } from './models';\ntype AnyFunction = (...args: any) => any;\ntype AnyCreateSelectorFunction = CreateSelectorFunction<<F extends AnyFunction>(f: F) => F, <F extends AnyFunction>(f: F) => F>;\nexport type GetSelectorsOptions = {\n  createSelector?: AnyCreateSelectorFunction;\n};\nexport function createSelectorsFactory<T, Id extends EntityId>() {\n  function getSelectors(selectState?: undefined, options?: GetSelectorsOptions): EntitySelectors<T, EntityState<T, Id>, Id>;\n  function getSelectors<V>(selectState: (state: V) => EntityState<T, Id>, options?: GetSelectorsOptions): EntitySelectors<T, V, Id>;\n  function getSelectors<V>(selectState?: (state: V) => EntityState<T, Id>, options: GetSelectorsOptions = {}): EntitySelectors<T, any, Id> {\n    const {\n      createSelector = createDraftSafeSelector as AnyCreateSelectorFunction\n    } = options;\n    const selectIds = (state: EntityState<T, Id>) => state.ids;\n    const selectEntities = (state: EntityState<T, Id>) => state.entities;\n    const selectAll = createSelector(selectIds, selectEntities, (ids, entities): T[] => ids.map(id => entities[id]!));\n    const selectId = (_: unknown, id: Id) => id;\n    const selectById = (entities: Record<Id, T>, id: Id) => entities[id];\n    const selectTotal = createSelector(selectIds, ids => ids.length);\n    if (!selectState) {\n      return {\n        selectIds,\n        selectEntities,\n        selectAll,\n        selectTotal,\n        selectById: createSelector(selectEntities, selectId, selectById)\n      };\n    }\n    const selectGlobalizedEntities = createSelector(selectState as Selector<V, EntityState<T, Id>>, selectEntities);\n    return {\n      selectIds: createSelector(selectState, selectIds),\n      selectEntities: selectGlobalizedEntities,\n      selectAll: createSelector(selectState, selectAll),\n      selectTotal: createSelector(selectState, selectTotal),\n      selectById: createSelector(selectGlobalizedEntities, selectId, selectById)\n    };\n  }\n  return {\n    getSelectors\n  };\n}", "import { produce as createNextState, isDraft } from 'immer';\nimport type { Draft } from 'immer';\nimport type { EntityId, DraftableEntityState, PreventAny } from './models';\nimport type { PayloadAction } from '../createAction';\nimport { isFSA } from '../createAction';\nexport const isDraftTyped = isDraft as <T>(value: T | Draft<T>) => value is Draft<T>;\nexport function createSingleArgumentStateOperator<T, Id extends EntityId>(mutator: (state: DraftableEntityState<T, Id>) => void) {\n  const operator = createStateOperator((_: undefined, state: DraftableEntityState<T, Id>) => mutator(state));\n  return function operation<S extends DraftableEntityState<T, Id>>(state: PreventAny<S, T, Id>): S {\n    return operator(state as S, undefined);\n  };\n}\nexport function createStateOperator<T, Id extends EntityId, R>(mutator: (arg: R, state: DraftableEntityState<T, Id>) => void) {\n  return function operation<S extends DraftableEntityState<T, Id>>(state: S, arg: R | PayloadAction<R>): S {\n    function isPayloadActionArgument(arg: R | PayloadAction<R>): arg is PayloadAction<R> {\n      return isFSA(arg);\n    }\n    const runMutator = (draft: DraftableEntityState<T, Id>) => {\n      if (isPayloadActionArgument(arg)) {\n        mutator(arg.payload, draft);\n      } else {\n        mutator(arg, draft);\n      }\n    };\n    if (isDraftTyped<DraftableEntityState<T, Id>>(state)) {\n      // we must already be inside a `createNextState` call, likely because\n      // this is being wrapped in `createReducer` or `createSlice`.\n      // It's safe to just pass the draft to the mutator.\n      runMutator(state);\n\n      // since it's a draft, we'll just return it\n      return state;\n    }\n    return createNextState(state, runMutator);\n  };\n}", "import type { Draft } from 'immer';\nimport { current, isDraft } from 'immer';\nimport type { DraftableEntityState, EntityId, IdSelector, Update } from './models';\nexport function selectIdValue<T, Id extends EntityId>(entity: T, selectId: IdSelector<T, Id>) {\n  const key = selectId(entity);\n  if (process.env.NODE_ENV !== 'production' && key === undefined) {\n    console.warn('The entity passed to the `selectId` implementation returned undefined.', 'You should probably provide your own `selectId` implementation.', 'The entity that was passed:', entity, 'The `selectId` implementation:', selectId.toString());\n  }\n  return key;\n}\nexport function ensureEntitiesArray<T, Id extends EntityId>(entities: readonly T[] | Record<Id, T>): readonly T[] {\n  if (!Array.isArray(entities)) {\n    entities = Object.values(entities);\n  }\n  return entities;\n}\nexport function getCurrent<T>(value: T | Draft<T>): T {\n  return (isDraft(value) ? current(value) : value) as T;\n}\nexport function splitAddedUpdatedEntities<T, Id extends EntityId>(newEntities: readonly T[] | Record<Id, T>, selectId: IdSelector<T, Id>, state: DraftableEntityState<T, Id>): [T[], Update<T, Id>[], Id[]] {\n  newEntities = ensureEntitiesArray(newEntities);\n  const existingIdsArray = getCurrent(state.ids);\n  const existingIds = new Set<Id>(existingIdsArray);\n  const added: T[] = [];\n  const addedIds = new Set<Id>([]);\n  const updated: Update<T, Id>[] = [];\n  for (const entity of newEntities) {\n    const id = selectIdValue(entity, selectId);\n    if (existingIds.has(id) || addedIds.has(id)) {\n      updated.push({\n        id,\n        changes: entity\n      });\n    } else {\n      addedIds.add(id);\n      added.push(entity);\n    }\n  }\n  return [added, updated, existingIdsArray];\n}", "import type { Draft } from 'immer';\nimport type { EntityStateAdapter, IdSelector, Update, EntityId, DraftableEntityState } from './models';\nimport { createStateOperator, createSingleArgumentStateOperator } from './state_adapter';\nimport { selectIdValue, ensureEntitiesArray, splitAddedUpdatedEntities } from './utils';\nexport function createUnsortedStateAdapter<T, Id extends EntityId>(selectId: IdSelector<T, Id>): EntityStateAdapter<T, Id> {\n  type R = DraftableEntityState<T, Id>;\n  function addOneMutably(entity: T, state: R): void {\n    const key = selectIdValue(entity, selectId);\n    if (key in state.entities) {\n      return;\n    }\n    state.ids.push(key as Id & Draft<Id>);\n    (state.entities as Record<Id, T>)[key] = entity;\n  }\n  function addManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      addOneMutably(entity, state);\n    }\n  }\n  function setOneMutably(entity: T, state: R): void {\n    const key = selectIdValue(entity, selectId);\n    if (!(key in state.entities)) {\n      state.ids.push(key as Id & Draft<Id>);\n    }\n    ;\n    (state.entities as Record<Id, T>)[key] = entity;\n  }\n  function setManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      setOneMutably(entity, state);\n    }\n  }\n  function setAllMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.ids = [];\n    state.entities = {} as Record<Id, T>;\n    addManyMutably(newEntities, state);\n  }\n  function removeOneMutably(key: Id, state: R): void {\n    return removeManyMutably([key], state);\n  }\n  function removeManyMutably(keys: readonly Id[], state: R): void {\n    let didMutate = false;\n    keys.forEach(key => {\n      if (key in state.entities) {\n        delete (state.entities as Record<Id, T>)[key];\n        didMutate = true;\n      }\n    });\n    if (didMutate) {\n      state.ids = (state.ids as Id[]).filter(id => id in state.entities) as Id[] | Draft<Id[]>;\n    }\n  }\n  function removeAllMutably(state: R): void {\n    Object.assign(state, {\n      ids: [],\n      entities: {}\n    });\n  }\n  function takeNewKey(keys: {\n    [id: string]: Id;\n  }, update: Update<T, Id>, state: R): boolean {\n    const original: T | undefined = (state.entities as Record<Id, T>)[update.id];\n    if (original === undefined) {\n      return false;\n    }\n    const updated: T = Object.assign({}, original, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    const hasNewKey = newKey !== update.id;\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete (state.entities as Record<Id, T>)[update.id];\n    }\n    ;\n    (state.entities as Record<Id, T>)[newKey] = updated;\n    return hasNewKey;\n  }\n  function updateOneMutably(update: Update<T, Id>, state: R): void {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates: ReadonlyArray<Update<T, Id>>, state: R): void {\n    const newKeys: {\n      [id: string]: Id;\n    } = {};\n    const updatesPerEntity: {\n      [id: string]: Update<T, Id>;\n    } = {};\n    updates.forEach(update => {\n      // Only apply updates to entities that currently exist\n      if (update.id in state.entities) {\n        // If there are multiple updates to one entity, merge them together\n        updatesPerEntity[update.id] = {\n          id: update.id,\n          // Spreads ignore falsy values, so this works even if there isn't\n          // an existing update already at this key\n          changes: {\n            ...updatesPerEntity[update.id]?.changes,\n            ...update.changes\n          }\n        };\n      }\n    });\n    updates = Object.values(updatesPerEntity);\n    const didMutateEntities = updates.length > 0;\n    if (didMutateEntities) {\n      const didMutateIds = updates.filter(update => takeNewKey(newKeys, update, state)).length > 0;\n      if (didMutateIds) {\n        state.ids = Object.values(state.entities).map(e => selectIdValue(e as T, selectId));\n      }\n    }\n  }\n  function upsertOneMutably(entity: T, state: R): void {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    const [added, updated] = splitAddedUpdatedEntities<T, Id>(newEntities, selectId, state);\n    addManyMutably(added, state);\n    updateManyMutably(updated, state);\n  }\n  return {\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably)\n  };\n}", "import type { IdSelector, Comparer, EntityStateAdapter, Update, EntityId, DraftableEntityState } from './models';\nimport { createStateOperator } from './state_adapter';\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter';\nimport { selectIdValue, ensureEntitiesArray, splitAddedUpdatedEntities, getCurrent } from './utils';\n\n// Borrowed from Replay\nexport function findInsertIndex<T>(sortedItems: T[], item: T, comparisonFunction: Comparer<T>): number {\n  let lowIndex = 0;\n  let highIndex = sortedItems.length;\n  while (lowIndex < highIndex) {\n    let middleIndex = lowIndex + highIndex >>> 1;\n    const currentItem = sortedItems[middleIndex];\n    const res = comparisonFunction(item, currentItem);\n    if (res >= 0) {\n      lowIndex = middleIndex + 1;\n    } else {\n      highIndex = middleIndex;\n    }\n  }\n  return lowIndex;\n}\nexport function insert<T>(sortedItems: T[], item: T, comparisonFunction: Comparer<T>): T[] {\n  const insertAtIndex = findInsertIndex(sortedItems, item, comparisonFunction);\n  sortedItems.splice(insertAtIndex, 0, item);\n  return sortedItems;\n}\nexport function createSortedStateAdapter<T, Id extends EntityId>(selectId: IdSelector<T, Id>, comparer: Comparer<T>): EntityStateAdapter<T, Id> {\n  type R = DraftableEntityState<T, Id>;\n  const {\n    removeOne,\n    removeMany,\n    removeAll\n  } = createUnsortedStateAdapter(selectId);\n  function addOneMutably(entity: T, state: R): void {\n    return addManyMutably([entity], state);\n  }\n  function addManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R, existingIds?: Id[]): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    const existingKeys = new Set<Id>(existingIds ?? getCurrent(state.ids));\n    const models = newEntities.filter(model => !existingKeys.has(selectIdValue(model, selectId)));\n    if (models.length !== 0) {\n      mergeFunction(state, models);\n    }\n  }\n  function setOneMutably(entity: T, state: R): void {\n    return setManyMutably([entity], state);\n  }\n  function setManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    if (newEntities.length !== 0) {\n      for (const item of newEntities) {\n        delete (state.entities as Record<Id, T>)[selectId(item)];\n      }\n      mergeFunction(state, newEntities);\n    }\n  }\n  function setAllMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.entities = {} as Record<Id, T>;\n    state.ids = [];\n    addManyMutably(newEntities, state, []);\n  }\n  function updateOneMutably(update: Update<T, Id>, state: R): void {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates: ReadonlyArray<Update<T, Id>>, state: R): void {\n    let appliedUpdates = false;\n    let replacedIds = false;\n    for (let update of updates) {\n      const entity: T | undefined = (state.entities as Record<Id, T>)[update.id];\n      if (!entity) {\n        continue;\n      }\n      appliedUpdates = true;\n      Object.assign(entity, update.changes);\n      const newId = selectId(entity);\n      if (update.id !== newId) {\n        // We do support the case where updates can change an item's ID.\n        // This makes things trickier - go ahead and swap the IDs in state now.\n        replacedIds = true;\n        delete (state.entities as Record<Id, T>)[update.id];\n        const oldIndex = (state.ids as Id[]).indexOf(update.id);\n        state.ids[oldIndex] = newId;\n        (state.entities as Record<Id, T>)[newId] = entity;\n      }\n    }\n    if (appliedUpdates) {\n      mergeFunction(state, [], appliedUpdates, replacedIds);\n    }\n  }\n  function upsertOneMutably(entity: T, state: R): void {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    const [added, updated, existingIdsArray] = splitAddedUpdatedEntities<T, Id>(newEntities, selectId, state);\n    if (added.length) {\n      addManyMutably(added, state, existingIdsArray);\n    }\n    if (updated.length) {\n      updateManyMutably(updated, state);\n    }\n  }\n  function areArraysEqual(a: readonly unknown[], b: readonly unknown[]) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] === b[i]) {\n        continue;\n      }\n      return false;\n    }\n    return true;\n  }\n  type MergeFunction = (state: R, addedItems: readonly T[], appliedUpdates?: boolean, replacedIds?: boolean) => void;\n  const mergeFunction: MergeFunction = (state, addedItems, appliedUpdates, replacedIds) => {\n    const currentEntities = getCurrent(state.entities);\n    const currentIds = getCurrent(state.ids);\n    const stateEntities = state.entities as Record<Id, T>;\n    let ids: Iterable<Id> = currentIds;\n    if (replacedIds) {\n      ids = new Set(currentIds);\n    }\n    let sortedEntities: T[] = [];\n    for (const id of ids) {\n      const entity = currentEntities[id];\n      if (entity) {\n        sortedEntities.push(entity);\n      }\n    }\n    const wasPreviouslyEmpty = sortedEntities.length === 0;\n\n    // Insert/overwrite all new/updated\n    for (const item of addedItems) {\n      stateEntities[selectId(item)] = item;\n      if (!wasPreviouslyEmpty) {\n        // Binary search insertion generally requires fewer comparisons\n        insert(sortedEntities, item, comparer);\n      }\n    }\n    if (wasPreviouslyEmpty) {\n      // All we have is the incoming values, sort them\n      sortedEntities = addedItems.slice().sort(comparer);\n    } else if (appliedUpdates) {\n      // We should have a _mostly_-sorted array already\n      sortedEntities.sort(comparer);\n    }\n    const newSortedIds = sortedEntities.map(selectId);\n    if (!areArraysEqual(currentIds, newSortedIds)) {\n      state.ids = newSortedIds;\n    }\n  };\n  return {\n    removeOne,\n    removeMany,\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably)\n  };\n}", "import type { EntityAdapter, EntityId, EntityAdapterOptions } from './models';\nimport { createInitialStateFactory } from './entity_state';\nimport { createSelectorsFactory } from './state_selectors';\nimport { createSortedStateAdapter } from './sorted_state_adapter';\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter';\nimport type { WithRequiredProp } from '../tsHelpers';\nexport function createEntityAdapter<T, Id extends EntityId>(options: WithRequiredProp<EntityAdapterOptions<T, Id>, 'selectId'>): EntityAdapter<T, Id>;\nexport function createEntityAdapter<T extends {\n  id: EntityId;\n}>(options?: Omit<EntityAdapterOptions<T, T['id']>, 'selectId'>): EntityAdapter<T, T['id']>;\n\n/**\n *\n * @param options\n *\n * @public\n */\nexport function createEntityAdapter<T>(options: EntityAdapterOptions<T, EntityId> = {}): EntityAdapter<T, EntityId> {\n  const {\n    selectId,\n    sortComparer\n  }: Required<EntityAdapterOptions<T, EntityId>> = {\n    sortComparer: false,\n    selectId: (instance: any) => instance.id,\n    ...options\n  };\n  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  const stateFactory = createInitialStateFactory(stateAdapter);\n  const selectorsFactory = createSelectorsFactory<T, EntityId>();\n  return {\n    selectId,\n    sortComparer,\n    ...stateFactory,\n    ...selectorsFactory,\n    ...stateAdapter\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3 } from \"@reduxjs/toolkit\";\nimport type { Action, Dispatch, MiddlewareAPI, UnknownAction } from 'redux';\nimport { isAction } from 'redux';\nimport type { ThunkDispatch } from 'redux-thunk';\nimport { createAction } from '../createAction';\nimport { nanoid } from '../nanoid';\nimport { TaskAbortError, listenerCancelled, listenerCompleted, taskCancelled, taskCompleted } from './exceptions';\nimport { createDelay, createPause, raceWithSignal, runTask, validateActive } from './task';\nimport type { AbortSignalWithReason, AddListenerOverloads, AnyListenerPredicate, CreateListenerMiddlewareOptions, FallbackAddListenerOptions, ForkOptions, ForkedTask, ForkedTaskExecutor, ListenerEntry, ListenerErrorHandler, ListenerErrorInfo, ListenerMiddleware, ListenerMiddlewareInstance, TakePattern, TaskResult, TypedAddListener, TypedCreateListenerEntry, TypedRemoveListener, UnsubscribeListener, UnsubscribeListenerOptions } from './types';\nimport { abortControllerWithReason, addAbortSignalListener, assertFunction, catchRejection, noop } from './utils';\nexport { TaskAbortError } from './exceptions';\nexport type { AsyncTaskExecutor, CreateListenerMiddlewareOptions, ForkedTask, ForkedTaskAPI, ForkedTaskExecutor, ListenerEffect, ListenerEffectAPI, ListenerErrorHandler, ListenerMiddleware, ListenerMiddlewareInstance, SyncTaskExecutor, TaskCancelled, TaskRejected, TaskResolved, TaskResult, TypedAddListener, TypedRemoveListener, TypedStartListening, TypedStopListening, UnsubscribeListener, UnsubscribeListenerOptions } from './types';\n\n//Overly-aggressive byte-shaving\nconst {\n  assign\n} = Object;\n/**\n * @internal\n */\nconst INTERNAL_NIL_TOKEN = {} as const;\nconst alm = 'listenerMiddleware' as const;\nconst createFork = (parentAbortSignal: AbortSignalWithReason<unknown>, parentBlockingPromises: Promise<any>[]) => {\n  const linkControllers = (controller: AbortController) => addAbortSignalListener(parentAbortSignal, () => abortControllerWithReason(controller, parentAbortSignal.reason));\n  return <T,>(taskExecutor: ForkedTaskExecutor<T>, opts?: ForkOptions): ForkedTask<T> => {\n    assertFunction(taskExecutor, 'taskExecutor');\n    const childAbortController = new AbortController();\n    linkControllers(childAbortController);\n    const result = runTask<T>(async (): Promise<T> => {\n      validateActive(parentAbortSignal);\n      validateActive(childAbortController.signal);\n      const result = (await taskExecutor({\n        pause: createPause(childAbortController.signal),\n        delay: createDelay(childAbortController.signal),\n        signal: childAbortController.signal\n      })) as T;\n      validateActive(childAbortController.signal);\n      return result;\n    }, () => abortControllerWithReason(childAbortController, taskCompleted));\n    if (opts?.autoJoin) {\n      parentBlockingPromises.push(result.catch(noop));\n    }\n    return {\n      result: createPause<TaskResult<T>>(parentAbortSignal)(result),\n      cancel() {\n        abortControllerWithReason(childAbortController, taskCancelled);\n      }\n    };\n  };\n};\nconst createTakePattern = <S,>(startListening: AddListenerOverloads<UnsubscribeListener, S, Dispatch>, signal: AbortSignal): TakePattern<S> => {\n  /**\n   * A function that takes a ListenerPredicate and an optional timeout,\n   * and resolves when either the predicate returns `true` based on an action\n   * state combination or when the timeout expires.\n   * If the parent listener is canceled while waiting, this will throw a\n   * TaskAbortError.\n   */\n  const take = async <P extends AnyListenerPredicate<S>,>(predicate: P, timeout: number | undefined) => {\n    validateActive(signal);\n\n    // Placeholder unsubscribe function until the listener is added\n    let unsubscribe: UnsubscribeListener = () => {};\n    const tuplePromise = new Promise<[Action, S, S]>((resolve, reject) => {\n      // Inside the Promise, we synchronously add the listener.\n      let stopListening = startListening({\n        predicate: predicate as any,\n        effect: (action, listenerApi): void => {\n          // One-shot listener that cleans up as soon as the predicate passes\n          listenerApi.unsubscribe();\n          // Resolve the promise with the same arguments the predicate saw\n          resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);\n        }\n      });\n      unsubscribe = () => {\n        stopListening();\n        reject();\n      };\n    });\n    const promises: (Promise<null> | Promise<[Action, S, S]>)[] = [tuplePromise];\n    if (timeout != null) {\n      promises.push(new Promise<null>(resolve => setTimeout(resolve, timeout, null)));\n    }\n    try {\n      const output = await raceWithSignal(signal, Promise.race(promises));\n      validateActive(signal);\n      return output;\n    } finally {\n      // Always clean up the listener\n      unsubscribe();\n    }\n  };\n  return ((predicate: AnyListenerPredicate<S>, timeout: number | undefined) => catchRejection(take(predicate, timeout))) as TakePattern<S>;\n};\nconst getListenerEntryPropsFrom = (options: FallbackAddListenerOptions) => {\n  let {\n    type,\n    actionCreator,\n    matcher,\n    predicate,\n    effect\n  } = options;\n  if (type) {\n    predicate = createAction(type).match;\n  } else if (actionCreator) {\n    type = actionCreator!.type;\n    predicate = actionCreator.match;\n  } else if (matcher) {\n    predicate = matcher;\n  } else if (predicate) {\n    // pass\n  } else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(21) : 'Creating or removing a listener requires one of the known fields for matching an action');\n  }\n  assertFunction(effect, 'options.listener');\n  return {\n    predicate,\n    type,\n    effect\n  };\n};\n\n/** Accepts the possible options for creating a listener, and returns a formatted listener entry */\nexport const createListenerEntry: TypedCreateListenerEntry<unknown> = /* @__PURE__ */assign((options: FallbackAddListenerOptions) => {\n  const {\n    type,\n    predicate,\n    effect\n  } = getListenerEntryPropsFrom(options);\n  const entry: ListenerEntry<unknown> = {\n    id: nanoid(),\n    effect,\n    type,\n    predicate,\n    pending: new Set<AbortController>(),\n    unsubscribe: () => {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(22) : 'Unsubscribe not initialized');\n    }\n  };\n  return entry;\n}, {\n  withTypes: () => createListenerEntry\n}) as unknown as TypedCreateListenerEntry<unknown>;\nconst findListenerEntry = (listenerMap: Map<string, ListenerEntry>, options: FallbackAddListenerOptions) => {\n  const {\n    type,\n    effect,\n    predicate\n  } = getListenerEntryPropsFrom(options);\n  return Array.from(listenerMap.values()).find(entry => {\n    const matchPredicateOrType = typeof type === 'string' ? entry.type === type : entry.predicate === predicate;\n    return matchPredicateOrType && entry.effect === effect;\n  });\n};\nconst cancelActiveListeners = (entry: ListenerEntry<unknown, Dispatch<UnknownAction>>) => {\n  entry.pending.forEach(controller => {\n    abortControllerWithReason(controller, listenerCancelled);\n  });\n};\nconst createClearListenerMiddleware = (listenerMap: Map<string, ListenerEntry>) => {\n  return () => {\n    listenerMap.forEach(cancelActiveListeners);\n    listenerMap.clear();\n  };\n};\n\n/**\n * Safely reports errors to the `errorHandler` provided.\n * Errors that occur inside `errorHandler` are notified in a new task.\n * Inspired by [rxjs reportUnhandledError](https://github.com/ReactiveX/rxjs/blob/6fafcf53dc9e557439b25debaeadfd224b245a66/src/internal/util/reportUnhandledError.ts)\n * @param errorHandler\n * @param errorToNotify\n */\nconst safelyNotifyError = (errorHandler: ListenerErrorHandler, errorToNotify: unknown, errorInfo: ListenerErrorInfo): void => {\n  try {\n    errorHandler(errorToNotify, errorInfo);\n  } catch (errorHandlerError) {\n    // We cannot let an error raised here block the listener queue.\n    // The error raised here will be picked up by `window.onerror`, `process.on('error')` etc...\n    setTimeout(() => {\n      throw errorHandlerError;\n    }, 0);\n  }\n};\n\n/**\n * @public\n */\nexport const addListener = /* @__PURE__ */assign(/* @__PURE__ */createAction(`${alm}/add`), {\n  withTypes: () => addListener\n}) as unknown as TypedAddListener<unknown>;\n\n/**\n * @public\n */\nexport const clearAllListeners = /* @__PURE__ */createAction(`${alm}/removeAll`);\n\n/**\n * @public\n */\nexport const removeListener = /* @__PURE__ */assign(/* @__PURE__ */createAction(`${alm}/remove`), {\n  withTypes: () => removeListener\n}) as unknown as TypedRemoveListener<unknown>;\nconst defaultErrorHandler: ListenerErrorHandler = (...args: unknown[]) => {\n  console.error(`${alm}/error`, ...args);\n};\n\n/**\n * @public\n */\nexport const createListenerMiddleware = <StateType = unknown, DispatchType extends Dispatch<Action> = ThunkDispatch<StateType, unknown, UnknownAction>, ExtraArgument = unknown>(middlewareOptions: CreateListenerMiddlewareOptions<ExtraArgument> = {}) => {\n  const listenerMap = new Map<string, ListenerEntry>();\n  const {\n    extra,\n    onError = defaultErrorHandler\n  } = middlewareOptions;\n  assertFunction(onError, 'onError');\n  const insertEntry = (entry: ListenerEntry) => {\n    entry.unsubscribe = () => listenerMap.delete(entry.id);\n    listenerMap.set(entry.id, entry);\n    return (cancelOptions?: UnsubscribeListenerOptions) => {\n      entry.unsubscribe();\n      if (cancelOptions?.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    };\n  };\n  const startListening = ((options: FallbackAddListenerOptions) => {\n    const entry = findListenerEntry(listenerMap, options) ?? createListenerEntry(options as any);\n    return insertEntry(entry);\n  }) as AddListenerOverloads<any>;\n  assign(startListening, {\n    withTypes: () => startListening\n  });\n  const stopListening = (options: FallbackAddListenerOptions & UnsubscribeListenerOptions): boolean => {\n    const entry = findListenerEntry(listenerMap, options);\n    if (entry) {\n      entry.unsubscribe();\n      if (options.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    }\n    return !!entry;\n  };\n  assign(stopListening, {\n    withTypes: () => stopListening\n  });\n  const notifyListener = async (entry: ListenerEntry<unknown, Dispatch<UnknownAction>>, action: unknown, api: MiddlewareAPI, getOriginalState: () => StateType) => {\n    const internalTaskController = new AbortController();\n    const take = createTakePattern(startListening as AddListenerOverloads<any>, internalTaskController.signal);\n    const autoJoinPromises: Promise<any>[] = [];\n    try {\n      entry.pending.add(internalTaskController);\n      await Promise.resolve(entry.effect(action,\n      // Use assign() rather than ... to avoid extra helper functions added to bundle\n      assign({}, api, {\n        getOriginalState,\n        condition: (predicate: AnyListenerPredicate<any>, timeout?: number) => take(predicate, timeout).then(Boolean),\n        take,\n        delay: createDelay(internalTaskController.signal),\n        pause: createPause<any>(internalTaskController.signal),\n        extra,\n        signal: internalTaskController.signal,\n        fork: createFork(internalTaskController.signal, autoJoinPromises),\n        unsubscribe: entry.unsubscribe,\n        subscribe: () => {\n          listenerMap.set(entry.id, entry);\n        },\n        cancelActiveListeners: () => {\n          entry.pending.forEach((controller, _, set) => {\n            if (controller !== internalTaskController) {\n              abortControllerWithReason(controller, listenerCancelled);\n              set.delete(controller);\n            }\n          });\n        },\n        cancel: () => {\n          abortControllerWithReason(internalTaskController, listenerCancelled);\n          entry.pending.delete(internalTaskController);\n        },\n        throwIfCancelled: () => {\n          validateActive(internalTaskController.signal);\n        }\n      })));\n    } catch (listenerError) {\n      if (!(listenerError instanceof TaskAbortError)) {\n        safelyNotifyError(onError, listenerError, {\n          raisedBy: 'effect'\n        });\n      }\n    } finally {\n      await Promise.all(autoJoinPromises);\n      abortControllerWithReason(internalTaskController, listenerCompleted); // Notify that the task has completed\n      entry.pending.delete(internalTaskController);\n    }\n  };\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\n  const middleware: ListenerMiddleware<StateType, DispatchType, ExtraArgument> = api => next => action => {\n    if (!isAction(action)) {\n      // we only want to notify listeners for action objects\n      return next(action);\n    }\n    if (addListener.match(action)) {\n      return startListening(action.payload as any);\n    }\n    if (clearAllListeners.match(action)) {\n      clearListenerMiddleware();\n      return;\n    }\n    if (removeListener.match(action)) {\n      return stopListening(action.payload);\n    }\n\n    // Need to get this state _before_ the reducer processes the action\n    let originalState: StateType | typeof INTERNAL_NIL_TOKEN = api.getState();\n\n    // `getOriginalState` can only be called synchronously.\n    // @see https://github.com/reduxjs/redux-toolkit/discussions/1648#discussioncomment-1932820\n    const getOriginalState = (): StateType => {\n      if (originalState === INTERNAL_NIL_TOKEN) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(23) : `${alm}: getOriginalState can only be called synchronously`);\n      }\n      return originalState as StateType;\n    };\n    let result: unknown;\n    try {\n      // Actually forward the action to the reducer before we handle listeners\n      result = next(action);\n      if (listenerMap.size > 0) {\n        const currentState = api.getState();\n        // Work around ESBuild+TS transpilation issue\n        const listenerEntries = Array.from(listenerMap.values());\n        for (const entry of listenerEntries) {\n          let runListener = false;\n          try {\n            runListener = entry.predicate(action, currentState, originalState);\n          } catch (predicateError) {\n            runListener = false;\n            safelyNotifyError(onError, predicateError, {\n              raisedBy: 'predicate'\n            });\n          }\n          if (!runListener) {\n            continue;\n          }\n          notifyListener(entry, action, api, getOriginalState);\n        }\n      }\n    } finally {\n      // Remove `originalState` store from this scope.\n      originalState = INTERNAL_NIL_TOKEN;\n    }\n    return result;\n  };\n  return {\n    middleware,\n    startListening,\n    stopListening,\n    clearListeners: clearListenerMiddleware\n  } as ListenerMiddlewareInstance<StateType, DispatchType, ExtraArgument>;\n};", "import type { SerializedError } from '@reduxjs/toolkit';\nconst task = 'task';\nconst listener = 'listener';\nconst completed = 'completed';\nconst cancelled = 'cancelled';\n\n/* TaskAbortError error codes  */\nexport const taskCancelled = `task-${cancelled}` as const;\nexport const taskCompleted = `task-${completed}` as const;\nexport const listenerCancelled = `${listener}-${cancelled}` as const;\nexport const listenerCompleted = `${listener}-${completed}` as const;\nexport class TaskAbortError implements SerializedError {\n  name = 'TaskAbortError';\n  message: string;\n  constructor(public code: string | undefined) {\n    this.message = `${task} ${cancelled} (reason: ${code})`;\n  }\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { AbortSignalWithReason } from './types';\nexport const assertFunction: (func: unknown, expected: string) => asserts func is (...args: unknown[]) => unknown = (func: unknown, expected: string) => {\n  if (typeof func !== 'function') {\n    throw new TypeError(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(32) : `${expected} is not a function`);\n  }\n};\nexport const noop = () => {};\nexport const catchRejection = <T,>(promise: Promise<T>, onError = noop): Promise<T> => {\n  promise.catch(onError);\n  return promise;\n};\nexport const addAbortSignalListener = (abortSignal: AbortSignal, callback: (evt: Event) => void) => {\n  abortSignal.addEventListener('abort', callback, {\n    once: true\n  });\n  return () => abortSignal.removeEventListener('abort', callback);\n};\n\n/**\n * Calls `abortController.abort(reason)` and patches `signal.reason`.\n * if it is not supported.\n *\n * At the time of writing `signal.reason` is available in FF chrome, edge node 17 and deno.\n * @param abortController\n * @param reason\n * @returns\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/reason\n */\nexport const abortControllerWithReason = <T,>(abortController: AbortController, reason: T): void => {\n  type Consumer<T> = (val: T) => void;\n  const signal = abortController.signal as AbortSignalWithReason<T>;\n  if (signal.aborted) {\n    return;\n  }\n\n  // Patch `reason` if necessary.\n  // - We use defineProperty here because reason is a getter of `AbortSignal.__proto__`.\n  // - We need to patch 'reason' before calling `.abort()` because listeners to the 'abort'\n  // event are are notified immediately.\n  if (!('reason' in signal)) {\n    Object.defineProperty(signal, 'reason', {\n      enumerable: true,\n      value: reason,\n      configurable: true,\n      writable: true\n    });\n  }\n  ;\n  (abortController.abort as Consumer<typeof reason>)(reason);\n};", "import { TaskAbortError } from './exceptions';\nimport type { AbortSignalWithReason, TaskResult } from './types';\nimport { addAbortSignalListener, catchRejection, noop } from './utils';\n\n/**\n * Synchronously raises {@link TaskAbortError} if the task tied to the input `signal` has been cancelled.\n * @param signal\n * @param reason\n * @see {TaskAbortError}\n */\nexport const validateActive = (signal: AbortSignal): void => {\n  if (signal.aborted) {\n    const {\n      reason\n    } = signal as AbortSignalWithReason<string>;\n    throw new TaskAbortError(reason);\n  }\n};\n\n/**\n * Generates a race between the promise(s) and the AbortSignal\n * This avoids `Promise.race()`-related memory leaks:\n * https://github.com/nodejs/node/issues/17469#issuecomment-349794909\n */\nexport function raceWithSignal<T>(signal: AbortSignalWithReason<string>, promise: Promise<T>): Promise<T> {\n  let cleanup = noop;\n  return new Promise<T>((resolve, reject) => {\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason));\n    if (signal.aborted) {\n      notifyRejection();\n      return;\n    }\n    cleanup = addAbortSignalListener(signal, notifyRejection);\n    promise.finally(() => cleanup()).then(resolve, reject);\n  }).finally(() => {\n    // after this point, replace `cleanup` with a noop, so there is no reference to `signal` any more\n    cleanup = noop;\n  });\n}\n\n/**\n * Runs a task and returns promise that resolves to {@link TaskResult}.\n * Second argument is an optional `cleanUp` function that always runs after task.\n *\n * **Note:** `runTask` runs the executor in the next microtask.\n * @returns\n */\nexport const runTask = async <T,>(task: () => Promise<T>, cleanUp?: () => void): Promise<TaskResult<T>> => {\n  try {\n    await Promise.resolve();\n    const value = await task();\n    return {\n      status: 'ok',\n      value\n    };\n  } catch (error: any) {\n    return {\n      status: error instanceof TaskAbortError ? 'cancelled' : 'rejected',\n      error\n    };\n  } finally {\n    cleanUp?.();\n  }\n};\n\n/**\n * Given an input `AbortSignal` and a promise returns another promise that resolves\n * as soon the input promise is provided or rejects as soon as\n * `AbortSignal.abort` is `true`.\n * @param signal\n * @returns\n */\nexport const createPause = <T,>(signal: AbortSignal) => {\n  return (promise: Promise<T>): Promise<T> => {\n    return catchRejection(raceWithSignal(signal, promise).then(output => {\n      validateActive(signal);\n      return output;\n    }));\n  };\n};\n\n/**\n * Given an input `AbortSignal` and `timeoutMs` returns a promise that resolves\n * after `timeoutMs` or rejects as soon as `AbortSignal.abort` is `true`.\n * @param signal\n * @returns\n */\nexport const createDelay = (signal: AbortSignal) => {\n  const pause = createPause<void>(signal);\n  return (timeoutMs: number): Promise<void> => {\n    return pause(new Promise<void>(resolve => setTimeout(resolve, timeoutMs)));\n  };\n};", "import type { Dispatch, Middleware, UnknownAction } from 'redux';\nimport { compose } from 'redux';\nimport { createAction } from '../createAction';\nimport { isAllOf } from '../matchers';\nimport { nanoid } from '../nanoid';\nimport { getOrInsertComputed } from '../utils';\nimport type { AddMiddleware, DynamicMiddleware, DynamicMiddlewareInstance, MiddlewareEntry, WithMiddleware } from './types';\nexport type { DynamicMiddlewareInstance, GetDispatchType as GetDispatch, MiddlewareApiConfig } from './types';\nconst createMiddlewareEntry = <State = any, DispatchType extends Dispatch<UnknownAction> = Dispatch<UnknownAction>>(middleware: Middleware<any, State, DispatchType>): MiddlewareEntry<State, DispatchType> => ({\n  middleware,\n  applied: new Map()\n});\nconst matchInstance = (instanceId: string) => (action: any): action is {\n  meta: {\n    instanceId: string;\n  };\n} => action?.meta?.instanceId === instanceId;\nexport const createDynamicMiddleware = <State = any, DispatchType extends Dispatch<UnknownAction> = Dispatch<UnknownAction>>(): DynamicMiddlewareInstance<State, DispatchType> => {\n  const instanceId = nanoid();\n  const middlewareMap = new Map<Middleware<any, State, DispatchType>, MiddlewareEntry<State, DispatchType>>();\n  const withMiddleware = Object.assign(createAction('dynamicMiddleware/add', (...middlewares: Middleware<any, State, DispatchType>[]) => ({\n    payload: middlewares,\n    meta: {\n      instanceId\n    }\n  })), {\n    withTypes: () => withMiddleware\n  }) as WithMiddleware<State, DispatchType>;\n  const addMiddleware = Object.assign(function addMiddleware(...middlewares: Middleware<any, State, DispatchType>[]) {\n    middlewares.forEach(middleware => {\n      getOrInsertComputed(middlewareMap, middleware, createMiddlewareEntry);\n    });\n  }, {\n    withTypes: () => addMiddleware\n  }) as AddMiddleware<State, DispatchType>;\n  const getFinalMiddleware: Middleware<{}, State, DispatchType> = api => {\n    const appliedMiddleware = Array.from(middlewareMap.values()).map(entry => getOrInsertComputed(entry.applied, api, entry.middleware));\n    return compose(...appliedMiddleware);\n  };\n  const isWithMiddleware = isAllOf(withMiddleware, matchInstance(instanceId));\n  const middleware: DynamicMiddleware<State, DispatchType> = api => next => action => {\n    if (isWithMiddleware(action)) {\n      addMiddleware(...action.payload);\n      return api.dispatch;\n    }\n    return getFinalMiddleware(api)(next)(action);\n  };\n  return {\n    middleware,\n    addMiddleware,\n    withMiddleware,\n    instanceId\n  };\n};", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2 } from \"@reduxjs/toolkit\";\nimport type { Reducer, StateFromReducersMapObject, UnknownAction } from 'redux';\nimport { combineReducers } from 'redux';\nimport { nanoid } from './nanoid';\nimport type { Id, NonUndefined, Tail, UnionToIntersection, WithOptionalProp } from './tsHelpers';\nimport { getOrInsertComputed } from './utils';\ntype SliceLike<ReducerPath extends string, State> = {\n  reducerPath: ReducerPath;\n  reducer: Reducer<State>;\n};\ntype AnySliceLike = SliceLike<string, any>;\ntype SliceLikeReducerPath<A extends AnySliceLike> = A extends SliceLike<infer ReducerPath, any> ? ReducerPath : never;\ntype SliceLikeState<A extends AnySliceLike> = A extends SliceLike<any, infer State> ? State : never;\nexport type WithSlice<A extends AnySliceLike> = { [Path in SliceLikeReducerPath<A>]: SliceLikeState<A> };\ntype ReducerMap = Record<string, Reducer>;\ntype ExistingSliceLike<DeclaredState> = { [ReducerPath in keyof DeclaredState]: SliceLike<ReducerPath & string, NonUndefined<DeclaredState[ReducerPath]>> }[keyof DeclaredState];\nexport type InjectConfig = {\n  /**\n   * Allow replacing reducer with a different reference. Normally, an error will be thrown if a different reducer instance to the one already injected is used.\n   */\n  overrideExisting?: boolean;\n};\n\n/**\n * A reducer that allows for slices/reducers to be injected after initialisation.\n */\nexport interface CombinedSliceReducer<InitialState, DeclaredState = InitialState> extends Reducer<DeclaredState, UnknownAction, Partial<DeclaredState>> {\n  /**\n   * Provide a type for slices that will be injected lazily.\n   *\n   * One way to do this would be with interface merging:\n   * ```ts\n   *\n   * export interface LazyLoadedSlices {}\n   *\n   * export const rootReducer = combineSlices(stringSlice).withLazyLoadedSlices<LazyLoadedSlices>();\n   *\n   * // elsewhere\n   *\n   * declare module './reducer' {\n   *   export interface LazyLoadedSlices extends WithSlice<typeof booleanSlice> {}\n   * }\n   *\n   * const withBoolean = rootReducer.inject(booleanSlice);\n   *\n   * // elsewhere again\n   *\n   * declare module './reducer' {\n   *   export interface LazyLoadedSlices {\n   *     customName: CustomState\n   *   }\n   * }\n   *\n   * const withCustom = rootReducer.inject({ reducerPath: \"customName\", reducer: customSlice.reducer })\n   * ```\n   */\n  withLazyLoadedSlices<Lazy = {}>(): CombinedSliceReducer<InitialState, Id<DeclaredState & Partial<Lazy>>>;\n\n  /**\n   * Inject a slice.\n   *\n   * Accepts an individual slice, RTKQ API instance, or a \"slice-like\" { reducerPath, reducer } object.\n   *\n   * ```ts\n   * rootReducer.inject(booleanSlice)\n   * rootReducer.inject(baseApi)\n   * rootReducer.inject({ reducerPath: 'boolean' as const, reducer: newReducer }, { overrideExisting: true })\n   * ```\n   *\n   */\n  inject<Sl extends Id<ExistingSliceLike<DeclaredState>>>(slice: Sl, config?: InjectConfig): CombinedSliceReducer<InitialState, Id<DeclaredState & WithSlice<Sl>>>;\n\n  /**\n   * Inject a slice.\n   *\n   * Accepts an individual slice, RTKQ API instance, or a \"slice-like\" { reducerPath, reducer } object.\n   *\n   * ```ts\n   * rootReducer.inject(booleanSlice)\n   * rootReducer.inject(baseApi)\n   * rootReducer.inject({ reducerPath: 'boolean' as const, reducer: newReducer }, { overrideExisting: true })\n   * ```\n   *\n   */\n  inject<ReducerPath extends string, State>(slice: SliceLike<ReducerPath, State & (ReducerPath extends keyof DeclaredState ? never : State)>, config?: InjectConfig): CombinedSliceReducer<InitialState, Id<DeclaredState & WithSlice<SliceLike<ReducerPath, State>>>>;\n\n  /**\n   * Create a selector that guarantees that the slices injected will have a defined value when selector is run.\n   *\n   * ```ts\n   * const selectBooleanWithoutInjection = (state: RootState) => state.boolean;\n   * //                                                                ^? boolean | undefined\n   *\n   * const selectBoolean = rootReducer.inject(booleanSlice).selector((state) => {\n   *   // if action hasn't been dispatched since slice was injected, this would usually be undefined\n   *   // however selector() uses a Proxy around the first parameter to ensure that it evaluates to the initial state instead, if undefined\n   *   return state.boolean;\n   *   //           ^? boolean\n   * })\n   * ```\n   *\n   * If the reducer is nested inside the root state, a selectState callback can be passed to retrieve the reducer's state.\n   *\n   * ```ts\n   *\n   * export interface LazyLoadedSlices {};\n   *\n   * export const innerReducer = combineSlices(stringSlice).withLazyLoadedSlices<LazyLoadedSlices>();\n   *\n   * export const rootReducer = combineSlices({ inner: innerReducer });\n   *\n   * export type RootState = ReturnType<typeof rootReducer>;\n   *\n   * // elsewhere\n   *\n   * declare module \"./reducer.ts\" {\n   *  export interface LazyLoadedSlices extends WithSlice<typeof booleanSlice> {}\n   * }\n   *\n   * const withBool = innerReducer.inject(booleanSlice);\n   *\n   * const selectBoolean = withBool.selector(\n   *   (state) => state.boolean,\n   *   (rootState: RootState) => state.inner\n   * );\n   * //    now expects to be passed RootState instead of innerReducer state\n   *\n   * ```\n   *\n   * Value passed to selectorFn will be a Proxy - use selector.original(proxy) to get original state value (useful for debugging)\n   *\n   * ```ts\n   * const injectedReducer = rootReducer.inject(booleanSlice);\n   * const selectBoolean = injectedReducer.selector((state) => {\n   *   console.log(injectedReducer.selector.original(state).boolean) // possibly undefined\n   *   return state.boolean\n   * })\n   * ```\n   */\n  selector: {\n    /**\n     * Create a selector that guarantees that the slices injected will have a defined value when selector is run.\n     *\n     * ```ts\n     * const selectBooleanWithoutInjection = (state: RootState) => state.boolean;\n     * //                                                                ^? boolean | undefined\n     *\n     * const selectBoolean = rootReducer.inject(booleanSlice).selector((state) => {\n     *   // if action hasn't been dispatched since slice was injected, this would usually be undefined\n     *   // however selector() uses a Proxy around the first parameter to ensure that it evaluates to the initial state instead, if undefined\n     *   return state.boolean;\n     *   //           ^? boolean\n     * })\n     * ```\n     *\n     * Value passed to selectorFn will be a Proxy - use selector.original(proxy) to get original state value (useful for debugging)\n     *\n     * ```ts\n     * const injectedReducer = rootReducer.inject(booleanSlice);\n     * const selectBoolean = injectedReducer.selector((state) => {\n     *   console.log(injectedReducer.selector.original(state).boolean) // undefined\n     *   return state.boolean\n     * })\n     * ```\n     */\n    <Selector extends (state: DeclaredState, ...args: any[]) => unknown>(selectorFn: Selector): (state: WithOptionalProp<Parameters<Selector>[0], Exclude<keyof DeclaredState, keyof InitialState>>, ...args: Tail<Parameters<Selector>>) => ReturnType<Selector>;\n\n    /**\n     * Create a selector that guarantees that the slices injected will have a defined value when selector is run.\n     *\n     * ```ts\n     * const selectBooleanWithoutInjection = (state: RootState) => state.boolean;\n     * //                                                                ^? boolean | undefined\n     *\n     * const selectBoolean = rootReducer.inject(booleanSlice).selector((state) => {\n     *   // if action hasn't been dispatched since slice was injected, this would usually be undefined\n     *   // however selector() uses a Proxy around the first parameter to ensure that it evaluates to the initial state instead, if undefined\n     *   return state.boolean;\n     *   //           ^? boolean\n     * })\n     * ```\n     *\n     * If the reducer is nested inside the root state, a selectState callback can be passed to retrieve the reducer's state.\n     *\n     * ```ts\n     *\n     * interface LazyLoadedSlices {};\n     *\n     * const innerReducer = combineSlices(stringSlice).withLazyLoadedSlices<LazyLoadedSlices>();\n     *\n     * const rootReducer = combineSlices({ inner: innerReducer });\n     *\n     * type RootState = ReturnType<typeof rootReducer>;\n     *\n     * // elsewhere\n     *\n     * declare module \"./reducer.ts\" {\n     *  interface LazyLoadedSlices extends WithSlice<typeof booleanSlice> {}\n     * }\n     *\n     * const withBool = innerReducer.inject(booleanSlice);\n     *\n     * const selectBoolean = withBool.selector(\n     *   (state) => state.boolean,\n     *   (rootState: RootState) => state.inner\n     * );\n     * //    now expects to be passed RootState instead of innerReducer state\n     *\n     * ```\n     *\n     * Value passed to selectorFn will be a Proxy - use selector.original(proxy) to get original state value (useful for debugging)\n     *\n     * ```ts\n     * const injectedReducer = rootReducer.inject(booleanSlice);\n     * const selectBoolean = injectedReducer.selector((state) => {\n     *   console.log(injectedReducer.selector.original(state).boolean) // possibly undefined\n     *   return state.boolean\n     * })\n     * ```\n     */\n    <Selector extends (state: DeclaredState, ...args: any[]) => unknown, RootState>(selectorFn: Selector, selectState: (rootState: RootState, ...args: Tail<Parameters<Selector>>) => WithOptionalProp<Parameters<Selector>[0], Exclude<keyof DeclaredState, keyof InitialState>>): (state: RootState, ...args: Tail<Parameters<Selector>>) => ReturnType<Selector>;\n    /**\n     * Returns the unproxied state. Useful for debugging.\n     * @param state state Proxy, that ensures injected reducers have value\n     * @returns original, unproxied state\n     * @throws if value passed is not a state Proxy\n     */\n    original: (state: DeclaredState) => InitialState & Partial<DeclaredState>;\n  };\n}\ntype InitialState<Slices extends Array<AnySliceLike | ReducerMap>> = UnionToIntersection<Slices[number] extends infer Slice ? Slice extends AnySliceLike ? WithSlice<Slice> : StateFromReducersMapObject<Slice> : never>;\nconst isSliceLike = (maybeSliceLike: AnySliceLike | ReducerMap): maybeSliceLike is AnySliceLike => 'reducerPath' in maybeSliceLike && typeof maybeSliceLike.reducerPath === 'string';\nconst getReducers = (slices: Array<AnySliceLike | ReducerMap>) => slices.flatMap(sliceOrMap => isSliceLike(sliceOrMap) ? [[sliceOrMap.reducerPath, sliceOrMap.reducer] as const] : Object.entries(sliceOrMap));\nconst ORIGINAL_STATE = Symbol.for('rtk-state-proxy-original');\nconst isStateProxy = (value: any) => !!value && !!value[ORIGINAL_STATE];\nconst stateProxyMap = new WeakMap<object, object>();\nconst createStateProxy = <State extends object,>(state: State, reducerMap: Partial<Record<PropertyKey, Reducer>>, initialStateCache: Record<PropertyKey, unknown>) => getOrInsertComputed(stateProxyMap, state, () => new Proxy(state, {\n  get: (target, prop, receiver) => {\n    if (prop === ORIGINAL_STATE) return target;\n    const result = Reflect.get(target, prop, receiver);\n    if (typeof result === 'undefined') {\n      const cached = initialStateCache[prop];\n      if (typeof cached !== 'undefined') return cached;\n      const reducer = reducerMap[prop];\n      if (reducer) {\n        // ensure action type is random, to prevent reducer treating it differently\n        const reducerResult = reducer(undefined, {\n          type: nanoid()\n        });\n        if (typeof reducerResult === 'undefined') {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(24) : `The slice reducer for key \"${prop.toString()}\" returned undefined when called for selector(). ` + `If the state passed to the reducer is undefined, you must ` + `explicitly return the initial state. The initial state may ` + `not be undefined. If you don't want to set a value for this reducer, ` + `you can use null instead of undefined.`);\n        }\n        initialStateCache[prop] = reducerResult;\n        return reducerResult;\n      }\n    }\n    return result;\n  }\n})) as State;\nconst original = (state: any) => {\n  if (!isStateProxy(state)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(25) : 'original must be used on state Proxy');\n  }\n  return state[ORIGINAL_STATE];\n};\nconst emptyObject = {};\nconst noopReducer: Reducer<Record<string, any>> = (state = emptyObject) => state;\nexport function combineSlices<Slices extends Array<AnySliceLike | ReducerMap>>(...slices: Slices): CombinedSliceReducer<Id<InitialState<Slices>>> {\n  const reducerMap = Object.fromEntries<Reducer>(getReducers(slices));\n  const getReducer = () => Object.keys(reducerMap).length ? combineReducers(reducerMap) : noopReducer;\n  let reducer = getReducer();\n  function combinedReducer(state: Record<string, unknown>, action: UnknownAction) {\n    return reducer(state, action);\n  }\n  combinedReducer.withLazyLoadedSlices = () => combinedReducer;\n  const initialStateCache: Record<PropertyKey, unknown> = {};\n  const inject = (slice: AnySliceLike, config: InjectConfig = {}): typeof combinedReducer => {\n    const {\n      reducerPath,\n      reducer: reducerToInject\n    } = slice;\n    const currentReducer = reducerMap[reducerPath];\n    if (!config.overrideExisting && currentReducer && currentReducer !== reducerToInject) {\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n        console.error(`called \\`inject\\` to override already-existing reducer ${reducerPath} without specifying \\`overrideExisting: true\\``);\n      }\n      return combinedReducer;\n    }\n    if (config.overrideExisting && currentReducer !== reducerToInject) {\n      delete initialStateCache[reducerPath];\n    }\n    reducerMap[reducerPath] = reducerToInject;\n    reducer = getReducer();\n    return combinedReducer;\n  };\n  const selector = Object.assign(function makeSelector<State extends object, RootState, Args extends any[]>(selectorFn: (state: State, ...args: Args) => any, selectState?: (rootState: RootState, ...args: Args) => State) {\n    return function selector(state: State, ...args: Args) {\n      return selectorFn(createStateProxy(selectState ? selectState(state as any, ...args) : state, reducerMap, initialStateCache), ...args);\n    };\n  }, {\n    original\n  });\n  return Object.assign(combinedReducer, {\n    inject,\n    selector\n  }) as any;\n}", "/**\r\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\r\n *\r\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\r\n * during build.\r\n * @param {number} code\r\n */\nexport function formatProdErrorMessage(code: number) {\n  return `Minified Redux Toolkit error #${code}; visit https://redux-toolkit.js.org/Errors?code=${code} for the full message or ` + 'use the non-minified dev environment for full errors. ';\n}"], "mappings": ";;;;;AAGA,cAAc;AACd,SAAoBA,OAAA,EAAiBC,OAAA,IAAAC,QAAA,EAASC,MAAA,EAAQC,QAAA,IAAAC,SAAA,EAAUC,OAAA,IAAAC,QAAA,QAAe;AAE/E,SAASC,cAAA,EAAgBC,qBAAA,IAAAC,sBAAA,EAAuBC,UAAA,EAAYC,cAAA,IAAAC,eAAA,QAAsB;;;ACNlF,SAASZ,OAAA,EAASK,OAAA,QAAe;AACjC,SAASG,qBAAA,EAAuBG,cAAA,QAAsB;AAC/C,IAAME,8BAAA,GAA+D,SAAAA,CAAA,EAAwB;EAClG,MAAMC,eAAA,GAAkBN,qBAAA,CAA8B,GAAAO,SAAO;EAC7D,MAAMC,wBAAA,GAA0BC,MAAA,CAAOC,MAAA,CAAO,YAAwB;IACpE,MAAMC,QAAA,GAAWL,eAAA,CAAe,GAAAC,SAAO;IACvC,MAAMK,eAAA,GAAkB,SAAAA,CAACC,KAAA;MAAA,SAAAC,IAAA,GAAAP,SAAA,CAAAQ,MAAA,EAAmBC,IAAA,OAAAC,KAAA,CAAAH,IAAA,OAAAA,IAAA,WAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAAF,IAAA,CAAAE,IAAA,QAAAX,SAAA,CAAAW,IAAA;MAAA;MAAA,OAAoBP,QAAA,CAASd,OAAA,CAAQgB,KAAK,IAAIrB,OAAA,CAAQqB,KAAK,IAAIA,KAAA,EAAO,GAAGG,IAAI;IAAA;IACzHP,MAAA,CAAOC,MAAA,CAAOE,eAAA,EAAiBD,QAAQ;IACvC,OAAOC,eAAA;EACT,GAAG;IACDO,SAAA,EAAWA,CAAA,KAAMX;EACnB,CAAC;EACD,OAAOA,wBAAA;AACT;AASO,IAAMY,uBAAA,GACb,eAAAf,8BAAA,CAA+BF,cAAc;;;ACrB7C,SAASkB,eAAA,EAAiBC,WAAA,EAAaC,OAAA,IAAAC,QAAA,EAASC,eAAA,EAAiBC,aAAA,IAAAC,cAAA,QAAqB;;;ACDtF,SAASJ,OAAA,QAAe;AAkNjB,IAAMK,mBAAA,GAA2C,OAAOC,MAAA,KAAW,eAAgBA,MAAA,CAAeC,oCAAA,GAAwCD,MAAA,CAAeC,oCAAA,GAAuC,YAAY;EACjN,IAAIvB,SAAA,CAAUQ,MAAA,KAAW,GAAG,OAAO;EACnC,IAAI,OAAOR,SAAA,CAAU,CAAC,MAAM,UAAU,OAAOgB,OAAA;EAC7C,OAAOA,OAAA,CAAQQ,KAAA,CAAM,MAAMxB,SAA8B;AAC3D;AAKO,IAAMyB,gBAAA,GAET,OAAOH,MAAA,KAAW,eAAgBA,MAAA,CAAeI,4BAAA,GAAgCJ,MAAA,CAAeI,4BAAA,GAA+B,YAAY;EAC7I,OAAO,UAAUC,KAAA,EAAM;IACrB,OAAOA,KAAA;EACT;AACF;;;AChOA,SAASC,KAAA,IAASC,eAAA,EAAiBC,iBAAA,QAAyB;;;ACD5D,SAASC,QAAA,QAAgB;;;ACsFlB,IAAMC,gBAAA,GAAwBC,CAAA,IAA4C;EAC/E,OAAOA,CAAA,IAAK,OAAQA,CAAA,CAA0BC,KAAA,KAAU;AAC1D;;;AD4GO,SAASC,aAAaC,IAAA,EAAcC,aAAA,EAA+B;EACxE,SAASC,cAAA,EAA8B;IACrC,IAAID,aAAA,EAAe;MACjB,IAAIE,QAAA,GAAWF,aAAA,CAAc,GAAArC,SAAO;MACpC,IAAI,CAACuC,QAAA,EAAU;QACb,MAAM,IAAIC,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,CAAC,IAAI,wCAAwC;MAC/H;MACA,OAAAC,aAAA,CAAAA,aAAA;QACET,IAAA;QACAU,OAAA,EAASP,QAAA,CAASO;MAAA,GACd,UAAUP,QAAA,IAAY;QACxBQ,IAAA,EAAMR,QAAA,CAASQ;MACjB,IACI,WAAWR,QAAA,IAAY;QACzBS,KAAA,EAAOT,QAAA,CAASS;MAClB;IAEJ;IACA,OAAO;MACLZ,IAAA;MACAU,OAAA,EAAA9C,SAAA,CAAAQ,MAAA,QAAAyC,SAAA,GAAAjD,SAAA;IACF;EACF;EACAsC,aAAA,CAAcY,QAAA,GAAW,SAAAC,MAAA,CAASf,IAAI;EACtCE,aAAA,CAAcF,IAAA,GAAOA,IAAA;EACrBE,aAAA,CAAcJ,KAAA,GAASkB,MAAA,IAA6CrB,QAAA,CAASqB,MAAM,KAAKA,MAAA,CAAOhB,IAAA,KAASA,IAAA;EACxG,OAAOE,aAAA;AACT;AAKO,SAASe,gBAAgBD,MAAA,EAA0E;EACxG,OAAO,OAAOA,MAAA,KAAW,cAAc,UAAUA,MAAA;EAAA;EAEjDpB,gBAAA,CAAiBoB,MAAa;AAChC;AAKO,SAASE,MAAMF,MAAA,EAKpB;EACA,OAAOrB,QAAA,CAASqB,MAAM,KAAKlD,MAAA,CAAOqD,IAAA,CAAKH,MAAM,EAAEI,KAAA,CAAMC,UAAU;AACjE;AACA,SAASA,WAAWC,GAAA,EAAa;EAC/B,OAAO,CAAC,QAAQ,WAAW,SAAS,MAAM,EAAEC,OAAA,CAAQD,GAAG,IAAI;AAC7D;;;AE7OO,SAASE,WAAWxB,IAAA,EAAgB;EACzC,MAAMyB,SAAA,GAAYzB,IAAA,GAAO,GAAAe,MAAA,CAAGf,IAAI,EAAG0B,KAAA,CAAM,GAAG,IAAI,EAAC;EACjD,MAAMC,UAAA,GAAaF,SAAA,CAAUA,SAAA,CAAUrD,MAAA,GAAS,CAAC,KAAK;EACtD,iDAAA2C,MAAA,CAAgDf,IAAA,IAAQ,SAAS,4GAAAe,MAAA,CACeY,UAAU,gCAAAZ,MAAA,CAA+BY,UAAU;AACrI;AACO,SAASC,uCAAA,EAA0G;EAAA,IAAnEC,OAAA,GAAAjE,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAmD,CAAC;EACzG,IAAIyC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,OAAO,MAAMuB,IAAA,IAAQd,MAAA,IAAUc,IAAA,CAAKd,MAAM;EAC5C;EACA,MAAM;IACJC,eAAA,EAAAc,gBAAA,GAAkBd;EACpB,IAAIY,OAAA;EACJ,OAAO,MAAMC,IAAA,IAAQd,MAAA,IAAU;IAC7B,IAAIe,gBAAA,CAAgBf,MAAM,GAAG;MAC3BgB,OAAA,CAAQC,IAAA,CAAKT,UAAA,CAAWR,MAAA,CAAOhB,IAAI,CAAC;IACtC;IACA,OAAO8B,IAAA,CAAKd,MAAM;EACpB;AACF;;;AC9BA,SAASpE,OAAA,IAAWsF,eAAA,EAAiBC,WAAA,QAAmB;AACjD,SAASC,oBAAoBC,QAAA,EAAkBC,MAAA,EAAgB;EACpE,IAAIC,OAAA,GAAU;EACd,OAAO;IACLC,YAAeC,EAAA,EAAgB;MAC7B,MAAMC,OAAA,GAAUC,IAAA,CAAKC,GAAA,CAAI;MACzB,IAAI;QACF,OAAOH,EAAA,CAAG;MACZ,UAAE;QACA,MAAMI,QAAA,GAAWF,IAAA,CAAKC,GAAA,CAAI;QAC1BL,OAAA,IAAWM,QAAA,GAAWH,OAAA;MACxB;IACF;IACAI,eAAA,EAAiB;MACf,IAAIP,OAAA,GAAUF,QAAA,EAAU;QACtBL,OAAA,CAAQC,IAAA,IAAAlB,MAAA,CAAQuB,MAAM,YAAAvB,MAAA,CAASwB,OAAO,sDAAAxB,MAAA,CAAmDsB,QAAQ,iTAE7B;MACtE;IACF;EACF;AACF;AAIO,IAAMU,KAAA,GAAN,MAAMC,MAAA,SAAyD1E,KAAA,CAAqB;EAGzF2E,YAAA,EAA6B;IAC3B,MAAM,GAAArF,SAAQ;IACdE,MAAA,CAAOoF,cAAA,CAAe,MAAMF,MAAA,CAAMG,SAAS;EAC7C;EACA,YAAqBC,MAAA,CAAOC,OAAO,IAAI;IACrC,OAAOL,MAAA;EACT;EAISjC,OAAA,EAAsB;IAAA,SAAAuC,KAAA,GAAA1F,SAAA,CAAAQ,MAAA,EAAZmF,GAAA,OAAAjF,KAAA,CAAAgF,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;MAAAD,GAAA,CAAAC,KAAA,IAAA5F,SAAA,CAAA4F,KAAA;IAAA;IACjB,OAAO,MAAMzC,MAAA,CAAO3B,KAAA,CAAM,MAAMmE,GAAG;EACrC;EAIAE,QAAA,EAAuB;IAAA,SAAAC,KAAA,GAAA9F,SAAA,CAAAQ,MAAA,EAAZmF,GAAA,OAAAjF,KAAA,CAAAoF,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAAJ,GAAA,CAAAI,KAAA,IAAA/F,SAAA,CAAA+F,KAAA;IAAA;IACT,IAAIJ,GAAA,CAAInF,MAAA,KAAW,KAAKE,KAAA,CAAMsF,OAAA,CAAQL,GAAA,CAAI,CAAC,CAAC,GAAG;MAC7C,OAAO,IAAIP,MAAA,CAAM,GAAGO,GAAA,CAAI,CAAC,EAAExC,MAAA,CAAO,IAAI,CAAC;IACzC;IACA,OAAO,IAAIiC,MAAA,CAAM,GAAGO,GAAA,CAAIxC,MAAA,CAAO,IAAI,CAAC;EACtC;AACF;AACO,SAAS8C,gBAAmBC,GAAA,EAAQ;EACzC,OAAO3B,WAAA,CAAY2B,GAAG,IAAI5B,eAAA,CAAgB4B,GAAA,EAAK,MAAM,CAAC,CAAC,IAAIA,GAAA;AAC7D;AASO,SAASC,oBAAyCC,GAAA,EAAgC1C,GAAA,EAAQ2C,OAAA,EAA2B;EAC1H,IAAID,GAAA,CAAIE,GAAA,CAAI5C,GAAG,GAAG,OAAO0C,GAAA,CAAIG,GAAA,CAAI7C,GAAG;EACpC,OAAO0C,GAAA,CAAII,GAAA,CAAI9C,GAAA,EAAK2C,OAAA,CAAQ3C,GAAG,CAAC,EAAE6C,GAAA,CAAI7C,GAAG;AAC3C;;;ACtDO,SAAS+C,mBAAmBnG,KAAA,EAAyB;EAC1D,OAAO,OAAOA,KAAA,KAAU,YAAYA,KAAA,IAAS,QAAQJ,MAAA,CAAOwG,QAAA,CAASpG,KAAK;AAC5E;AACO,SAASqG,kBAAkBC,WAAA,EAA8BC,WAAA,EAAsCC,GAAA,EAAU;EAC9G,MAAMC,iBAAA,GAAoBC,eAAA,CAAgBJ,WAAA,EAAaC,WAAA,EAAaC,GAAG;EACvE,OAAO;IACLG,gBAAA,EAAkB;MAChB,OAAOA,eAAA,CAAgBL,WAAA,EAAaC,WAAA,EAAaE,iBAAA,EAAmBD,GAAG;IACzE;EACF;AACF;AAKA,SAASE,gBAAgBJ,WAAA,EAAgK;EAAA,IAAlIC,WAAA,GAAA7G,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAA2B,EAAC;EAAA,IAAG8G,GAAA,GAAA9G,SAAA,CAAAQ,MAAA,OAAAR,SAAA,MAAAiD,SAAA;EAAA,IAA0BiE,IAAA,GAAAlH,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAe;EAAA,IAAImH,cAAA,GAAAnH,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAA2C,mBAAIoH,GAAA,CAAI;EACpL,MAAMC,OAAA,GAAoC;IACxC/G,KAAA,EAAOwG;EACT;EACA,IAAI,CAACF,WAAA,CAAYE,GAAG,KAAK,CAACK,cAAA,CAAeb,GAAA,CAAIQ,GAAG,GAAG;IACjDK,cAAA,CAAeG,GAAA,CAAIR,GAAG;IACtBO,OAAA,CAAQE,QAAA,GAAW,CAAC;IACpB,WAAW7D,GAAA,IAAOoD,GAAA,EAAK;MACrB,MAAMU,SAAA,GAAYN,IAAA,GAAOA,IAAA,GAAO,MAAMxD,GAAA,GAAMA,GAAA;MAC5C,IAAImD,WAAA,CAAYrG,MAAA,IAAUqG,WAAA,CAAYlD,OAAA,CAAQ6D,SAAS,MAAM,IAAI;QAC/D;MACF;MACAH,OAAA,CAAQE,QAAA,CAAS7D,GAAG,IAAIsD,eAAA,CAAgBJ,WAAA,EAAaC,WAAA,EAAaC,GAAA,CAAIpD,GAAG,GAAG8D,SAAS;IACvF;EACF;EACA,OAAOH,OAAA;AACT;AACA,SAASJ,gBAAgBL,WAAA,EAGvB;EAAA,IAHqDa,YAAA,GAAAzH,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAA4B,EAAC;EAAA,IAAG0H,eAAA,GAAA1H,SAAA,CAAAQ,MAAA,OAAAR,SAAA,MAAAiD,SAAA;EAAA,IAAkC6D,GAAA,GAAA9G,SAAA,CAAAQ,MAAA,OAAAR,SAAA,MAAAiD,SAAA;EAAA,IAAU0E,aAAA,GAAA3H,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAyB;EAAA,IAAOkH,IAAA,GAAAlH,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAe;EAIhL,MAAM4H,OAAA,GAAUF,eAAA,GAAkBA,eAAA,CAAgBpH,KAAA,GAAQ;EAC1D,MAAMuH,OAAA,GAAUD,OAAA,KAAYd,GAAA;EAC5B,IAAIa,aAAA,IAAiB,CAACE,OAAA,IAAW,CAACC,MAAA,CAAOC,KAAA,CAAMjB,GAAG,GAAG;IACnD,OAAO;MACLkB,UAAA,EAAY;MACZd;IACF;EACF;EACA,IAAIN,WAAA,CAAYgB,OAAO,KAAKhB,WAAA,CAAYE,GAAG,GAAG;IAC5C,OAAO;MACLkB,UAAA,EAAY;IACd;EACF;EAGA,MAAMC,YAAA,GAAwC,CAAC;EAC/C,SAASvE,GAAA,IAAOgE,eAAA,CAAgBH,QAAA,EAAU;IACxCU,YAAA,CAAavE,GAAG,IAAI;EACtB;EACA,SAASA,GAAA,IAAOoD,GAAA,EAAK;IACnBmB,YAAA,CAAavE,GAAG,IAAI;EACtB;EACA,MAAMwE,eAAA,GAAkBT,YAAA,CAAajH,MAAA,GAAS;EAC9C,SAASkD,GAAA,IAAOuE,YAAA,EAAc;IAC5B,MAAME,UAAA,GAAajB,IAAA,GAAOA,IAAA,GAAO,MAAMxD,GAAA,GAAMA,GAAA;IAC7C,IAAIwE,eAAA,EAAiB;MACnB,MAAME,UAAA,GAAaX,YAAA,CAAaY,IAAA,CAAKC,OAAA,IAAW;QAC9C,IAAIA,OAAA,YAAmBC,MAAA,EAAQ;UAC7B,OAAOD,OAAA,CAAQE,IAAA,CAAKL,UAAU;QAChC;QACA,OAAOA,UAAA,KAAeG,OAAA;MACxB,CAAC;MACD,IAAIF,UAAA,EAAY;QACd;MACF;IACF;IACA,MAAMK,MAAA,GAASxB,eAAA,CAAgBL,WAAA,EAAaa,YAAA,EAAcC,eAAA,CAAgBH,QAAA,CAAS7D,GAAG,GAAGoD,GAAA,CAAIpD,GAAG,GAAGmE,OAAA,EAASM,UAAU;IACtH,IAAIM,MAAA,CAAOT,UAAA,EAAY;MACrB,OAAOS,MAAA;IACT;EACF;EACA,OAAO;IACLT,UAAA,EAAY;EACd;AACF;AAmCO,SAASU,wCAAA,EAA4G;EAAA,IAApEzE,OAAA,GAAAjE,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAoD,CAAC;EAC3G,IAAIyC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,OAAO,MAAMuB,IAAA,IAAQd,MAAA,IAAUc,IAAA,CAAKd,MAAM;EAC5C,OAAO;IACL,IAASuF,UAAA,GAAT,SAAAC,CAAmB9B,GAAA,EAAU+B,UAAA,EAA6BC,MAAA,EAA0BC,QAAA,EAAmC;QACrH,OAAOC,IAAA,CAAKJ,SAAA,CAAU9B,GAAA,EAAKmC,aAAA,CAAaJ,UAAA,EAAYE,QAAQ,GAAGD,MAAM;MACvE;MACSG,aAAA,GAAT,SAAAC,CAAsBL,UAAA,EAA6BE,QAAA,EAA2C;QAC5F,IAAII,KAAA,GAAe,EAAC;UAClB5F,IAAA,GAAc,EAAC;QACjB,IAAI,CAACwF,QAAA,EAAUA,QAAA,GAAW,SAAAA,CAAUK,CAAA,EAAW9I,KAAA,EAAY;UACzD,IAAI6I,KAAA,CAAM,CAAC,MAAM7I,KAAA,EAAO,OAAO;UAC/B,OAAO,iBAAiBiD,IAAA,CAAK8F,KAAA,CAAM,GAAGF,KAAA,CAAMxF,OAAA,CAAQrD,KAAK,CAAC,EAAEgJ,IAAA,CAAK,GAAG,IAAI;QAC1E;QACA,OAAO,UAAqB5F,GAAA,EAAapD,KAAA,EAAY;UACnD,IAAI6I,KAAA,CAAM3I,MAAA,GAAS,GAAG;YACpB,IAAI+I,OAAA,GAAUJ,KAAA,CAAMxF,OAAA,CAAQ,IAAI;YAChC,CAAC4F,OAAA,GAAUJ,KAAA,CAAMK,MAAA,CAAOD,OAAA,GAAU,CAAC,IAAIJ,KAAA,CAAMM,IAAA,CAAK,IAAI;YACtD,CAACF,OAAA,GAAUhG,IAAA,CAAKiG,MAAA,CAAOD,OAAA,EAASG,QAAA,EAAUhG,GAAG,IAAIH,IAAA,CAAKkG,IAAA,CAAK/F,GAAG;YAC9D,IAAI,CAACyF,KAAA,CAAMxF,OAAA,CAAQrD,KAAK,GAAGA,KAAA,GAAQyI,QAAA,CAAUY,IAAA,CAAK,MAAMjG,GAAA,EAAKpD,KAAK;UACpE,OAAO6I,KAAA,CAAMM,IAAA,CAAKnJ,KAAK;UACvB,OAAOuI,UAAA,IAAc,OAAOvI,KAAA,GAAQuI,UAAA,CAAWc,IAAA,CAAK,MAAMjG,GAAA,EAAKpD,KAAK;QACtE;MACF;IAnBS,IAAAsI,SAAA,GAAAD,UAAA;MAGAO,YAAA,GAAAD,aAAA;IAiBT,IAAI;MACFrC,WAAA,GAAcH,kBAAA;MACdgB,YAAA;MACAmC,SAAA,GAAY;IACd,IAAI3F,OAAA;IACJ,MAAM4F,KAAA,GAAQlD,iBAAA,CAAkBmD,IAAA,CAAK,MAAMlD,WAAA,EAAaa,YAAY;IACpE,OAAOsC,IAAA,IAED;MAAA,IAFE;QACNC;MACF,IAAAD,IAAA;MACE,IAAIE,KAAA,GAAQD,QAAA,CAAS;MACrB,IAAIE,OAAA,GAAUL,KAAA,CAAMI,KAAK;MACzB,IAAIxB,MAAA;MACJ,OAAOvE,IAAA,IAAQd,MAAA,IAAU;QACvB,MAAM+G,YAAA,GAAe3F,mBAAA,CAAoBoF,SAAA,EAAW,mCAAmC;QACvFO,YAAA,CAAavF,WAAA,CAAY,MAAM;UAC7BqF,KAAA,GAAQD,QAAA,CAAS;UACjBvB,MAAA,GAASyB,OAAA,CAAQjD,eAAA,CAAgB;UAEjCiD,OAAA,GAAUL,KAAA,CAAMI,KAAK;UACrB,IAAIxB,MAAA,CAAOT,UAAA,EAAY;YACrB,MAAM,IAAIxF,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,EAAE,sEAAAO,MAAA,CAAsEsF,MAAA,CAAOvB,IAAA,IAAQ,EAAE,8GAA2G;UACtR;QACF,CAAC;QACD,MAAMkD,gBAAA,GAAmBlG,IAAA,CAAKd,MAAM;QACpC+G,YAAA,CAAavF,WAAA,CAAY,MAAM;UAC7BqF,KAAA,GAAQD,QAAA,CAAS;UACjBvB,MAAA,GAASyB,OAAA,CAAQjD,eAAA,CAAgB;UAEjCiD,OAAA,GAAUL,KAAA,CAAMI,KAAK;UACrB,IAAIxB,MAAA,CAAOT,UAAA,EAAY;YACrB,MAAM,IAAIxF,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,qEAAAO,MAAA,CAAqEsF,MAAA,CAAOvB,IAAA,IAAQ,EAAE,0DAAA/D,MAAA,CAAuDwF,UAAA,CAAUvF,MAAM,CAAC,yEAAsE;UACzT;QACF,CAAC;QACD+G,YAAA,CAAajF,cAAA,CAAe;QAC5B,OAAOkF,gBAAA;MACT;IACF;EACF;AACF;;;AC3LA,SAASrI,QAAA,IAAAsI,SAAA,EAAUlJ,aAAA,QAAqB;AAYjC,SAASmJ,QAAQpE,GAAA,EAAU;EAChC,MAAM9D,IAAA,GAAO,OAAO8D,GAAA;EACpB,OAAOA,GAAA,IAAO,QAAQ9D,IAAA,KAAS,YAAYA,IAAA,KAAS,aAAaA,IAAA,KAAS,YAAY1B,KAAA,CAAMsF,OAAA,CAAQE,GAAG,KAAK/E,aAAA,CAAc+E,GAAG;AAC/H;AAUO,SAASqE,yBAAyBjK,KAAA,EAAmO;EAAA,IAAnN4G,IAAA,GAAAlH,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAe;EAAA,IAAIwK,cAAA,GAAAxK,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAA8CsK,OAAA;EAAA,IAASG,UAAA,GAAAzK,SAAA,CAAAQ,MAAA,OAAAR,SAAA,MAAAiD,SAAA;EAAA,IAAkDwE,YAAA,GAAAzH,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAA4B,EAAC;EAAA,IAAG0K,KAAA,GAAA1K,SAAA,CAAAQ,MAAA,OAAAR,SAAA,MAAAiD,SAAA;EACnN,IAAI0H,uBAAA;EACJ,IAAI,CAACH,cAAA,CAAelK,KAAK,GAAG;IAC1B,OAAO;MACLsK,OAAA,EAAS1D,IAAA,IAAQ;MACjB5G;IACF;EACF;EACA,IAAI,OAAOA,KAAA,KAAU,YAAYA,KAAA,KAAU,MAAM;IAC/C,OAAO;EACT;EACA,IAAIoK,KAAA,aAAAA,KAAA,eAAAA,KAAA,CAAOpE,GAAA,CAAIhG,KAAK,GAAG,OAAO;EAC9B,MAAMuK,OAAA,GAAUJ,UAAA,IAAc,OAAOA,UAAA,CAAWnK,KAAK,IAAIJ,MAAA,CAAO2K,OAAA,CAAQvK,KAAK;EAC7E,MAAM4H,eAAA,GAAkBT,YAAA,CAAajH,MAAA,GAAS;EAC9C,WAAW,CAACkD,GAAA,EAAKoH,WAAW,KAAKD,OAAA,EAAS;IACxC,MAAM1C,UAAA,GAAajB,IAAA,GAAOA,IAAA,GAAO,MAAMxD,GAAA,GAAMA,GAAA;IAC7C,IAAIwE,eAAA,EAAiB;MACnB,MAAME,UAAA,GAAaX,YAAA,CAAaY,IAAA,CAAKC,OAAA,IAAW;QAC9C,IAAIA,OAAA,YAAmBC,MAAA,EAAQ;UAC7B,OAAOD,OAAA,CAAQE,IAAA,CAAKL,UAAU;QAChC;QACA,OAAOA,UAAA,KAAeG,OAAA;MACxB,CAAC;MACD,IAAIF,UAAA,EAAY;QACd;MACF;IACF;IACA,IAAI,CAACoC,cAAA,CAAeM,WAAW,GAAG;MAChC,OAAO;QACLF,OAAA,EAASzC,UAAA;QACT7H,KAAA,EAAOwK;MACT;IACF;IACA,IAAI,OAAOA,WAAA,KAAgB,UAAU;MACnCH,uBAAA,GAA0BJ,wBAAA,CAAyBO,WAAA,EAAa3C,UAAA,EAAYqC,cAAA,EAAgBC,UAAA,EAAYhD,YAAA,EAAciD,KAAK;MAC3H,IAAIC,uBAAA,EAAyB;QAC3B,OAAOA,uBAAA;MACT;IACF;EACF;EACA,IAAID,KAAA,IAASK,cAAA,CAAezK,KAAK,GAAGoK,KAAA,CAAMpD,GAAA,CAAIhH,KAAK;EACnD,OAAO;AACT;AACO,SAASyK,eAAezK,KAAA,EAAe;EAC5C,IAAI,CAACJ,MAAA,CAAOwG,QAAA,CAASpG,KAAK,GAAG,OAAO;EACpC,WAAWwK,WAAA,IAAe5K,MAAA,CAAO8K,MAAA,CAAO1K,KAAK,GAAG;IAC9C,IAAI,OAAOwK,WAAA,KAAgB,YAAYA,WAAA,KAAgB,MAAM;IAC7D,IAAI,CAACC,cAAA,CAAeD,WAAW,GAAG,OAAO;EAC3C;EACA,OAAO;AACT;AAwEO,SAASG,2CAAA,EAAkH;EAAA,IAAvEhH,OAAA,GAAAjE,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAuD,CAAC;EACjH,IAAIyC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,OAAO,MAAMuB,IAAA,IAAQd,MAAA,IAAUc,IAAA,CAAKd,MAAM;EAC5C,OAAO;IACL,MAAM;MACJoH,cAAA,GAAiBF,OAAA;MACjBG,UAAA;MACAS,cAAA,GAAiB,EAAC;MAClBC,kBAAA,GAAqB,CAAC,YAAY,oBAAoB;MACtD1D,YAAA,GAAe,EAAC;MAChBmC,SAAA,GAAY;MACZwB,WAAA,GAAc;MACdC,aAAA,GAAgB;MAChBC,YAAA,GAAe;IACjB,IAAIrH,OAAA;IACJ,MAAMyG,KAAA,GAAqC,CAACY,YAAA,IAAgBC,OAAA,GAAU,mBAAIA,OAAA,CAAQ,IAAI;IACtF,OAAOC,QAAA,IAAYtH,IAAA,IAAQd,MAAA,IAAU;MACnC,IAAI,CAACiH,SAAA,CAASjH,MAAM,GAAG;QACrB,OAAOc,IAAA,CAAKd,MAAM;MACpB;MACA,MAAMqF,MAAA,GAASvE,IAAA,CAAKd,MAAM;MAC1B,MAAM+G,YAAA,GAAe3F,mBAAA,CAAoBoF,SAAA,EAAW,sCAAsC;MAC1F,IAAI,CAACyB,aAAA,IAAiB,EAAEH,cAAA,CAAe1K,MAAA,IAAU0K,cAAA,CAAevH,OAAA,CAAQP,MAAA,CAAOhB,IAAW,MAAM,KAAK;QACnG+H,YAAA,CAAavF,WAAA,CAAY,MAAM;UAC7B,MAAM6G,+BAAA,GAAkClB,wBAAA,CAAyBnH,MAAA,EAAQ,IAAIoH,cAAA,EAAgBC,UAAA,EAAYU,kBAAA,EAAoBT,KAAK;UAClI,IAAIe,+BAAA,EAAiC;YACnC,MAAM;cACJb,OAAA;cACAtK;YACF,IAAImL,+BAAA;YACJrH,OAAA,CAAQpB,KAAA,sEAAAG,MAAA,CAA4EyH,OAAO,gBAActK,KAAA,EAAO,4DAA4D8C,MAAA,EAAQ,yIAAyI,6HAA6H;UAC5b;QACF,CAAC;MACH;MACA,IAAI,CAACgI,WAAA,EAAa;QAChBjB,YAAA,CAAavF,WAAA,CAAY,MAAM;UAC7B,MAAMqF,KAAA,GAAQuB,QAAA,CAASxB,QAAA,CAAS;UAChC,MAAM0B,8BAAA,GAAiCnB,wBAAA,CAAyBN,KAAA,EAAO,IAAIO,cAAA,EAAgBC,UAAA,EAAYhD,YAAA,EAAciD,KAAK;UAC1H,IAAIgB,8BAAA,EAAgC;YAClC,MAAM;cACJd,OAAA;cACAtK;YACF,IAAIoL,8BAAA;YACJtH,OAAA,CAAQpB,KAAA,sEAAAG,MAAA,CAA4EyH,OAAO,gBAActK,KAAA,gEAAA6C,MAAA,CAC1DC,MAAA,CAAOhB,IAAI,sIACyD;UACrH;QACF,CAAC;QACD+H,YAAA,CAAajF,cAAA,CAAe;MAC9B;MACA,OAAOuD,MAAA;IACT;EACF;AACF;;;AN3LA,SAASkD,UAAUC,CAAA,EAAsB;EACvC,OAAO,OAAOA,CAAA,KAAM;AACtB;AAuBO,IAAMC,yBAAA,GAA4BA,CAAA,KAAyC,SAASC,qBAAqB7H,OAAA,EAAS;EACvH,MAAM;IACJrC,KAAA,GAAQ;IACRmK,cAAA,GAAiB;IACjBC,iBAAA,GAAoB;IACpBC,kBAAA,GAAqB;EACvB,IAAIhI,OAAA,aAAAA,OAAA,cAAAA,OAAA,GAAW,CAAC;EAChB,IAAIiI,eAAA,GAAkB,IAAI/G,KAAA,CAAoB;EAC9C,IAAIvD,KAAA,EAAO;IACT,IAAI+J,SAAA,CAAU/J,KAAK,GAAG;MACpBsK,eAAA,CAAgBzC,IAAA,CAAK5H,eAAe;IACtC,OAAO;MACLqK,eAAA,CAAgBzC,IAAA,CAAK3H,iBAAA,CAAkBF,KAAA,CAAMuK,aAAa,CAAC;IAC7D;EACF;EACA,IAAI1J,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,IAAIoJ,cAAA,EAAgB;MAElB,IAAIK,gBAAA,GAA6D,CAAC;MAClE,IAAI,CAACT,SAAA,CAAUI,cAAc,GAAG;QAC9BK,gBAAA,GAAmBL,cAAA;MACrB;MACAG,eAAA,CAAgBG,OAAA,CAAQ3D,uCAAA,CAAwC0D,gBAAgB,CAAC;IAEnF;IACA,IAAIJ,iBAAA,EAAmB;MACrB,IAAIM,mBAAA,GAAmE,CAAC;MACxE,IAAI,CAACX,SAAA,CAAUK,iBAAiB,GAAG;QACjCM,mBAAA,GAAsBN,iBAAA;MACxB;MACAE,eAAA,CAAgBzC,IAAA,CAAKwB,0CAAA,CAA2CqB,mBAAmB,CAAC;IACtF;IACA,IAAIL,kBAAA,EAAoB;MACtB,IAAIM,oBAAA,GAAgE,CAAC;MACrE,IAAI,CAACZ,SAAA,CAAUM,kBAAkB,GAAG;QAClCM,oBAAA,GAAuBN,kBAAA;MACzB;MACAC,eAAA,CAAgBG,OAAA,CAAQrI,sCAAA,CAAuCuI,oBAAoB,CAAC;IACtF;EACF;EACA,OAAOL,eAAA;AACT;;;AO/EO,IAAMM,gBAAA,GAAmB;AACzB,IAAMC,kBAAA,GAAqBA,CAAA,KAAW3J,OAAA,KAGvC;EACJA,OAAA;EACAC,IAAA,EAAM;IACJ,CAACyJ,gBAAgB,GAAG;EACtB;AACF;AACA,IAAME,oBAAA,GAAwBC,OAAA,IAAoB;EAChD,OAAQC,MAAA,IAAuB;IAC7BC,UAAA,CAAWD,MAAA,EAAQD,OAAO;EAC5B;AACF;AAmCO,IAAMG,iBAAA,GAAoB,SAAAA,CAAA;EAAA,IAAC7I,OAAA,GAAAjE,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAA4B;IAC5DoC,IAAA,EAAM;EACR;EAAA,OAAqB8B,IAAA,IAAQ,YAAa;IACxC,MAAM6I,KAAA,GAAQ7I,IAAA,CAAK,GAAAlE,SAAO;IAC1B,IAAIgN,SAAA,GAAY;IAChB,IAAIC,uBAAA,GAA0B;IAC9B,IAAIC,kBAAA,GAAqB;IACzB,MAAMC,SAAA,GAAY,mBAAI/F,GAAA,CAAgB;IACtC,MAAMgG,aAAA,GAAgBnJ,OAAA,CAAQ7B,IAAA,KAAS,SAASiL,cAAA,GAAiBpJ,OAAA,CAAQ7B,IAAA,KAAS;IAAA;IAElF,OAAOd,MAAA,KAAW,eAAeA,MAAA,CAAOgM,qBAAA,GAAwBhM,MAAA,CAAOgM,qBAAA,GAAwBZ,oBAAA,CAAqB,EAAE,IAAIzI,OAAA,CAAQ7B,IAAA,KAAS,aAAa6B,OAAA,CAAQsJ,iBAAA,GAAoBb,oBAAA,CAAqBzI,OAAA,CAAQ0I,OAAO;IACxN,MAAMa,eAAA,GAAkBA,CAAA,KAAM;MAG5BN,kBAAA,GAAqB;MACrB,IAAID,uBAAA,EAAyB;QAC3BA,uBAAA,GAA0B;QAC1BE,SAAA,CAAUM,OAAA,CAAQC,CAAA,IAAKA,CAAA,CAAE,CAAC;MAC5B;IACF;IACA,OAAOxN,MAAA,CAAOC,MAAA,CAAO,CAAC,GAAG4M,KAAA,EAAO;MAAA;MAAA;MAG9BY,UAAUC,SAAA,EAAsB;QAK9B,MAAMC,eAAA,GAAmCA,CAAA,KAAMb,SAAA,IAAaY,SAAA,CAAS;QACrE,MAAME,WAAA,GAAcf,KAAA,CAAMY,SAAA,CAAUE,eAAe;QACnDV,SAAA,CAAU7F,GAAA,CAAIsG,SAAQ;QACtB,OAAO,MAAM;UACXE,WAAA,CAAY;UACZX,SAAA,CAAUY,MAAA,CAAOH,SAAQ;QAC3B;MACF;MAAA;MAAA;MAGAI,SAAS5K,MAAA,EAAa;QACpB,IAAI;UAAA,IAAA6K,YAAA;UAGFjB,SAAA,GAAY,EAAC5J,MAAA,aAAAA,MAAA,gBAAA6K,YAAA,GAAA7K,MAAA,CAAQL,IAAA,cAAAkL,YAAA,eAARA,YAAA,CAAezB,gBAAgB;UAG5CS,uBAAA,GAA0B,CAACD,SAAA;UAC3B,IAAIC,uBAAA,EAAyB;YAI3B,IAAI,CAACC,kBAAA,EAAoB;cACvBA,kBAAA,GAAqB;cACrBE,aAAA,CAAcI,eAAe;YAC/B;UACF;UAOA,OAAOT,KAAA,CAAMiB,QAAA,CAAS5K,MAAM;QAC9B,UAAE;UAEA4J,SAAA,GAAY;QACd;MACF;IACF,CAAC;EACH;AAAA;;;AC1GO,IAAMkB,wBAAA,GAAyDC,kBAAA,IAEvC,SAASC,oBAAoBnK,OAAA,EAAS;EACnE,MAAM;IACJoK,SAAA,GAAY;EACd,IAAIpK,OAAA,aAAAA,OAAA,cAAAA,OAAA,GAAW,CAAC;EAChB,IAAIqK,aAAA,GAAgB,IAAInJ,KAAA,CAAuBgJ,kBAAkB;EACjE,IAAIE,SAAA,EAAW;IACbC,aAAA,CAAc7E,IAAA,CAAKqD,iBAAA,CAAkB,OAAOuB,SAAA,KAAc,WAAWA,SAAA,GAAY,MAAS,CAAC;EAC7F;EACA,OAAOC,aAAA;AACT;;;AV8DO,SAASC,eAEYtK,OAAA,EAAuE;EACjG,MAAM6H,oBAAA,GAAuBD,yBAAA,CAA6B;EAC1D,MAAM;IACJ2C,OAAA,GAAU;IACVC,UAAA;IACAC,QAAA,GAAW;IACXC,wBAAA,GAA2B;IAC3BC,cAAA,GAAiB;IACjBC,SAAA,GAAY;EACd,IAAI5K,OAAA,IAAW,CAAC;EAChB,IAAI6K,WAAA;EACJ,IAAI,OAAON,OAAA,KAAY,YAAY;IACjCM,WAAA,GAAcN,OAAA;EAChB,WAAWpN,cAAA,CAAcoN,OAAO,GAAG;IACjCM,WAAA,GAAc5N,eAAA,CAAgBsN,OAAO;EACvC,OAAO;IACL,MAAM,IAAIhM,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,CAAC,IAAI,0HAA0H;EACjN;EACA,IAAIH,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB8L,UAAA,IAAc,OAAOA,UAAA,KAAe,YAAY;IAC3F,MAAM,IAAIjM,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,CAAC,IAAI,uCAAuC;EAC/H;EACA,IAAImM,eAAA;EACJ,IAAI,OAAON,UAAA,KAAe,YAAY;IACpCM,eAAA,GAAkBN,UAAA,CAAW3C,oBAAoB;IACjD,IAAIrJ,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB,CAACjC,KAAA,CAAMsF,OAAA,CAAQ+I,eAAe,GAAG;MAC5E,MAAM,IAAIvM,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,CAAC,IAAI,mFAAmF;IAC3K;EACF,OAAO;IACLmM,eAAA,GAAkBjD,oBAAA,CAAqB;EACzC;EACA,IAAIrJ,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgBoM,eAAA,CAAgB1G,IAAA,CAAM2G,IAAA,IAAc,OAAOA,IAAA,KAAS,UAAU,GAAG;IAC5G,MAAM,IAAIxM,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,CAAC,IAAI,+DAA+D;EACvJ;EACA,IAAIH,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgBgM,wBAAA,EAA0B;IACrE,IAAIM,oBAAA,GAAuB,mBAAI7H,GAAA,CAAwB;IACvD2H,eAAA,CAAgBtB,OAAA,CAAQyB,WAAA,IAAc;MACpC,IAAID,oBAAA,CAAqB3I,GAAA,CAAI4I,WAAU,GAAG;QACxC,MAAM,IAAI1M,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,mHAAmH;MAC5M;MACAqM,oBAAA,CAAqB3H,GAAA,CAAI4H,WAAU;IACrC,CAAC;EACH;EACA,IAAIC,YAAA,GAAelO,QAAA;EACnB,IAAIyN,QAAA,EAAU;IACZS,YAAA,GAAe9N,mBAAA,CAAAwB,aAAA;MAAoB;MAEjCuM,KAAA,EAAO3M,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa;IAAA,GAC5B,OAAO+L,QAAA,KAAa,YAAYA,QAAA,CACrC;EACH;EACA,MAAMP,kBAAA,GAAqBrN,eAAA,CAAgB,GAAGiO,eAAe;EAC7D,MAAMX,mBAAA,GAAsBF,wBAAA,CAA4BC,kBAAkB;EAC1E,IAAI1L,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgBkM,SAAA,IAAa,OAAOA,SAAA,KAAc,YAAY;IACzF,MAAM,IAAIrM,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,CAAC,IAAI,sCAAsC;EAC9H;EACA,IAAIyM,cAAA,GAAiB,OAAOR,SAAA,KAAc,aAAaA,SAAA,CAAUT,mBAAmB,IAAIA,mBAAA,CAAoB;EAC5G,IAAI3L,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB,CAACjC,KAAA,CAAMsF,OAAA,CAAQqJ,cAAc,GAAG;IAC3E,MAAM,IAAI7M,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,CAAC,IAAI,2CAA2C;EACnI;EACA,IAAIH,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB0M,cAAA,CAAehH,IAAA,CAAM2G,IAAA,IAAc,OAAOA,IAAA,KAAS,UAAU,GAAG;IAC3G,MAAM,IAAIxM,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,CAAC,IAAI,6DAA6D;EACrJ;EACA,IAAIH,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgBoM,eAAA,CAAgBvO,MAAA,IAAU,CAAC6O,cAAA,CAAeC,QAAA,CAASnB,kBAAkB,GAAG;IACnH/J,OAAA,CAAQpB,KAAA,CAAM,kIAAkI;EAClJ;EACA,MAAMuM,gBAAA,GAAuCJ,YAAA,CAAa,GAAGE,cAAc;EAC3E,OAAOtO,WAAA,CAAY+N,WAAA,EAAaF,cAAA,EAAqBW,gBAAgB;AACvE;;;AWxJA,SAASvQ,OAAA,IAAWwQ,gBAAA,EAAiBlQ,OAAA,IAAAmQ,QAAA,EAASlL,WAAA,IAAAmL,YAAA,QAAmB;;;ACwG1D,SAASC,8BAAiCC,eAAA,EAAmK;EAClN,MAAMC,UAAA,GAAmC,CAAC;EAC1C,MAAMC,cAAA,GAAwD,EAAC;EAC/D,IAAIC,kBAAA;EACJ,MAAMC,OAAA,GAAU;IACdC,QAAQC,mBAAA,EAAuD1B,OAAA,EAAyB;MACtF,IAAI/L,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QAMzC,IAAImN,cAAA,CAAetP,MAAA,GAAS,GAAG;UAC7B,MAAM,IAAIgC,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,EAAE,IAAI,6EAA6E;QACrK;QACA,IAAImN,kBAAA,EAAoB;UACtB,MAAM,IAAIvN,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,iFAAiF;QAC1K;MACF;MACA,MAAMR,IAAA,GAAO,OAAO8N,mBAAA,KAAwB,WAAWA,mBAAA,GAAsBA,mBAAA,CAAoB9N,IAAA;MACjG,IAAI,CAACA,IAAA,EAAM;QACT,MAAM,IAAII,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,8DAA8D;MACvJ;MACA,IAAIR,IAAA,IAAQyN,UAAA,EAAY;QACtB,MAAM,IAAIrN,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,sFAAAO,MAAA,CAA2Ff,IAAI,MAAG;MACvL;MACAyN,UAAA,CAAWzN,IAAI,IAAIoM,OAAA;MACnB,OAAOwB,OAAA;IACT;IACAG,WAAcC,OAAA,EAAuB5B,OAAA,EAA4D;MAC/F,IAAI/L,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QACzC,IAAIoN,kBAAA,EAAoB;UACtB,MAAM,IAAIvN,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,oFAAoF;QAC7K;MACF;MACAkN,cAAA,CAAerG,IAAA,CAAK;QAClB2G,OAAA;QACA5B;MACF,CAAC;MACD,OAAOwB,OAAA;IACT;IACAK,eAAe7B,OAAA,EAAiC;MAC9C,IAAI/L,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QACzC,IAAIoN,kBAAA,EAAoB;UACtB,MAAM,IAAIvN,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,kDAAkD;QAC3I;MACF;MACAmN,kBAAA,GAAqBvB,OAAA;MACrB,OAAOwB,OAAA;IACT;EACF;EACAJ,eAAA,CAAgBI,OAAO;EACvB,OAAO,CAACH,UAAA,EAAYC,cAAA,EAAgBC,kBAAkB;AACxD;;;ADzGA,SAASO,gBAAmB1E,CAAA,EAA0B;EACpD,OAAO,OAAOA,CAAA,KAAM;AACtB;AAqEO,SAAS2E,cAA0CC,YAAA,EAA6BC,oBAAA,EAAiG;EACtL,IAAIhO,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,IAAI,OAAO8N,oBAAA,KAAyB,UAAU;MAC5C,MAAM,IAAIjO,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,CAAC,IAAI,8JAA8J;IACrP;EACF;EACA,IAAI,CAACiN,UAAA,EAAYa,mBAAA,EAAqBC,uBAAuB,IAAIhB,6BAAA,CAA8Bc,oBAAoB;EAGnH,IAAIG,eAAA;EACJ,IAAIN,eAAA,CAAgBE,YAAY,GAAG;IACjCI,eAAA,GAAkBA,CAAA,KAAM3K,eAAA,CAAgBuK,YAAA,CAAa,CAAC;EACxD,OAAO;IACL,MAAMK,kBAAA,GAAqB5K,eAAA,CAAgBuK,YAAY;IACvDI,eAAA,GAAkBA,CAAA,KAAMC,kBAAA;EAC1B;EACA,SAASrC,QAAA,EAAmD;IAAA,IAA3CvE,KAAA,GAAAjK,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAQ4Q,eAAA,CAAgB;IAAA,IAAGxN,MAAA,GAAApD,SAAA,CAAAQ,MAAA,OAAAR,SAAA,MAAAiD,SAAA;IAC1C,IAAI6N,YAAA,GAAe,CAACjB,UAAA,CAAWzM,MAAA,CAAOhB,IAAI,GAAG,GAAGsO,mBAAA,CAAoBK,MAAA,CAAOC,KAAA;MAAA,IAAC;QAC1EZ;MACF,IAAAY,KAAA;MAAA,OAAMZ,OAAA,CAAQhN,MAAM,CAAC;IAAA,GAAEgD,GAAA,CAAI6K,KAAA;MAAA,IAAC;QAC1BzC,OAAA,EAAA0C;MACF,IAAAD,KAAA;MAAA,OAAMC,QAAO;IAAA,EAAC;IACd,IAAIJ,YAAA,CAAaC,MAAA,CAAOI,EAAA,IAAM,CAAC,CAACA,EAAE,EAAE3Q,MAAA,KAAW,GAAG;MAChDsQ,YAAA,GAAe,CAACH,uBAAuB;IACzC;IACA,OAAOG,YAAA,CAAaM,MAAA,CAAO,CAACC,aAAA,EAAeC,WAAA,KAAmB;MAC5D,IAAIA,WAAA,EAAa;QACf,IAAI7B,QAAA,CAAQ4B,aAAa,GAAG;UAI1B,MAAME,KAAA,GAAQF,aAAA;UACd,MAAM5I,MAAA,GAAS6I,WAAA,CAAYC,KAAA,EAAOnO,MAAM;UACxC,IAAIqF,MAAA,KAAW,QAAW;YACxB,OAAO4I,aAAA;UACT;UACA,OAAO5I,MAAA;QACT,WAAW,CAACiH,YAAA,CAAY2B,aAAa,GAAG;UAGtC,MAAM5I,MAAA,GAAS6I,WAAA,CAAYD,aAAA,EAAsBjO,MAAM;UACvD,IAAIqF,MAAA,KAAW,QAAW;YACxB,IAAI4I,aAAA,KAAkB,MAAM;cAC1B,OAAOA,aAAA;YACT;YACA,MAAM7O,KAAA,CAAM,mEAAmE;UACjF;UACA,OAAOiG,MAAA;QACT,OAAO;UAIL,OAAO+G,gBAAA,CAAgB6B,aAAA,EAAgBE,KAAA,IAAoB;YACzD,OAAOD,WAAA,CAAYC,KAAA,EAAOnO,MAAM;UAClC,CAAC;QACH;MACF;MACA,OAAOiO,aAAA;IACT,GAAGpH,KAAK;EACV;EACAuE,OAAA,CAAQoC,eAAA,GAAkBA,eAAA;EAC1B,OAAOpC,OAAA;AACT;;;AElLA,IAAMgD,OAAA,GAAUA,CAACpB,OAAA,EAAuBhN,MAAA,KAAgB;EACtD,IAAIpB,gBAAA,CAAiBoO,OAAO,GAAG;IAC7B,OAAOA,OAAA,CAAQlO,KAAA,CAAMkB,MAAM;EAC7B,OAAO;IACL,OAAOgN,OAAA,CAAQhN,MAAM;EACvB;AACF;AAWO,SAASqO,QAAA,EAAgE;EAAA,SAAAC,KAAA,GAAA1R,SAAA,CAAAQ,MAAA,EAApBmR,QAAA,OAAAjR,KAAA,CAAAgR,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;IAAAD,QAAA,CAAAC,KAAA,IAAA5R,SAAA,CAAA4R,KAAA;EAAA;EAC1D,OAAQxO,MAAA,IAAyD;IAC/D,OAAOuO,QAAA,CAAStJ,IAAA,CAAK+H,OAAA,IAAWoB,OAAA,CAAQpB,OAAA,EAAShN,MAAM,CAAC;EAC1D;AACF;AAWO,SAASyO,QAAA,EAAgE;EAAA,SAAAC,KAAA,GAAA9R,SAAA,CAAAQ,MAAA,EAApBmR,QAAA,OAAAjR,KAAA,CAAAoR,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAAJ,QAAA,CAAAI,KAAA,IAAA/R,SAAA,CAAA+R,KAAA;EAAA;EAC1D,OAAQ3O,MAAA,IAAyD;IAC/D,OAAOuO,QAAA,CAASnO,KAAA,CAAM4M,OAAA,IAAWoB,OAAA,CAAQpB,OAAA,EAAShN,MAAM,CAAC;EAC3D;AACF;AAQO,SAAS4O,2BAA2B5O,MAAA,EAAa6O,WAAA,EAAgC;EACtF,IAAI,CAAC7O,MAAA,IAAU,CAACA,MAAA,CAAOL,IAAA,EAAM,OAAO;EACpC,MAAMmP,iBAAA,GAAoB,OAAO9O,MAAA,CAAOL,IAAA,CAAKoP,SAAA,KAAc;EAC3D,MAAMC,qBAAA,GAAwBH,WAAA,CAAYtO,OAAA,CAAQP,MAAA,CAAOL,IAAA,CAAKsP,aAAa,IAAI;EAC/E,OAAOH,iBAAA,IAAqBE,qBAAA;AAC9B;AACA,SAASE,kBAAkBC,CAAA,EAAkD;EAC3E,OAAO,OAAOA,CAAA,CAAE,CAAC,MAAM,cAAc,aAAaA,CAAA,CAAE,CAAC,KAAK,eAAeA,CAAA,CAAE,CAAC,KAAK,cAAcA,CAAA,CAAE,CAAC;AACpG;AA2BO,SAASC,UAAA,EAAwG;EAAA,SAAAC,KAAA,GAAAzS,SAAA,CAAAQ,MAAA,EAAlCkS,WAAA,OAAAhS,KAAA,CAAA+R,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;IAAAD,WAAA,CAAAC,KAAA,IAAA3S,SAAA,CAAA2S,KAAA;EAAA;EACpF,IAAID,WAAA,CAAYlS,MAAA,KAAW,GAAG;IAC5B,OAAQ4C,MAAA,IAAgB4O,0BAAA,CAA2B5O,MAAA,EAAQ,CAAC,SAAS,CAAC;EACxE;EACA,IAAI,CAACkP,iBAAA,CAAkBI,WAAW,GAAG;IACnC,OAAOF,SAAA,CAAU,EAAEE,WAAA,CAAY,CAAC,CAAC;EACnC;EACA,OAAOjB,OAAA,CAAQ,GAAGiB,WAAA,CAAYtM,GAAA,CAAIwM,UAAA,IAAcA,UAAA,CAAWC,OAAO,CAAC;AACrE;AA2BO,SAASC,WAAA,EAAyG;EAAA,SAAAC,KAAA,GAAA/S,SAAA,CAAAQ,MAAA,EAAlCkS,WAAA,OAAAhS,KAAA,CAAAqS,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAAN,WAAA,CAAAM,KAAA,IAAAhT,SAAA,CAAAgT,KAAA;EAAA;EACrF,IAAIN,WAAA,CAAYlS,MAAA,KAAW,GAAG;IAC5B,OAAQ4C,MAAA,IAAgB4O,0BAAA,CAA2B5O,MAAA,EAAQ,CAAC,UAAU,CAAC;EACzE;EACA,IAAI,CAACkP,iBAAA,CAAkBI,WAAW,GAAG;IACnC,OAAOI,UAAA,CAAW,EAAEJ,WAAA,CAAY,CAAC,CAAC;EACpC;EACA,OAAOjB,OAAA,CAAQ,GAAGiB,WAAA,CAAYtM,GAAA,CAAIwM,UAAA,IAAcA,UAAA,CAAWK,QAAQ,CAAC;AACtE;AA+BO,SAASC,oBAAA,EAAkH;EAChI,MAAMC,OAAA,GAAW/P,MAAA,IAA+B;IAC9C,OAAOA,MAAA,IAAUA,MAAA,CAAOL,IAAA,IAAQK,MAAA,CAAOL,IAAA,CAAKqQ,iBAAA;EAC9C;EAAA,SAAAC,KAAA,GAAArT,SAAA,CAAAQ,MAAA,EAH8FkS,WAAA,OAAAhS,KAAA,CAAA2S,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAAZ,WAAA,CAAAY,KAAA,IAAAtT,SAAA,CAAAsT,KAAA;EAAA;EAI9F,IAAIZ,WAAA,CAAYlS,MAAA,KAAW,GAAG;IAC5B,OAAOqR,OAAA,CAAQiB,UAAA,CAAW,GAAGJ,WAAW,GAAGS,OAAO;EACpD;EACA,IAAI,CAACb,iBAAA,CAAkBI,WAAW,GAAG;IACnC,OAAOQ,mBAAA,CAAoB,EAAER,WAAA,CAAY,CAAC,CAAC;EAC7C;EACA,OAAOb,OAAA,CAAQiB,UAAA,CAAW,GAAGJ,WAAW,GAAGS,OAAO;AACpD;AA2BO,SAASI,YAAA,EAA0G;EAAA,SAAAC,KAAA,GAAAxT,SAAA,CAAAQ,MAAA,EAAlCkS,WAAA,OAAAhS,KAAA,CAAA8S,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAAf,WAAA,CAAAe,KAAA,IAAAzT,SAAA,CAAAyT,KAAA;EAAA;EACtF,IAAIf,WAAA,CAAYlS,MAAA,KAAW,GAAG;IAC5B,OAAQ4C,MAAA,IAAgB4O,0BAAA,CAA2B5O,MAAA,EAAQ,CAAC,WAAW,CAAC;EAC1E;EACA,IAAI,CAACkP,iBAAA,CAAkBI,WAAW,GAAG;IACnC,OAAOa,WAAA,CAAY,EAAEb,WAAA,CAAY,CAAC,CAAC;EACrC;EACA,OAAOjB,OAAA,CAAQ,GAAGiB,WAAA,CAAYtM,GAAA,CAAIwM,UAAA,IAAcA,UAAA,CAAWc,SAAS,CAAC;AACvE;AAoCO,SAASC,mBAAA,EAAiH;EAAA,SAAAC,KAAA,GAAA5T,SAAA,CAAAQ,MAAA,EAAlCkS,WAAA,OAAAhS,KAAA,CAAAkT,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAAnB,WAAA,CAAAmB,KAAA,IAAA7T,SAAA,CAAA6T,KAAA;EAAA;EAC7F,IAAInB,WAAA,CAAYlS,MAAA,KAAW,GAAG;IAC5B,OAAQ4C,MAAA,IAAgB4O,0BAAA,CAA2B5O,MAAA,EAAQ,CAAC,WAAW,aAAa,UAAU,CAAC;EACjG;EACA,IAAI,CAACkP,iBAAA,CAAkBI,WAAW,GAAG;IACnC,OAAOiB,kBAAA,CAAmB,EAAEjB,WAAA,CAAY,CAAC,CAAC;EAC5C;EACA,OAAOjB,OAAA,CAAQ,GAAGiB,WAAA,CAAYoB,OAAA,CAAQlB,UAAA,IAAc,CAACA,UAAA,CAAWC,OAAA,EAASD,UAAA,CAAWK,QAAA,EAAUL,UAAA,CAAWc,SAAS,CAAC,CAAC;AACtH;;;ACzPA,IAAIK,WAAA,GAAc;AAMX,IAAIC,MAAA,GAAS,SAAAA,CAAA,EAAe;EAAA,IAAdC,IAAA,GAAAjU,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAO;EAC1B,IAAIkU,EAAA,GAAK;EAET,IAAIC,CAAA,GAAIF,IAAA;EACR,OAAOE,CAAA,IAAK;IAEVD,EAAA,IAAMH,WAAA,CAAYK,IAAA,CAAKC,MAAA,CAAO,IAAI,KAAK,CAAC;EAC1C;EACA,OAAOH,EAAA;AACT;;;ACSA,IAAMI,gBAAA,GAAiD,CAAC,QAAQ,WAAW,SAAS,MAAM;AAC1F,IAAMC,eAAA,GAAN,MAAMA,eAAA,CAAuC;EAM3ClP,YAA4BvC,OAAA,EAAkCC,IAAA,EAAoB;IAAC;AAAA;AAAA;AAAA;IAAAyR,eAAA;IAAvD,KAAA1R,OAAA,GAAAA,OAAA;IAAkC,KAAAC,IAAA,GAAAA,IAAA;EAAqB;AACrF;AACA,IAAM0R,eAAA,GAAN,MAAMA,eAAA,CAAwC;EAM5CpP,YAA4BvC,OAAA,EAAkCC,IAAA,EAAqB;IAAC;AAAA;AAAA;AAAA;IAAAyR,eAAA;IAAxD,KAAA1R,OAAA,GAAAA,OAAA;IAAkC,KAAAC,IAAA,GAAAA,IAAA;EAAsB;AACtF;AAQO,IAAM2R,kBAAA,GAAsBpU,KAAA,IAAgC;EACjE,IAAI,OAAOA,KAAA,KAAU,YAAYA,KAAA,KAAU,MAAM;IAC/C,MAAMqU,WAAA,GAA+B,CAAC;IACtC,WAAWC,QAAA,IAAYN,gBAAA,EAAkB;MACvC,IAAI,OAAOhU,KAAA,CAAMsU,QAAQ,MAAM,UAAU;QACvCD,WAAA,CAAYC,QAAQ,IAAItU,KAAA,CAAMsU,QAAQ;MACxC;IACF;IACA,OAAOD,WAAA;EACT;EACA,OAAO;IACLE,OAAA,EAASC,MAAA,CAAOxU,KAAK;EACvB;AACF;AA4MA,IAAMyU,oBAAA,GAAuB;AACtB,IAAMC,gBAAA,GAAmC,sBAAM;EACpD,SAASC,kBAA8EC,UAAA,EAAoBC,cAAA,EAA8ElR,OAAA,EAAuG;IAK9R,MAAMyP,SAAA,GAAkFvR,YAAA,CAAa+S,UAAA,GAAa,cAAc,CAACpS,OAAA,EAAmBqP,SAAA,EAAmBiD,GAAA,EAAerS,IAAA,MAA0B;MAC9MD,OAAA;MACAC,IAAA,EAAAF,aAAA,CAAAA,aAAA,KACME,IAAA,IAAe,CAAC;QACpBqS,GAAA;QACAjD,SAAA;QACAE,aAAA,EAAe;MAAA;IAEnB,EAAE;IACF,MAAMQ,OAAA,GAAoE1Q,YAAA,CAAa+S,UAAA,GAAa,YAAY,CAAC/C,SAAA,EAAmBiD,GAAA,EAAerS,IAAA,MAAwB;MACzKD,OAAA,EAAS;MACTC,IAAA,EAAAF,aAAA,CAAAA,aAAA,KACME,IAAA,IAAe,CAAC;QACpBqS,GAAA;QACAjD,SAAA;QACAE,aAAA,EAAe;MAAA;IAEnB,EAAE;IACF,MAAMY,QAAA,GAAsE9Q,YAAA,CAAa+S,UAAA,GAAa,aAAa,CAAClS,KAAA,EAAqBmP,SAAA,EAAmBiD,GAAA,EAAetS,OAAA,EAAyBC,IAAA,MAAyB;MAC3ND,OAAA;MACAE,KAAA,GAAQiB,OAAA,IAAWA,OAAA,CAAQoR,cAAA,IAAkBX,kBAAA,EAAoB1R,KAAA,IAAS,UAAU;MACpFD,IAAA,EAAAF,aAAA,CAAAA,aAAA,KACME,IAAA,IAAe,CAAC;QACpBqS,GAAA;QACAjD,SAAA;QACAiB,iBAAA,EAAmB,CAAC,CAACtQ,OAAA;QACrBuP,aAAA,EAAe;QACfiD,OAAA,EAAS,CAAAtS,KAAA,aAAAA,KAAA,uBAAAA,KAAA,CAAOuS,IAAA,MAAS;QACzBC,SAAA,EAAW,CAAAxS,KAAA,aAAAA,KAAA,uBAAAA,KAAA,CAAOuS,IAAA,MAAS;MAAA;IAE/B,EAAE;IACF,SAASjT,cAAc8S,GAAA,EAE2E;MAAA,IAF5D;QACpCK;MACF,IAAAzV,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAA8B,CAAC;MAC7B,OAAO,CAACgO,QAAA,EAAUhE,QAAA,EAAU0L,KAAA,KAAU;QACpC,MAAMvD,SAAA,GAAYlO,OAAA,aAAAA,OAAA,eAAAA,OAAA,CAAS0R,WAAA,GAAc1R,OAAA,CAAQ0R,WAAA,CAAYP,GAAG,IAAIpB,MAAA,CAAO;QAC3E,MAAM4B,eAAA,GAAkB,IAAIC,eAAA,CAAgB;QAC5C,IAAIC,YAAA;QACJ,IAAIC,WAAA;QACJ,SAASC,MAAMC,MAAA,EAAiB;UAC9BF,WAAA,GAAcE,MAAA;UACdL,eAAA,CAAgBI,KAAA,CAAM;QACxB;QACA,IAAIP,MAAA,EAAQ;UACV,IAAIA,MAAA,CAAOH,OAAA,EAAS;YAClBU,KAAA,CAAMjB,oBAAoB;UAC5B,OAAO;YACLU,MAAA,CAAOS,gBAAA,CAAiB,SAAS,MAAMF,KAAA,CAAMjB,oBAAoB,GAAG;cAClEoB,IAAA,EAAM;YACR,CAAC;UACH;QACF;QACA,MAAMC,OAAA,GAAU,kBAAkB;UAChC,IAAIC,WAAA;UACJ,IAAI;YAAA,IAAAC,kBAAA,EAAAC,qBAAA;YACF,IAAIC,eAAA,GAAkBvS,OAAA,aAAAA,OAAA,gBAAAqS,kBAAA,GAAArS,OAAA,CAASuR,SAAA,cAAAc,kBAAA,uBAATA,kBAAA,CAAA3M,IAAA,CAAA1F,OAAA,EAAqBmR,GAAA,EAAK;cAC9CpL,QAAA;cACA0L;YACF,CAAC;YACD,IAAIe,UAAA,CAAWD,eAAe,GAAG;cAC/BA,eAAA,GAAkB,MAAMA,eAAA;YAC1B;YACA,IAAIA,eAAA,KAAoB,SAASZ,eAAA,CAAgBH,MAAA,CAAOH,OAAA,EAAS;cAE/D,MAAM;gBACJC,IAAA,EAAM;gBACNV,OAAA,EAAS;cACX;YACF;YACA,MAAM6B,cAAA,GAAiB,IAAIC,OAAA,CAAe,CAACvN,CAAA,EAAGwN,MAAA,KAAW;cACvDd,YAAA,GAAeA,CAAA,KAAM;gBACnBc,MAAA,CAAO;kBACLrB,IAAA,EAAM;kBACNV,OAAA,EAASkB,WAAA,IAAe;gBAC1B,CAAC;cACH;cACAH,eAAA,CAAgBH,MAAA,CAAOS,gBAAA,CAAiB,SAASJ,YAAY;YAC/D,CAAC;YACD9H,QAAA,CAAS6E,OAAA,CAAQV,SAAA,EAAWiD,GAAA,EAAKnR,OAAA,aAAAA,OAAA,gBAAAsS,qBAAA,GAAAtS,OAAA,CAAS4S,cAAA,cAAAN,qBAAA,uBAATA,qBAAA,CAAA5M,IAAA,CAAA1F,OAAA,EAA0B;cACzDkO,SAAA;cACAiD;YACF,GAAG;cACDpL,QAAA;cACA0L;YACF,CAAC,CAAC,CAAQ;YACVW,WAAA,GAAc,MAAMM,OAAA,CAAQG,IAAA,CAAK,CAACJ,cAAA,EAAgBC,OAAA,CAAQI,OAAA,CAAQ5B,cAAA,CAAeC,GAAA,EAAK;cACpFpH,QAAA;cACAhE,QAAA;cACA0L,KAAA;cACAvD,SAAA;cACAsD,MAAA,EAAQG,eAAA,CAAgBH,MAAA;cACxBO,KAAA;cACAgB,eAAA,EAAkBA,CAAC1W,KAAA,EAAsByC,IAAA,KAAwB;gBAC/D,OAAO,IAAIwR,eAAA,CAAgBjU,KAAA,EAAOyC,IAAI;cACxC;cACAkU,gBAAA,EAAmBA,CAAC3W,KAAA,EAAgByC,IAAA,KAAyB;gBAC3D,OAAO,IAAI0R,eAAA,CAAgBnU,KAAA,EAAOyC,IAAI;cACxC;YACF,CAAC,CAAC,EAAEmU,IAAA,CAAKzO,MAAA,IAAU;cACjB,IAAIA,MAAA,YAAkB8L,eAAA,EAAiB;gBACrC,MAAM9L,MAAA;cACR;cACA,IAAIA,MAAA,YAAkBgM,eAAA,EAAiB;gBACrC,OAAOf,SAAA,CAAUjL,MAAA,CAAO3F,OAAA,EAASqP,SAAA,EAAWiD,GAAA,EAAK3M,MAAA,CAAO1F,IAAI;cAC9D;cACA,OAAO2Q,SAAA,CAAUjL,MAAA,EAAe0J,SAAA,EAAWiD,GAAG;YAChD,CAAC,CAAC,CAAC;UACL,SAAS+B,GAAA,EAAK;YACZd,WAAA,GAAcc,GAAA,YAAe5C,eAAA,GAAkBtB,QAAA,CAAS,MAAMd,SAAA,EAAWiD,GAAA,EAAK+B,GAAA,CAAIrU,OAAA,EAASqU,GAAA,CAAIpU,IAAI,IAAIkQ,QAAA,CAASkE,GAAA,EAAYhF,SAAA,EAAWiD,GAAG;UAC5I,UAAE;YACA,IAAIU,YAAA,EAAc;cAChBF,eAAA,CAAgBH,MAAA,CAAO2B,mBAAA,CAAoB,SAAStB,YAAY;YAClE;UACF;UAMA,MAAMuB,YAAA,GAAepT,OAAA,IAAW,CAACA,OAAA,CAAQqT,0BAAA,IAA8BrE,QAAA,CAAS/Q,KAAA,CAAMmU,WAAW,KAAMA,WAAA,CAAoBtT,IAAA,CAAKyS,SAAA;UAChI,IAAI,CAAC6B,YAAA,EAAc;YACjBrJ,QAAA,CAASqI,WAAkB;UAC7B;UACA,OAAOA,WAAA;QACT,EAAE;QACF,OAAOnW,MAAA,CAAOC,MAAA,CAAOiW,OAAA,EAA6B;UAChDJ,KAAA;UACA7D,SAAA;UACAiD,GAAA;UACAmC,OAAA,EAAS;YACP,OAAOnB,OAAA,CAAQc,IAAA,CAAUM,YAAY;UACvC;QACF,CAAC;MACH;IACF;IACA,OAAOtX,MAAA,CAAOC,MAAA,CAAOmC,aAAA,EAA8E;MACjGuQ,OAAA;MACAI,QAAA;MACAS,SAAA;MACA+D,OAAA,EAAShG,OAAA,CAAQwB,QAAA,EAAUS,SAAS;MACpCwB;IACF,CAAC;EACH;EACAD,iBAAA,CAAiBrU,SAAA,GAAY,MAAMqU,iBAAA;EACnC,OAAOA,iBAAA;AACT,GAAG;AAaI,SAASuC,aAA0CpU,MAAA,EAAsC;EAC9F,IAAIA,MAAA,CAAOL,IAAA,IAAQK,MAAA,CAAOL,IAAA,CAAKqQ,iBAAA,EAAmB;IAChD,MAAMhQ,MAAA,CAAON,OAAA;EACf;EACA,IAAIM,MAAA,CAAOJ,KAAA,EAAO;IAChB,MAAMI,MAAA,CAAOJ,KAAA;EACf;EACA,OAAOI,MAAA,CAAON,OAAA;AAChB;AAEA,SAAS2T,WAAWnW,KAAA,EAAuC;EACzD,OAAOA,KAAA,KAAU,QAAQ,OAAOA,KAAA,KAAU,YAAY,OAAOA,KAAA,CAAM4W,IAAA,KAAS;AAC9E;;;AC/aA,IAAMQ,gBAAA,GAAkC,eAAAlS,MAAA,CAAOmS,GAAA,CAAI,4BAA4B;AAExE,IAAMC,iBAAA,GAET;EACF,CAACF,gBAAgB,GAAG1C;AACtB;AAwLO,IAAK6C,WAAA,GAAL,gBAAKC,YAAA,IAAL;EACLA,YAAA,cAAU;EACVA,YAAA,yBAAqB;EACrBA,YAAA,iBAAa;EAHH,OAAAA,YAAA;AAAA,GAAAD,WAAA;AAoIZ,SAASE,QAAQ1O,KAAA,EAAe2O,SAAA,EAA2B;EACzD,UAAA7U,MAAA,CAAUkG,KAAK,OAAAlG,MAAA,CAAI6U,SAAS;AAC9B;AAMO,SAASC,iBAAA,EAEgB;EAAA,IAAAC,oBAAA;EAAA,IAFC;IAC/BC;EACF,IAAAnY,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAA4B,CAAC;EAC3B,MAAMoY,GAAA,GAAMD,QAAA,aAAAA,QAAA,gBAAAD,oBAAA,GAAAC,QAAA,CAAUvF,UAAA,cAAAsF,oBAAA,uBAAVA,oBAAA,CAAuBR,gBAAgB;EACnD,OAAO,SAASW,aAAmKpU,OAAA,EAA0I;IAC3T,MAAM;MACJsR,IAAA;MACA+C,WAAA,GAAc/C;IAChB,IAAItR,OAAA;IACJ,IAAI,CAACsR,IAAA,EAAM;MACT,MAAM,IAAI/S,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,EAAE,IAAI,6CAA6C;IACrI;IACA,IAAI,OAAOH,OAAA,KAAY,eAAeA,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAe;MAC5E,IAAIsB,OAAA,CAAQuM,YAAA,KAAiB,QAAW;QACtCpM,OAAA,CAAQpB,KAAA,CAAM,0GAA0G;MAC1H;IACF;IACA,MAAMuV,QAAA,IAAY,OAAOtU,OAAA,CAAQsU,QAAA,KAAa,aAAatU,OAAA,CAAQsU,QAAA,CAASC,oBAAA,CAA4B,CAAC,IAAIvU,OAAA,CAAQsU,QAAA,KAAa,CAAC;IACnI,MAAME,YAAA,GAAevY,MAAA,CAAOqD,IAAA,CAAKgV,QAAQ;IACzC,MAAMG,OAAA,GAAyC;MAC7CC,uBAAA,EAAyB,CAAC;MAC1BC,uBAAA,EAAyB,CAAC;MAC1BC,cAAA,EAAgB,CAAC;MACjBC,aAAA,EAAe;IACjB;IACA,MAAMC,cAAA,GAAuD;MAC3D9I,QAAQC,mBAAA,EAAuDgB,QAAA,EAA6B;QAC1F,MAAM9O,IAAA,GAAO,OAAO8N,mBAAA,KAAwB,WAAWA,mBAAA,GAAsBA,mBAAA,CAAoB9N,IAAA;QACjG,IAAI,CAACA,IAAA,EAAM;UACT,MAAM,IAAII,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,8DAA8D;QACvJ;QACA,IAAIR,IAAA,IAAQsW,OAAA,CAAQE,uBAAA,EAAyB;UAC3C,MAAM,IAAIpW,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,oFAAoFR,IAAI;QACjL;QACAsW,OAAA,CAAQE,uBAAA,CAAwBxW,IAAI,IAAI8O,QAAA;QACxC,OAAO6H,cAAA;MACT;MACA5I,WAAWC,OAAA,EAASc,QAAA,EAAS;QAC3BwH,OAAA,CAAQI,aAAA,CAAcrP,IAAA,CAAK;UACzB2G,OAAA;UACA5B,OAAA,EAAA0C;QACF,CAAC;QACD,OAAO6H,cAAA;MACT;MACAC,aAAaC,KAAA,EAAM3W,aAAA,EAAe;QAChCoW,OAAA,CAAQG,cAAA,CAAeI,KAAI,IAAI3W,aAAA;QAC/B,OAAOyW,cAAA;MACT;MACAG,kBAAkBD,KAAA,EAAM/H,QAAA,EAAS;QAC/BwH,OAAA,CAAQC,uBAAA,CAAwBM,KAAI,IAAI/H,QAAA;QACxC,OAAO6H,cAAA;MACT;IACF;IACAN,YAAA,CAAahL,OAAA,CAAQ0L,WAAA,IAAe;MAClC,MAAMC,iBAAA,GAAoBb,QAAA,CAASY,WAAW;MAC9C,MAAME,cAAA,GAAiC;QACrCF,WAAA;QACA/W,IAAA,EAAM2V,OAAA,CAAQxC,IAAA,EAAM4D,WAAW;QAC/BG,cAAA,EAAgB,OAAOrV,OAAA,CAAQsU,QAAA,KAAa;MAC9C;MACA,IAAIgB,kCAAA,CAA0CH,iBAAiB,GAAG;QAChEI,gCAAA,CAAiCH,cAAA,EAAgBD,iBAAA,EAAmBL,cAAA,EAAgBX,GAAG;MACzF,OAAO;QACLqB,6BAAA,CAAqCJ,cAAA,EAAgBD,iBAAA,EAA0BL,cAAc;MAC/F;IACF,CAAC;IACD,SAASW,aAAA,EAAe;MACtB,IAAIjX,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QACzC,IAAI,OAAOsB,OAAA,CAAQ0V,aAAA,KAAkB,UAAU;UAC7C,MAAM,IAAInX,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,wKAAwK;QACjQ;MACF;MACA,MAAM,CAAC+W,aAAA,GAAgB,CAAC,GAAG7J,cAAA,GAAiB,EAAC,EAAGC,kBAAA,GAAqB,MAAS,IAAI,OAAO9L,OAAA,CAAQ0V,aAAA,KAAkB,aAAahK,6BAAA,CAA8B1L,OAAA,CAAQ0V,aAAa,IAAI,CAAC1V,OAAA,CAAQ0V,aAAa;MAC7M,MAAMC,iBAAA,GAAA/W,aAAA,CAAAA,aAAA,KACD8W,aAAA,GACAjB,OAAA,CAAQE,uBAAA,CACb;MACA,OAAOrI,aAAA,CAActM,OAAA,CAAQuM,YAAA,EAAcR,OAAA,IAAW;QACpD,SAAStM,GAAA,IAAOkW,iBAAA,EAAmB;UACjC5J,OAAA,CAAQC,OAAA,CAAQvM,GAAA,EAAKkW,iBAAA,CAAkBlW,GAAG,CAAqB;QACjE;QACA,SAASmW,EAAA,IAAMnB,OAAA,CAAQI,aAAA,EAAe;UACpC9I,OAAA,CAAQG,UAAA,CAAW0J,EAAA,CAAGzJ,OAAA,EAASyJ,EAAA,CAAGrL,OAAO;QAC3C;QACA,SAASsL,CAAA,IAAKhK,cAAA,EAAgB;UAC5BE,OAAA,CAAQG,UAAA,CAAW2J,CAAA,CAAE1J,OAAA,EAAS0J,CAAA,CAAEtL,OAAO;QACzC;QACA,IAAIuB,kBAAA,EAAoB;UACtBC,OAAA,CAAQK,cAAA,CAAeN,kBAAkB;QAC3C;MACF,CAAC;IACH;IACA,MAAMgK,UAAA,GAAc9P,KAAA,IAAiBA,KAAA;IACrC,MAAM+P,qBAAA,GAAwB,mBAAIC,GAAA,CAAsG;IACxI,MAAMC,kBAAA,GAAqB,mBAAIC,OAAA,CAA0C;IACzE,IAAIC,QAAA;IACJ,SAAS5L,QAAQvE,KAAA,EAA0B7G,MAAA,EAAuB;MAChE,IAAI,CAACgX,QAAA,EAAUA,QAAA,GAAWV,YAAA,CAAa;MACvC,OAAOU,QAAA,CAASnQ,KAAA,EAAO7G,MAAM;IAC/B;IACA,SAASwN,gBAAA,EAAkB;MACzB,IAAI,CAACwJ,QAAA,EAAUA,QAAA,GAAWV,YAAA,CAAa;MACvC,OAAOU,QAAA,CAASxJ,eAAA,CAAgB;IAClC;IACA,SAASyJ,kBAAmEC,YAAA,EAAwL;MAAA,IAAvJC,QAAA,GAAAva,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAW;MACtH,SAASwa,YAAYvQ,KAAA,EAA6C;QAChE,IAAIwQ,UAAA,GAAaxQ,KAAA,CAAMqQ,YAAW;QAClC,IAAI,OAAOG,UAAA,KAAe,aAAa;UACrC,IAAIF,QAAA,EAAU;YACZE,UAAA,GAAatU,mBAAA,CAAoB+T,kBAAA,EAAoBM,WAAA,EAAa5J,eAAe;UACnF,WAAWnO,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;YAChD,MAAM,IAAIH,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,gEAAgE;UACzJ;QACF;QACA,OAAO6X,UAAA;MACT;MACA,SAASC,aAAA,EAAkE;QAAA,IAArDC,WAAA,GAAA3a,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAyC+Z,UAAA;QAC7D,MAAMa,aAAA,GAAgBzU,mBAAA,CAAoB6T,qBAAA,EAAuBO,QAAA,EAAU,MAAM,mBAAIJ,OAAA,CAAQ,CAAC;QAC9F,OAAOhU,mBAAA,CAAoByU,aAAA,EAAeD,WAAA,EAAa,MAAM;UAC3D,MAAMvU,GAAA,GAA0C,CAAC;UACjD,WAAW,CAAC6S,KAAA,EAAM7Y,QAAQ,KAAKF,MAAA,CAAO2K,OAAA,EAAAgQ,kBAAA,GAAQ5W,OAAA,CAAQ6W,SAAA,cAAAD,kBAAA,cAAAA,kBAAA,GAAa,CAAC,CAAC,GAAG;YAAA,IAAAA,kBAAA;YACtEzU,GAAA,CAAI6S,KAAI,IAAI8B,YAAA,CAAa3a,QAAA,EAAUua,WAAA,EAAa,MAAMxU,mBAAA,CAAoB+T,kBAAA,EAAoBS,WAAA,EAAa/J,eAAe,GAAG2J,QAAQ;UACvI;UACA,OAAOnU,GAAA;QACT,CAAC;MACH;MACA,OAAO;QACLkS,WAAA,EAAAgC,YAAA;QACAI,YAAA;QACA,IAAII,UAAA,EAAY;UACd,OAAOJ,YAAA,CAAaF,WAAW;QACjC;QACAA;MACF;IACF;IACA,MAAMnR,KAAA,GAAAxG,aAAA,CAAAA,aAAA;MACJ0S,IAAA;MACA/G,OAAA;MACAwM,OAAA,EAAStC,OAAA,CAAQG,cAAA;MACjB/H,YAAA,EAAc4H,OAAA,CAAQC,uBAAA;MACtB/H;IAAA,GACGyJ,iBAAA,CAAkB/B,WAAW;MAChC2C,WAAWC,UAAA,EAGH;QAAA,IAAAC,KAAA,GAAAnb,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAJ,CAAC;UAHkB;YACrBsY,WAAA,EAAa8C;UAEf,IAAAD,KAAA;UADKE,MAAA,GAAAC,wBAAA,CAAAH,KAAA,EAAAI,SAAA;QAEH,MAAMC,cAAA,GAAiBJ,OAAA,aAAAA,OAAA,cAAAA,OAAA,GAAW9C,WAAA;QAClC4C,UAAA,CAAWO,MAAA,CAAO;UAChBnD,WAAA,EAAakD,cAAA;UACbhN;QACF,GAAG6M,MAAM;QACT,OAAAxY,aAAA,CAAAA,aAAA,KACKwG,KAAA,GACAgR,iBAAA,CAAkBmB,cAAA,EAAgB,IAAI;MAE7C;IAAA,EACF;IACA,OAAOnS,KAAA;EACT;AACF;AACA,SAAS0R,aAAyD3a,QAAA,EAAaua,WAAA,EAAwC/J,eAAA,EAA8B2J,QAAA,EAAoB;EACvK,SAASmB,QAAQC,SAAA,EAAqC;IACpD,IAAIlB,UAAA,GAAaE,WAAA,CAAYgB,SAAS;IACtC,IAAI,OAAOlB,UAAA,KAAe,aAAa;MACrC,IAAIF,QAAA,EAAU;QACZE,UAAA,GAAa7J,eAAA,CAAgB;MAC/B,WAAWnO,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QAChD,MAAM,IAAIH,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,gEAAgE;MACzJ;IACF;IAAA,SAAAgZ,KAAA,GAAA5b,SAAA,CAAAQ,MAAA,EARuCqb,IAAA,OAAAnb,KAAA,CAAAkb,KAAA,OAAAA,KAAA,WAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;MAAAD,IAAA,CAAAC,KAAA,QAAA9b,SAAA,CAAA8b,KAAA;IAAA;IASvC,OAAO1b,QAAA,CAASqa,UAAA,EAAY,GAAGoB,IAAI;EACrC;EACAH,OAAA,CAAQK,SAAA,GAAY3b,QAAA;EACpB,OAAOsb,OAAA;AACT;AAUO,IAAMM,WAAA,GAA6B,eAAA/D,gBAAA,CAAiB;AAkE3D,SAASO,qBAAA,EAAsD;EAC7D,SAAS5F,WAAWuC,cAAA,EAAoDkG,MAAA,EAAgG;IACtK,OAAAxY,aAAA;MACEoZ,sBAAA,EAAwB;MACxB9G;IAAA,GACGkG,MAAA;EAEP;EACAzI,UAAA,CAAWhS,SAAA,GAAY,MAAMgS,UAAA;EAC7B,OAAO;IACLpE,QAAQ8C,WAAA,EAAsC;MAC5C,OAAOpR,MAAA,CAAOC,MAAA,CAAO;QAAA;QAAA;QAGnB,CAACmR,WAAA,CAAYiE,IAAI,IAA2C;UAC1D,OAAOjE,WAAA,CAAY,GAAAtR,SAAO;QAC5B;MACF,EAAEsR,WAAA,CAAYiE,IAAI,GAAG;QACnB0G,sBAAA,EAAwB;MAC1B,CAAU;IACZ;IACAC,gBAAgBC,OAAA,EAAS3N,OAAA,EAAS;MAChC,OAAO;QACLyN,sBAAA,EAAwB;QACxBE,OAAA;QACA3N;MACF;IACF;IACAoE;EACF;AACF;AACA,SAAS6G,8BAAA2C,KAAA,EAIUC,uBAAA,EAGuD3D,OAAA,EAA+C;EAAA,IAP3E;IAC5CtW,IAAA;IACA+W,WAAA;IACAG;EACF,IAAA8C,KAAA;EAIE,IAAI9K,WAAA;EACJ,IAAIgL,eAAA;EACJ,IAAI,aAAaD,uBAAA,EAAyB;IACxC,IAAI/C,cAAA,IAAkB,CAACiD,kCAAA,CAAmCF,uBAAuB,GAAG;MAClF,MAAM,IAAI7Z,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,2GAA2G;IACpM;IACA0O,WAAA,GAAc+K,uBAAA,CAAwB7N,OAAA;IACtC8N,eAAA,GAAkBD,uBAAA,CAAwBF,OAAA;EAC5C,OAAO;IACL7K,WAAA,GAAc+K,uBAAA;EAChB;EACA3D,OAAA,CAAQzI,OAAA,CAAQ7N,IAAA,EAAMkP,WAAW,EAAE4H,iBAAA,CAAkBC,WAAA,EAAa7H,WAAW,EAAE0H,YAAA,CAAaG,WAAA,EAAamD,eAAA,GAAkBna,YAAA,CAAaC,IAAA,EAAMka,eAAe,IAAIna,YAAA,CAAaC,IAAI,CAAC;AACrL;AACA,SAASmX,mCAA0CH,iBAAA,EAAqG;EACtJ,OAAOA,iBAAA,CAAkB6C,sBAAA,KAA2B;AACtD;AACA,SAASM,mCAA0CnD,iBAAA,EAA2F;EAC5I,OAAOA,iBAAA,CAAkB6C,sBAAA,KAA2B;AACtD;AACA,SAASzC,iCAAAgD,KAAA,EAGUpD,iBAAA,EAA2EV,OAAA,EAA+CN,GAAA,EAA2C;EAAA,IAHvI;IAC/ChW,IAAA;IACA+W;EACF,IAAAqD,KAAA;EACE,IAAI,CAACpE,GAAA,EAAK;IACR,MAAM,IAAI5V,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,wLAA6L;EACtR;EACA,MAAM;IACJuS,cAAA;IACAzB,SAAA;IACAb,OAAA;IACAI,QAAA;IACAwE,OAAA;IACAxT;EACF,IAAImV,iBAAA;EACJ,MAAMxX,KAAA,GAAQwW,GAAA,CAAIhW,IAAA,EAAM+S,cAAA,EAAgBlR,OAAc;EACtDyU,OAAA,CAAQM,YAAA,CAAaG,WAAA,EAAavX,KAAK;EACvC,IAAI8R,SAAA,EAAW;IACbgF,OAAA,CAAQzI,OAAA,CAAQrO,KAAA,CAAM8R,SAAA,EAAWA,SAAS;EAC5C;EACA,IAAIb,OAAA,EAAS;IACX6F,OAAA,CAAQzI,OAAA,CAAQrO,KAAA,CAAMiR,OAAA,EAASA,OAAO;EACxC;EACA,IAAII,QAAA,EAAU;IACZyF,OAAA,CAAQzI,OAAA,CAAQrO,KAAA,CAAMqR,QAAA,EAAUA,QAAQ;EAC1C;EACA,IAAIwE,OAAA,EAAS;IACXiB,OAAA,CAAQvI,UAAA,CAAWvO,KAAA,CAAM6V,OAAA,EAASA,OAAO;EAC3C;EACAiB,OAAA,CAAQQ,iBAAA,CAAkBC,WAAA,EAAa;IACrCzF,SAAA,EAAWA,SAAA,IAAa+I,IAAA;IACxB5J,OAAA,EAASA,OAAA,IAAW4J,IAAA;IACpBxJ,QAAA,EAAUA,QAAA,IAAYwJ,IAAA;IACtBhF,OAAA,EAASA,OAAA,IAAWgF;EACtB,CAAC;AACH;AACA,SAASA,KAAA,EAAO,CAAC;;;AC/qBV,SAASC,sBAAA,EAAoE;EAClF,OAAO;IACLC,GAAA,EAAK,EAAC;IACNC,QAAA,EAAU,CAAC;EACb;AACF;AACO,SAASC,0BAAkDC,YAAA,EAAoE;EAGpI,SAASlM,gBAAA,EAAyF;IAAA,IAAzEmM,eAAA,GAAA/c,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAuB,CAAC;IAAA,IAAG4c,QAAA,GAAA5c,SAAA,CAAAQ,MAAA,OAAAR,SAAA,MAAAiD,SAAA;IAClD,MAAMgH,KAAA,GAAQ/J,MAAA,CAAOC,MAAA,CAAOuc,qBAAA,CAAsB,GAAGK,eAAe;IACpE,OAAOH,QAAA,GAAWE,YAAA,CAAaE,MAAA,CAAO/S,KAAA,EAAO2S,QAAQ,IAAI3S,KAAA;EAC3D;EACA,OAAO;IACL2G;EACF;AACF;;;ACTO,SAASqM,uBAAA,EAAiD;EAG/D,SAASvC,aAAgBC,WAAA,EAAgH;IAAA,IAAhE1W,OAAA,GAAAjE,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAA+B,CAAC;IACvG,MAAM;MACJR,cAAA,EAAAO,eAAA,GAAiBc;IACnB,IAAIoD,OAAA;IACJ,MAAMiZ,SAAA,GAAajT,KAAA,IAA8BA,KAAA,CAAM0S,GAAA;IACvD,MAAMQ,cAAA,GAAkBlT,KAAA,IAA8BA,KAAA,CAAM2S,QAAA;IAC5D,MAAMQ,SAAA,GAAYrd,eAAA,CAAemd,SAAA,EAAWC,cAAA,EAAgB,CAACR,GAAA,EAAKC,QAAA,KAAkBD,GAAA,CAAIvW,GAAA,CAAI8N,EAAA,IAAM0I,QAAA,CAAS1I,EAAE,CAAE,CAAC;IAChH,MAAMmJ,QAAA,GAAWA,CAACjU,CAAA,EAAY8K,EAAA,KAAWA,EAAA;IACzC,MAAMoJ,UAAA,GAAaA,CAACV,QAAA,EAAyB1I,EAAA,KAAW0I,QAAA,CAAS1I,EAAE;IACnE,MAAMqJ,WAAA,GAAcxd,eAAA,CAAemd,SAAA,EAAWP,GAAA,IAAOA,GAAA,CAAInc,MAAM;IAC/D,IAAI,CAACma,WAAA,EAAa;MAChB,OAAO;QACLuC,SAAA;QACAC,cAAA;QACAC,SAAA;QACAG,WAAA;QACAD,UAAA,EAAYvd,eAAA,CAAeod,cAAA,EAAgBE,QAAA,EAAUC,UAAU;MACjE;IACF;IACA,MAAME,wBAAA,GAA2Bzd,eAAA,CAAe4a,WAAA,EAAgDwC,cAAc;IAC9G,OAAO;MACLD,SAAA,EAAWnd,eAAA,CAAe4a,WAAA,EAAauC,SAAS;MAChDC,cAAA,EAAgBK,wBAAA;MAChBJ,SAAA,EAAWrd,eAAA,CAAe4a,WAAA,EAAayC,SAAS;MAChDG,WAAA,EAAaxd,eAAA,CAAe4a,WAAA,EAAa4C,WAAW;MACpDD,UAAA,EAAYvd,eAAA,CAAeyd,wBAAA,EAA0BH,QAAA,EAAUC,UAAU;IAC3E;EACF;EACA,OAAO;IACL5C;EACF;AACF;;;AC1CA,SAAS1b,OAAA,IAAWye,gBAAA,EAAiBne,OAAA,IAAAoe,QAAA,QAAe;AAK7C,IAAMC,YAAA,GAAeD,QAAA;AACrB,SAASE,kCAA0DC,OAAA,EAAuD;EAC/H,MAAMC,QAAA,GAAWC,mBAAA,CAAoB,CAAC3U,CAAA,EAAca,KAAA,KAAuC4T,OAAA,CAAQ5T,KAAK,CAAC;EACzG,OAAO,SAAS+T,UAAiD/T,KAAA,EAAgC;IAC/F,OAAO6T,QAAA,CAAS7T,KAAA,EAAY,MAAS;EACvC;AACF;AACO,SAAS8T,oBAA+CF,OAAA,EAA+D;EAC5H,OAAO,SAASG,UAAiD/T,KAAA,EAAUmL,GAAA,EAA8B;IACvG,SAAS6I,wBAAwBC,IAAA,EAAoD;MACnF,OAAO5a,KAAA,CAAM4a,IAAG;IAClB;IACA,MAAMC,UAAA,GAAc5M,KAAA,IAAuC;MACzD,IAAI0M,uBAAA,CAAwB7I,GAAG,GAAG;QAChCyI,OAAA,CAAQzI,GAAA,CAAItS,OAAA,EAASyO,KAAK;MAC5B,OAAO;QACLsM,OAAA,CAAQzI,GAAA,EAAK7D,KAAK;MACpB;IACF;IACA,IAAIoM,YAAA,CAA0C1T,KAAK,GAAG;MAIpDkU,UAAA,CAAWlU,KAAK;MAGhB,OAAOA,KAAA;IACT;IACA,OAAOwT,gBAAA,CAAgBxT,KAAA,EAAOkU,UAAU;EAC1C;AACF;;;AClCA,SAASlf,OAAA,IAAAmf,QAAA,EAAS9e,OAAA,IAAA+e,QAAA,QAAe;AAE1B,SAASC,cAAsCC,MAAA,EAAWlB,QAAA,EAA6B;EAC5F,MAAM3Z,GAAA,GAAM2Z,QAAA,CAASkB,MAAM;EAC3B,IAAI9b,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgBe,GAAA,KAAQ,QAAW;IAC9DU,OAAA,CAAQC,IAAA,CAAK,0EAA0E,mEAAmE,+BAA+Bka,MAAA,EAAQ,kCAAkClB,QAAA,CAASna,QAAA,CAAS,CAAC;EACxP;EACA,OAAOQ,GAAA;AACT;AACO,SAAS8a,oBAA4C5B,QAAA,EAAsD;EAChH,IAAI,CAAClc,KAAA,CAAMsF,OAAA,CAAQ4W,QAAQ,GAAG;IAC5BA,QAAA,GAAW1c,MAAA,CAAO8K,MAAA,CAAO4R,QAAQ;EACnC;EACA,OAAOA,QAAA;AACT;AACO,SAAS6B,WAAcne,KAAA,EAAwB;EACpD,OAAQ+d,QAAA,CAAQ/d,KAAK,IAAI8d,QAAA,CAAQ9d,KAAK,IAAIA,KAAA;AAC5C;AACO,SAASoe,0BAAkDC,WAAA,EAA2CtB,QAAA,EAA6BpT,KAAA,EAAkE;EAC1M0U,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;EAC7C,MAAMC,gBAAA,GAAmBH,UAAA,CAAWxU,KAAA,CAAM0S,GAAG;EAC7C,MAAMkC,WAAA,GAAc,IAAIzX,GAAA,CAAQwX,gBAAgB;EAChD,MAAME,KAAA,GAAa,EAAC;EACpB,MAAMC,QAAA,GAAW,mBAAI3X,GAAA,CAAQ,EAAE;EAC/B,MAAM4X,OAAA,GAA2B,EAAC;EAClC,WAAWT,MAAA,IAAUI,WAAA,EAAa;IAChC,MAAMzK,EAAA,GAAKoK,aAAA,CAAcC,MAAA,EAAQlB,QAAQ;IACzC,IAAIwB,WAAA,CAAYvY,GAAA,CAAI4N,EAAE,KAAK6K,QAAA,CAASzY,GAAA,CAAI4N,EAAE,GAAG;MAC3C8K,OAAA,CAAQvV,IAAA,CAAK;QACXyK,EAAA;QACA+K,OAAA,EAASV;MACX,CAAC;IACH,OAAO;MACLQ,QAAA,CAASzX,GAAA,CAAI4M,EAAE;MACf4K,KAAA,CAAMrV,IAAA,CAAK8U,MAAM;IACnB;EACF;EACA,OAAO,CAACO,KAAA,EAAOE,OAAA,EAASJ,gBAAgB;AAC1C;;;ACnCO,SAASM,2BAAmD7B,QAAA,EAAwD;EAEzH,SAAS8B,cAAcZ,MAAA,EAAWtU,KAAA,EAAgB;IAChD,MAAMvG,GAAA,GAAM4a,aAAA,CAAcC,MAAA,EAAQlB,QAAQ;IAC1C,IAAI3Z,GAAA,IAAOuG,KAAA,CAAM2S,QAAA,EAAU;MACzB;IACF;IACA3S,KAAA,CAAM0S,GAAA,CAAIlT,IAAA,CAAK/F,GAAqB;IACnCuG,KAAA,CAAM2S,QAAA,CAA2BlZ,GAAG,IAAI6a,MAAA;EAC3C;EACA,SAASa,eAAeT,WAAA,EAA2C1U,KAAA,EAAgB;IACjF0U,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;IAC7C,WAAWJ,MAAA,IAAUI,WAAA,EAAa;MAChCQ,aAAA,CAAcZ,MAAA,EAAQtU,KAAK;IAC7B;EACF;EACA,SAASoV,cAAcd,MAAA,EAAWtU,KAAA,EAAgB;IAChD,MAAMvG,GAAA,GAAM4a,aAAA,CAAcC,MAAA,EAAQlB,QAAQ;IAC1C,IAAI,EAAE3Z,GAAA,IAAOuG,KAAA,CAAM2S,QAAA,GAAW;MAC5B3S,KAAA,CAAM0S,GAAA,CAAIlT,IAAA,CAAK/F,GAAqB;IACtC;IACA;IACCuG,KAAA,CAAM2S,QAAA,CAA2BlZ,GAAG,IAAI6a,MAAA;EAC3C;EACA,SAASe,eAAeX,WAAA,EAA2C1U,KAAA,EAAgB;IACjF0U,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;IAC7C,WAAWJ,MAAA,IAAUI,WAAA,EAAa;MAChCU,aAAA,CAAcd,MAAA,EAAQtU,KAAK;IAC7B;EACF;EACA,SAASsV,cAAcZ,WAAA,EAA2C1U,KAAA,EAAgB;IAChF0U,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;IAC7C1U,KAAA,CAAM0S,GAAA,GAAM,EAAC;IACb1S,KAAA,CAAM2S,QAAA,GAAW,CAAC;IAClBwC,cAAA,CAAeT,WAAA,EAAa1U,KAAK;EACnC;EACA,SAASuV,iBAAiB9b,GAAA,EAASuG,KAAA,EAAgB;IACjD,OAAOwV,iBAAA,CAAkB,CAAC/b,GAAG,GAAGuG,KAAK;EACvC;EACA,SAASwV,kBAAkBlc,IAAA,EAAqB0G,KAAA,EAAgB;IAC9D,IAAIyV,SAAA,GAAY;IAChBnc,IAAA,CAAKkK,OAAA,CAAQ/J,GAAA,IAAO;MAClB,IAAIA,GAAA,IAAOuG,KAAA,CAAM2S,QAAA,EAAU;QACzB,OAAQ3S,KAAA,CAAM2S,QAAA,CAA2BlZ,GAAG;QAC5Cgc,SAAA,GAAY;MACd;IACF,CAAC;IACD,IAAIA,SAAA,EAAW;MACbzV,KAAA,CAAM0S,GAAA,GAAO1S,KAAA,CAAM0S,GAAA,CAAa5L,MAAA,CAAOmD,EAAA,IAAMA,EAAA,IAAMjK,KAAA,CAAM2S,QAAQ;IACnE;EACF;EACA,SAAS+C,iBAAiB1V,KAAA,EAAgB;IACxC/J,MAAA,CAAOC,MAAA,CAAO8J,KAAA,EAAO;MACnB0S,GAAA,EAAK,EAAC;MACNC,QAAA,EAAU,CAAC;IACb,CAAC;EACH;EACA,SAASgD,WAAWrc,IAAA,EAEjBsc,MAAA,EAAuB5V,KAAA,EAAmB;IAC3C,MAAM6V,SAAA,GAA2B7V,KAAA,CAAM2S,QAAA,CAA2BiD,MAAA,CAAO3L,EAAE;IAC3E,IAAI4L,SAAA,KAAa,QAAW;MAC1B,OAAO;IACT;IACA,MAAMd,OAAA,GAAa9e,MAAA,CAAOC,MAAA,CAAO,CAAC,GAAG2f,SAAA,EAAUD,MAAA,CAAOZ,OAAO;IAC7D,MAAMc,MAAA,GAASzB,aAAA,CAAcU,OAAA,EAAS3B,QAAQ;IAC9C,MAAM2C,SAAA,GAAYD,MAAA,KAAWF,MAAA,CAAO3L,EAAA;IACpC,IAAI8L,SAAA,EAAW;MACbzc,IAAA,CAAKsc,MAAA,CAAO3L,EAAE,IAAI6L,MAAA;MAClB,OAAQ9V,KAAA,CAAM2S,QAAA,CAA2BiD,MAAA,CAAO3L,EAAE;IACpD;IACA;IACCjK,KAAA,CAAM2S,QAAA,CAA2BmD,MAAM,IAAIf,OAAA;IAC5C,OAAOgB,SAAA;EACT;EACA,SAASC,iBAAiBJ,MAAA,EAAuB5V,KAAA,EAAgB;IAC/D,OAAOiW,iBAAA,CAAkB,CAACL,MAAM,GAAG5V,KAAK;EAC1C;EACA,SAASiW,kBAAkBC,OAAA,EAAuClW,KAAA,EAAgB;IAChF,MAAMmW,OAAA,GAEF,CAAC;IACL,MAAMC,gBAAA,GAEF,CAAC;IACLF,OAAA,CAAQ1S,OAAA,CAAQoS,MAAA,IAAU;MAExB,IAAIA,MAAA,CAAO3L,EAAA,IAAMjK,KAAA,CAAM2S,QAAA,EAAU;QAAA,IAAA0D,qBAAA;QAE/BD,gBAAA,CAAiBR,MAAA,CAAO3L,EAAE,IAAI;UAC5BA,EAAA,EAAI2L,MAAA,CAAO3L,EAAA;UAAA;UAAA;UAGX+K,OAAA,EAAApc,aAAA,CAAAA,aAAA,MAAAyd,qBAAA,GACKD,gBAAA,CAAiBR,MAAA,CAAO3L,EAAE,eAAAoM,qBAAA,uBAA1BA,qBAAA,CAA6BrB,OAAA,GAC7BY,MAAA,CAAOZ,OAAA;QAEd;MACF;IACF,CAAC;IACDkB,OAAA,GAAUjgB,MAAA,CAAO8K,MAAA,CAAOqV,gBAAgB;IACxC,MAAME,iBAAA,GAAoBJ,OAAA,CAAQ3f,MAAA,GAAS;IAC3C,IAAI+f,iBAAA,EAAmB;MACrB,MAAMC,YAAA,GAAeL,OAAA,CAAQpP,MAAA,CAAO8O,MAAA,IAAUD,UAAA,CAAWQ,OAAA,EAASP,MAAA,EAAQ5V,KAAK,CAAC,EAAEzJ,MAAA,GAAS;MAC3F,IAAIggB,YAAA,EAAc;QAChBvW,KAAA,CAAM0S,GAAA,GAAMzc,MAAA,CAAO8K,MAAA,CAAOf,KAAA,CAAM2S,QAAQ,EAAExW,GAAA,CAAIqa,CAAA,IAAKnC,aAAA,CAAcmC,CAAA,EAAQpD,QAAQ,CAAC;MACpF;IACF;EACF;EACA,SAASqD,iBAAiBnC,MAAA,EAAWtU,KAAA,EAAgB;IACnD,OAAO0W,iBAAA,CAAkB,CAACpC,MAAM,GAAGtU,KAAK;EAC1C;EACA,SAAS0W,kBAAkBhC,WAAA,EAA2C1U,KAAA,EAAgB;IACpF,MAAM,CAAC6U,KAAA,EAAOE,OAAO,IAAIN,yBAAA,CAAiCC,WAAA,EAAatB,QAAA,EAAUpT,KAAK;IACtFmV,cAAA,CAAeN,KAAA,EAAO7U,KAAK;IAC3BiW,iBAAA,CAAkBlB,OAAA,EAAS/U,KAAK;EAClC;EACA,OAAO;IACL2W,SAAA,EAAWhD,iCAAA,CAAkC+B,gBAAgB;IAC7DkB,MAAA,EAAQ9C,mBAAA,CAAoBoB,aAAa;IACzC2B,OAAA,EAAS/C,mBAAA,CAAoBqB,cAAc;IAC3C2B,MAAA,EAAQhD,mBAAA,CAAoBsB,aAAa;IACzC2B,OAAA,EAASjD,mBAAA,CAAoBuB,cAAc;IAC3CtC,MAAA,EAAQe,mBAAA,CAAoBwB,aAAa;IACzC0B,SAAA,EAAWlD,mBAAA,CAAoBkC,gBAAgB;IAC/CiB,UAAA,EAAYnD,mBAAA,CAAoBmC,iBAAiB;IACjDiB,SAAA,EAAWpD,mBAAA,CAAoB2C,gBAAgB;IAC/CU,UAAA,EAAYrD,mBAAA,CAAoB4C,iBAAiB;IACjDU,SAAA,EAAWtD,mBAAA,CAAoByB,gBAAgB;IAC/C8B,UAAA,EAAYvD,mBAAA,CAAoB0B,iBAAiB;EACnD;AACF;;;ACjIO,SAAS8B,gBAAmBC,WAAA,EAAkBxS,IAAA,EAASyS,kBAAA,EAAyC;EACrG,IAAIC,QAAA,GAAW;EACf,IAAIC,SAAA,GAAYH,WAAA,CAAYhhB,MAAA;EAC5B,OAAOkhB,QAAA,GAAWC,SAAA,EAAW;IAC3B,IAAIC,WAAA,GAAcF,QAAA,GAAWC,SAAA,KAAc;IAC3C,MAAME,WAAA,GAAcL,WAAA,CAAYI,WAAW;IAC3C,MAAME,GAAA,GAAML,kBAAA,CAAmBzS,IAAA,EAAM6S,WAAW;IAChD,IAAIC,GAAA,IAAO,GAAG;MACZJ,QAAA,GAAWE,WAAA,GAAc;IAC3B,OAAO;MACLD,SAAA,GAAYC,WAAA;IACd;EACF;EACA,OAAOF,QAAA;AACT;AACO,SAASK,OAAUP,WAAA,EAAkBxS,IAAA,EAASyS,kBAAA,EAAsC;EACzF,MAAMO,aAAA,GAAgBT,eAAA,CAAgBC,WAAA,EAAaxS,IAAA,EAAMyS,kBAAkB;EAC3ED,WAAA,CAAYhY,MAAA,CAAOwY,aAAA,EAAe,GAAGhT,IAAI;EACzC,OAAOwS,WAAA;AACT;AACO,SAASS,yBAAiD5E,QAAA,EAA6B6E,QAAA,EAAkD;EAE9I,MAAM;IACJb,SAAA;IACAC,UAAA;IACAV;EACF,IAAI1B,0BAAA,CAA2B7B,QAAQ;EACvC,SAAS8B,cAAcZ,MAAA,EAAWtU,KAAA,EAAgB;IAChD,OAAOmV,cAAA,CAAe,CAACb,MAAM,GAAGtU,KAAK;EACvC;EACA,SAASmV,eAAeT,WAAA,EAA2C1U,KAAA,EAAU4U,WAAA,EAA0B;IACrGF,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;IAC7C,MAAMwD,YAAA,GAAe,IAAI/a,GAAA,CAAQyX,WAAA,aAAAA,WAAA,cAAAA,WAAA,GAAeJ,UAAA,CAAWxU,KAAA,CAAM0S,GAAG,CAAC;IACrE,MAAMyF,MAAA,GAASzD,WAAA,CAAY5N,MAAA,CAAOsR,KAAA,IAAS,CAACF,YAAA,CAAa7b,GAAA,CAAIgY,aAAA,CAAc+D,KAAA,EAAOhF,QAAQ,CAAC,CAAC;IAC5F,IAAI+E,MAAA,CAAO5hB,MAAA,KAAW,GAAG;MACvB8hB,aAAA,CAAcrY,KAAA,EAAOmY,MAAM;IAC7B;EACF;EACA,SAAS/C,cAAcd,MAAA,EAAWtU,KAAA,EAAgB;IAChD,OAAOqV,cAAA,CAAe,CAACf,MAAM,GAAGtU,KAAK;EACvC;EACA,SAASqV,eAAeX,WAAA,EAA2C1U,KAAA,EAAgB;IACjF0U,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;IAC7C,IAAIA,WAAA,CAAYne,MAAA,KAAW,GAAG;MAC5B,WAAWwO,IAAA,IAAQ2P,WAAA,EAAa;QAC9B,OAAQ1U,KAAA,CAAM2S,QAAA,CAA2BS,QAAA,CAASrO,IAAI,CAAC;MACzD;MACAsT,aAAA,CAAcrY,KAAA,EAAO0U,WAAW;IAClC;EACF;EACA,SAASY,cAAcZ,WAAA,EAA2C1U,KAAA,EAAgB;IAChF0U,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;IAC7C1U,KAAA,CAAM2S,QAAA,GAAW,CAAC;IAClB3S,KAAA,CAAM0S,GAAA,GAAM,EAAC;IACbyC,cAAA,CAAeT,WAAA,EAAa1U,KAAA,EAAO,EAAE;EACvC;EACA,SAASgW,iBAAiBJ,MAAA,EAAuB5V,KAAA,EAAgB;IAC/D,OAAOiW,iBAAA,CAAkB,CAACL,MAAM,GAAG5V,KAAK;EAC1C;EACA,SAASiW,kBAAkBC,OAAA,EAAuClW,KAAA,EAAgB;IAChF,IAAIsY,cAAA,GAAiB;IACrB,IAAIC,WAAA,GAAc;IAClB,SAAS3C,MAAA,IAAUM,OAAA,EAAS;MAC1B,MAAM5B,MAAA,GAAyBtU,KAAA,CAAM2S,QAAA,CAA2BiD,MAAA,CAAO3L,EAAE;MACzE,IAAI,CAACqK,MAAA,EAAQ;QACX;MACF;MACAgE,cAAA,GAAiB;MACjBriB,MAAA,CAAOC,MAAA,CAAOoe,MAAA,EAAQsB,MAAA,CAAOZ,OAAO;MACpC,MAAMwD,KAAA,GAAQpF,QAAA,CAASkB,MAAM;MAC7B,IAAIsB,MAAA,CAAO3L,EAAA,KAAOuO,KAAA,EAAO;QAGvBD,WAAA,GAAc;QACd,OAAQvY,KAAA,CAAM2S,QAAA,CAA2BiD,MAAA,CAAO3L,EAAE;QAClD,MAAMwO,QAAA,GAAYzY,KAAA,CAAM0S,GAAA,CAAahZ,OAAA,CAAQkc,MAAA,CAAO3L,EAAE;QACtDjK,KAAA,CAAM0S,GAAA,CAAI+F,QAAQ,IAAID,KAAA;QACrBxY,KAAA,CAAM2S,QAAA,CAA2B6F,KAAK,IAAIlE,MAAA;MAC7C;IACF;IACA,IAAIgE,cAAA,EAAgB;MAClBD,aAAA,CAAcrY,KAAA,EAAO,EAAC,EAAGsY,cAAA,EAAgBC,WAAW;IACtD;EACF;EACA,SAAS9B,iBAAiBnC,MAAA,EAAWtU,KAAA,EAAgB;IACnD,OAAO0W,iBAAA,CAAkB,CAACpC,MAAM,GAAGtU,KAAK;EAC1C;EACA,SAAS0W,kBAAkBhC,WAAA,EAA2C1U,KAAA,EAAgB;IACpF,MAAM,CAAC6U,KAAA,EAAOE,OAAA,EAASJ,gBAAgB,IAAIF,yBAAA,CAAiCC,WAAA,EAAatB,QAAA,EAAUpT,KAAK;IACxG,IAAI6U,KAAA,CAAMte,MAAA,EAAQ;MAChB4e,cAAA,CAAeN,KAAA,EAAO7U,KAAA,EAAO2U,gBAAgB;IAC/C;IACA,IAAII,OAAA,CAAQxe,MAAA,EAAQ;MAClB0f,iBAAA,CAAkBlB,OAAA,EAAS/U,KAAK;IAClC;EACF;EACA,SAAS0Y,eAAepQ,CAAA,EAAuBqQ,CAAA,EAAuB;IACpE,IAAIrQ,CAAA,CAAE/R,MAAA,KAAWoiB,CAAA,CAAEpiB,MAAA,EAAQ;MACzB,OAAO;IACT;IACA,SAAS2T,CAAA,GAAI,GAAGA,CAAA,GAAI5B,CAAA,CAAE/R,MAAA,EAAQ2T,CAAA,IAAK;MACjC,IAAI5B,CAAA,CAAE4B,CAAC,MAAMyO,CAAA,CAAEzO,CAAC,GAAG;QACjB;MACF;MACA,OAAO;IACT;IACA,OAAO;EACT;EAEA,MAAMmO,aAAA,GAA+BA,CAACrY,KAAA,EAAO4Y,UAAA,EAAYN,cAAA,EAAgBC,WAAA,KAAgB;IACvF,MAAMM,eAAA,GAAkBrE,UAAA,CAAWxU,KAAA,CAAM2S,QAAQ;IACjD,MAAMmG,UAAA,GAAatE,UAAA,CAAWxU,KAAA,CAAM0S,GAAG;IACvC,MAAMqG,aAAA,GAAgB/Y,KAAA,CAAM2S,QAAA;IAC5B,IAAID,GAAA,GAAoBoG,UAAA;IACxB,IAAIP,WAAA,EAAa;MACf7F,GAAA,GAAM,IAAIvV,GAAA,CAAI2b,UAAU;IAC1B;IACA,IAAIE,cAAA,GAAsB,EAAC;IAC3B,WAAW/O,EAAA,IAAMyI,GAAA,EAAK;MACpB,MAAM4B,MAAA,GAASuE,eAAA,CAAgB5O,EAAE;MACjC,IAAIqK,MAAA,EAAQ;QACV0E,cAAA,CAAexZ,IAAA,CAAK8U,MAAM;MAC5B;IACF;IACA,MAAM2E,kBAAA,GAAqBD,cAAA,CAAeziB,MAAA,KAAW;IAGrD,WAAWwO,IAAA,IAAQ6T,UAAA,EAAY;MAC7BG,aAAA,CAAc3F,QAAA,CAASrO,IAAI,CAAC,IAAIA,IAAA;MAChC,IAAI,CAACkU,kBAAA,EAAoB;QAEvBnB,MAAA,CAAOkB,cAAA,EAAgBjU,IAAA,EAAMkT,QAAQ;MACvC;IACF;IACA,IAAIgB,kBAAA,EAAoB;MAEtBD,cAAA,GAAiBJ,UAAA,CAAWxZ,KAAA,CAAM,EAAE8Z,IAAA,CAAKjB,QAAQ;IACnD,WAAWK,cAAA,EAAgB;MAEzBU,cAAA,CAAeE,IAAA,CAAKjB,QAAQ;IAC9B;IACA,MAAMkB,YAAA,GAAeH,cAAA,CAAe7c,GAAA,CAAIiX,QAAQ;IAChD,IAAI,CAACsF,cAAA,CAAeI,UAAA,EAAYK,YAAY,GAAG;MAC7CnZ,KAAA,CAAM0S,GAAA,GAAMyG,YAAA;IACd;EACF;EACA,OAAO;IACL/B,SAAA;IACAC,UAAA;IACAV,SAAA;IACAC,MAAA,EAAQ9C,mBAAA,CAAoBoB,aAAa;IACzC8B,SAAA,EAAWlD,mBAAA,CAAoBkC,gBAAgB;IAC/CkB,SAAA,EAAWpD,mBAAA,CAAoB2C,gBAAgB;IAC/CK,MAAA,EAAQhD,mBAAA,CAAoBsB,aAAa;IACzC2B,OAAA,EAASjD,mBAAA,CAAoBuB,cAAc;IAC3CtC,MAAA,EAAQe,mBAAA,CAAoBwB,aAAa;IACzCuB,OAAA,EAAS/C,mBAAA,CAAoBqB,cAAc;IAC3C8B,UAAA,EAAYnD,mBAAA,CAAoBmC,iBAAiB;IACjDkB,UAAA,EAAYrD,mBAAA,CAAoB4C,iBAAiB;EACnD;AACF;;;ACrJO,SAAS0C,oBAAA,EAAoG;EAAA,IAA7Epf,OAAA,GAAAjE,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAA6C,CAAC;EACnF,MAAM;IACJqd,QAAA;IACAiG;EACF,IAAAzgB,aAAA;IACEygB,YAAA,EAAc;IACdjG,QAAA,EAAWkG,QAAA,IAAkBA,QAAA,CAASrP;EAAA,GACnCjQ,OAAA,CACL;EACA,MAAM6Y,YAAA,GAAewG,YAAA,GAAerB,wBAAA,CAAyB5E,QAAA,EAAUiG,YAAY,IAAIpE,0BAAA,CAA2B7B,QAAQ;EAC1H,MAAMmG,YAAA,GAAe3G,yBAAA,CAA0BC,YAAY;EAC3D,MAAM2G,gBAAA,GAAmBxG,sBAAA,CAAoC;EAC7D,OAAApa,aAAA,CAAAA,aAAA,CAAAA,aAAA;IACEwa,QAAA;IACAiG;EAAA,GACGE,YAAA,GACAC,gBAAA,GACA3G,YAAA;AAEP;;;AClCA,SAAS/a,QAAA,IAAA2hB,SAAA,QAAgB;;;ACDzB,IAAMC,IAAA,GAAO;AACb,IAAMC,QAAA,GAAW;AACjB,IAAMC,SAAA,GAAY;AAClB,IAAMC,SAAA,GAAY;AAGX,IAAMC,aAAA,WAAA5gB,MAAA,CAAwB2gB,SAAS;AACvC,IAAME,aAAA,WAAA7gB,MAAA,CAAwB0gB,SAAS;AACvC,IAAMI,iBAAA,MAAA9gB,MAAA,CAAuBygB,QAAQ,OAAAzgB,MAAA,CAAI2gB,SAAS;AAClD,IAAMI,iBAAA,MAAA/gB,MAAA,CAAuBygB,QAAQ,OAAAzgB,MAAA,CAAI0gB,SAAS;AAClD,IAAMM,cAAA,GAAN,MAAMA,cAAA,CAA0C;EAGrD9e,YAAmB+e,IAAA,EAA0B;IAAA5P,eAAA,eAFtC;IAAAA,eAAA;IAEY,KAAA4P,IAAA,GAAAA,IAAA;IACjB,KAAKvP,OAAA,MAAA1R,MAAA,CAAawgB,IAAI,OAAAxgB,MAAA,CAAI2gB,SAAS,gBAAA3gB,MAAA,CAAaihB,IAAI;EACtD;AACF;;;ACfO,IAAMC,cAAA,GAAuGA,CAACC,IAAA,EAAeC,QAAA,KAAqB;EACvJ,IAAI,OAAOD,IAAA,KAAS,YAAY;IAC9B,MAAM,IAAIE,SAAA,CAAU/hB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,EAAE,OAAAO,MAAA,CAAOohB,QAAQ,uBAAoB;EAC3H;AACF;AACO,IAAME,KAAA,GAAOhI,CAAA,KAAM,CAAC;AACpB,IAAMiI,cAAA,GAAiB,SAAAA,CAAKtO,OAAA,EAAoD;EAAA,IAA/BuO,OAAA,GAAA3kB,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAUykB,KAAA;EAChErO,OAAA,CAAQwO,KAAA,CAAMD,OAAO;EACrB,OAAOvO,OAAA;AACT;AACO,IAAMyO,sBAAA,GAAyBA,CAACC,WAAA,EAA0BC,QAAA,KAAmC;EAClGD,WAAA,CAAY5O,gBAAA,CAAiB,SAAS6O,QAAA,EAAU;IAC9C5O,IAAA,EAAM;EACR,CAAC;EACD,OAAO,MAAM2O,WAAA,CAAY1N,mBAAA,CAAoB,SAAS2N,QAAQ;AAChE;AAYO,IAAMC,yBAAA,GAA4BA,CAAKpP,eAAA,EAAkCK,MAAA,KAAoB;EAElG,MAAMR,MAAA,GAASG,eAAA,CAAgBH,MAAA;EAC/B,IAAIA,MAAA,CAAOH,OAAA,EAAS;IAClB;EACF;EAMA,IAAI,EAAE,YAAYG,MAAA,GAAS;IACzBvV,MAAA,CAAO+kB,cAAA,CAAexP,MAAA,EAAQ,UAAU;MACtCyP,UAAA,EAAY;MACZ5kB,KAAA,EAAO2V,MAAA;MACPkP,YAAA,EAAc;MACdC,QAAA,EAAU;IACZ,CAAC;EACH;EACA;EACCxP,eAAA,CAAgBI,KAAA,CAAkCC,MAAM;AAC3D;;;ACxCO,IAAMoP,cAAA,GAAkB5P,MAAA,IAA8B;EAC3D,IAAIA,MAAA,CAAOH,OAAA,EAAS;IAClB,MAAM;MACJW;IACF,IAAIR,MAAA;IACJ,MAAM,IAAI0O,cAAA,CAAelO,MAAM;EACjC;AACF;AAOO,SAASqP,eAAkB7P,MAAA,EAAuCW,OAAA,EAAiC;EACxG,IAAImP,OAAA,GAAUd,KAAA;EACd,OAAO,IAAI9N,OAAA,CAAW,CAACI,OAAA,EAASH,MAAA,KAAW;IACzC,MAAM4O,eAAA,GAAkBA,CAAA,KAAM5O,MAAA,CAAO,IAAIuN,cAAA,CAAe1O,MAAA,CAAOQ,MAAM,CAAC;IACtE,IAAIR,MAAA,CAAOH,OAAA,EAAS;MAClBkQ,eAAA,CAAgB;MAChB;IACF;IACAD,OAAA,GAAUV,sBAAA,CAAuBpP,MAAA,EAAQ+P,eAAe;IACxDpP,OAAA,CAAQqP,OAAA,CAAQ,MAAMF,OAAA,CAAQ,CAAC,EAAErO,IAAA,CAAKH,OAAA,EAASH,MAAM;EACvD,CAAC,EAAE6O,OAAA,CAAQ,MAAM;IAEfF,OAAA,GAAUd,KAAA;EACZ,CAAC;AACH;AASO,IAAMiB,OAAA,GAAU,MAAAA,CAAWC,KAAA,EAAwBC,OAAA,KAAiD;EACzG,IAAI;IACF,MAAMjP,OAAA,CAAQI,OAAA,CAAQ;IACtB,MAAMzW,KAAA,GAAQ,MAAMqlB,KAAA,CAAK;IACzB,OAAO;MACLE,MAAA,EAAQ;MACRvlB;IACF;EACF,SAAS0C,KAAA,EAAY;IACnB,OAAO;MACL6iB,MAAA,EAAQ7iB,KAAA,YAAiBmhB,cAAA,GAAiB,cAAc;MACxDnhB;IACF;EACF,UAAE;IACA4iB,OAAA,aAAAA,OAAA,eAAAA,OAAA,CAAU;EACZ;AACF;AASO,IAAME,WAAA,GAAmBrQ,MAAA,IAAwB;EACtD,OAAQW,OAAA,IAAoC;IAC1C,OAAOsO,cAAA,CAAeY,cAAA,CAAe7P,MAAA,EAAQW,OAAO,EAAEc,IAAA,CAAK6O,MAAA,IAAU;MACnEV,cAAA,CAAe5P,MAAM;MACrB,OAAOsQ,MAAA;IACT,CAAC,CAAC;EACJ;AACF;AAQO,IAAMC,WAAA,GAAevQ,MAAA,IAAwB;EAClD,MAAMwQ,KAAA,GAAQH,WAAA,CAAkBrQ,MAAM;EACtC,OAAQyQ,SAAA,IAAqC;IAC3C,OAAOD,KAAA,CAAM,IAAItP,OAAA,CAAcI,OAAA,IAAWlK,UAAA,CAAWkK,OAAA,EAASmP,SAAS,CAAC,CAAC;EAC3E;AACF;;;AH9EA,IAAM;EACJ/lB;AACF,IAAID,MAAA;AAIJ,IAAMimB,kBAAA,GAAqB,CAAC;AAC5B,IAAMC,GAAA,GAAM;AACZ,IAAMC,UAAA,GAAaA,CAACC,iBAAA,EAAmDC,sBAAA,KAA2C;EAChH,MAAMC,eAAA,GAAmBC,UAAA,IAAgC5B,sBAAA,CAAuByB,iBAAA,EAAmB,MAAMtB,yBAAA,CAA0ByB,UAAA,EAAYH,iBAAA,CAAkBrQ,MAAM,CAAC;EACxK,OAAO,CAAKyQ,YAAA,EAAqCC,IAAA,KAAsC;IACrFtC,cAAA,CAAeqC,YAAA,EAAc,cAAc;IAC3C,MAAME,oBAAA,GAAuB,IAAI/Q,eAAA,CAAgB;IACjD2Q,eAAA,CAAgBI,oBAAoB;IACpC,MAAMne,MAAA,GAASid,OAAA,CAAW,YAAwB;MAChDL,cAAA,CAAeiB,iBAAiB;MAChCjB,cAAA,CAAeuB,oBAAA,CAAqBnR,MAAM;MAC1C,MAAMoR,OAAA,GAAU,MAAMH,YAAA,CAAa;QACjCT,KAAA,EAAOH,WAAA,CAAYc,oBAAA,CAAqBnR,MAAM;QAC9CqR,KAAA,EAAOd,WAAA,CAAYY,oBAAA,CAAqBnR,MAAM;QAC9CA,MAAA,EAAQmR,oBAAA,CAAqBnR;MAC/B,CAAC;MACD4P,cAAA,CAAeuB,oBAAA,CAAqBnR,MAAM;MAC1C,OAAOoR,OAAA;IACT,GAAG,MAAM7B,yBAAA,CAA0B4B,oBAAA,EAAsB5C,aAAa,CAAC;IACvE,IAAI2C,IAAA,aAAAA,IAAA,eAAAA,IAAA,CAAMI,QAAA,EAAU;MAClBR,sBAAA,CAAuB9c,IAAA,CAAKhB,MAAA,CAAOmc,KAAA,CAAMH,KAAI,CAAC;IAChD;IACA,OAAO;MACLhc,MAAA,EAAQqd,WAAA,CAA2BQ,iBAAiB,EAAE7d,MAAM;MAC5Due,OAAA,EAAS;QACPhC,yBAAA,CAA0B4B,oBAAA,EAAsB7C,aAAa;MAC/D;IACF;EACF;AACF;AACA,IAAMkD,iBAAA,GAAoBA,CAAKC,cAAA,EAAwEzR,MAAA,KAAwC;EAQ7I,MAAM0R,IAAA,GAAO,MAAAA,CAA2CC,SAAA,EAAcza,OAAA,KAAgC;IACpG0Y,cAAA,CAAe5P,MAAM;IAGrB,IAAI3H,WAAA,GAAmCA,CAAA,KAAM,CAAC;IAC9C,MAAMuZ,YAAA,GAAe,IAAI1Q,OAAA,CAAwB,CAACI,OAAA,EAASH,MAAA,KAAW;MAEpE,IAAI0Q,aAAA,GAAgBJ,cAAA,CAAe;QACjCE,SAAA;QACAG,MAAA,EAAQA,CAACnkB,MAAA,EAAQokB,WAAA,KAAsB;UAErCA,WAAA,CAAY1Z,WAAA,CAAY;UAExBiJ,OAAA,CAAQ,CAAC3T,MAAA,EAAQokB,WAAA,CAAYxd,QAAA,CAAS,GAAGwd,WAAA,CAAYC,gBAAA,CAAiB,CAAC,CAAC;QAC1E;MACF,CAAC;MACD3Z,WAAA,GAAcA,CAAA,KAAM;QAClBwZ,aAAA,CAAc;QACd1Q,MAAA,CAAO;MACT;IACF,CAAC;IACD,MAAM8Q,QAAA,GAAwD,CAACL,YAAY;IAC3E,IAAI1a,OAAA,IAAW,MAAM;MACnB+a,QAAA,CAASje,IAAA,CAAK,IAAIkN,OAAA,CAAcI,OAAA,IAAWlK,UAAA,CAAWkK,OAAA,EAASpK,OAAA,EAAS,IAAI,CAAC,CAAC;IAChF;IACA,IAAI;MACF,MAAMoZ,MAAA,GAAS,MAAMT,cAAA,CAAe7P,MAAA,EAAQkB,OAAA,CAAQG,IAAA,CAAK4Q,QAAQ,CAAC;MAClErC,cAAA,CAAe5P,MAAM;MACrB,OAAOsQ,MAAA;IACT,UAAE;MAEAjY,WAAA,CAAY;IACd;EACF;EACA,OAAQ,CAACsZ,SAAA,EAAoCza,OAAA,KAAgC+X,cAAA,CAAeyC,IAAA,CAAKC,SAAA,EAAWza,OAAO,CAAC;AACtH;AACA,IAAMgb,yBAAA,GAA6B1jB,OAAA,IAAwC;EACzE,IAAI;IACF7B,IAAA;IACAE,aAAA;IACA8N,OAAA;IACAgX,SAAA;IACAG;EACF,IAAItjB,OAAA;EACJ,IAAI7B,IAAA,EAAM;IACRglB,SAAA,GAAYjlB,YAAA,CAAaC,IAAI,EAAEF,KAAA;EACjC,WAAWI,aAAA,EAAe;IACxBF,IAAA,GAAOE,aAAA,CAAeF,IAAA;IACtBglB,SAAA,GAAY9kB,aAAA,CAAcJ,KAAA;EAC5B,WAAWkO,OAAA,EAAS;IAClBgX,SAAA,GAAYhX,OAAA;EACd,WAAWgX,SAAA,EAAW,CAEtB,OAAO;IACL,MAAM,IAAI5kB,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,EAAE,IAAI,yFAAyF;EACjL;EACAyhB,cAAA,CAAekD,MAAA,EAAQ,kBAAkB;EACzC,OAAO;IACLH,SAAA;IACAhlB,IAAA;IACAmlB;EACF;AACF;AAGO,IAAMK,mBAAA,GAAwE,eAAAznB,MAAA,CAAQ8D,OAAA,IAAwC;EACnI,MAAM;IACJ7B,IAAA;IACAglB,SAAA;IACAG;EACF,IAAII,yBAAA,CAA0B1jB,OAAO;EACrC,MAAM4jB,KAAA,GAAgC;IACpC3T,EAAA,EAAIF,MAAA,CAAO;IACXuT,MAAA;IACAnlB,IAAA;IACAglB,SAAA;IACAvU,OAAA,EAAS,mBAAIzL,GAAA,CAAqB;IAClC0G,WAAA,EAAaA,CAAA,KAAM;MACjB,MAAM,IAAItL,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,6BAA6B;IACtH;EACF;EACA,OAAOilB,KAAA;AACT,GAAG;EACDjnB,SAAA,EAAWA,CAAA,KAAMgnB;AACnB,CAAC;AACD,IAAME,iBAAA,GAAoBA,CAACC,WAAA,EAAyC9jB,OAAA,KAAwC;EAC1G,MAAM;IACJ7B,IAAA;IACAmlB,MAAA;IACAH;EACF,IAAIO,yBAAA,CAA0B1jB,OAAO;EACrC,OAAOvD,KAAA,CAAMsnB,IAAA,CAAKD,WAAA,CAAY/c,MAAA,CAAO,CAAC,EAAEid,IAAA,CAAKJ,KAAA,IAAS;IACpD,MAAMK,oBAAA,GAAuB,OAAO9lB,IAAA,KAAS,WAAWylB,KAAA,CAAMzlB,IAAA,KAASA,IAAA,GAAOylB,KAAA,CAAMT,SAAA,KAAcA,SAAA;IAClG,OAAOc,oBAAA,IAAwBL,KAAA,CAAMN,MAAA,KAAWA,MAAA;EAClD,CAAC;AACH;AACA,IAAMY,qBAAA,GAAyBN,KAAA,IAA2D;EACxFA,KAAA,CAAMhV,OAAA,CAAQpF,OAAA,CAAQgZ,UAAA,IAAc;IAClCzB,yBAAA,CAA0ByB,UAAA,EAAYxC,iBAAiB;EACzD,CAAC;AACH;AACA,IAAMmE,6BAAA,GAAiCL,WAAA,IAA4C;EACjF,OAAO,MAAM;IACXA,WAAA,CAAYta,OAAA,CAAQ0a,qBAAqB;IACzCJ,WAAA,CAAYM,KAAA,CAAM;EACpB;AACF;AASA,IAAMC,iBAAA,GAAoBA,CAACC,YAAA,EAAoCC,aAAA,EAAwBC,SAAA,KAAuC;EAC5H,IAAI;IACFF,YAAA,CAAaC,aAAA,EAAeC,SAAS;EACvC,SAASC,iBAAA,EAAmB;IAG1B7b,UAAA,CAAW,MAAM;MACf,MAAM6b,iBAAA;IACR,GAAG,CAAC;EACN;AACF;AAKO,IAAMC,WAAA,GAA6B,eAAAxoB,MAAA,CAAsB,eAAAgC,YAAA,IAAAgB,MAAA,CAAgBijB,GAAG,SAAM,GAAG;EAC1FxlB,SAAA,EAAWA,CAAA,KAAM+nB;AACnB,CAAC;AAKM,IAAMC,iBAAA,GAAmC,eAAAzmB,YAAA,IAAAgB,MAAA,CAAgBijB,GAAG,eAAY;AAKxE,IAAMyC,cAAA,GAAgC,eAAA1oB,MAAA,CAAsB,eAAAgC,YAAA,IAAAgB,MAAA,CAAgBijB,GAAG,YAAS,GAAG;EAChGxlB,SAAA,EAAWA,CAAA,KAAMioB;AACnB,CAAC;AACD,IAAMC,mBAAA,GAA4C,SAAAA,CAAA,EAAwB;EAAA,SAAAC,MAAA,GAAA/oB,SAAA,CAAAQ,MAAA,EAApBqb,IAAA,OAAAnb,KAAA,CAAAqoB,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;IAAAnN,IAAA,CAAAmN,MAAA,IAAAhpB,SAAA,CAAAgpB,MAAA;EAAA;EACpD5kB,OAAA,CAAQpB,KAAA,IAAAG,MAAA,CAASijB,GAAG,aAAU,GAAGvK,IAAI;AACvC;AAKO,IAAMoN,wBAAA,GAA2B,SAAAA,CAAA,EAAoN;EAAA,IAA3EC,iBAAA,GAAAlpB,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAoE,CAAC;EACpP,MAAM+nB,WAAA,GAAc,mBAAI9N,GAAA,CAA2B;EACnD,MAAM;IACJvE,KAAA;IACAiP,OAAA,GAAUmE;EACZ,IAAII,iBAAA;EACJ7E,cAAA,CAAeM,OAAA,EAAS,SAAS;EACjC,MAAMwE,WAAA,GAAetB,KAAA,IAAyB;IAC5CA,KAAA,CAAM/Z,WAAA,GAAc,MAAMia,WAAA,CAAYha,MAAA,CAAO8Z,KAAA,CAAM3T,EAAE;IACrD6T,WAAA,CAAYvhB,GAAA,CAAIqhB,KAAA,CAAM3T,EAAA,EAAI2T,KAAK;IAC/B,OAAQuB,aAAA,IAA+C;MACrDvB,KAAA,CAAM/Z,WAAA,CAAY;MAClB,IAAIsb,aAAA,aAAAA,aAAA,eAAAA,aAAA,CAAeC,YAAA,EAAc;QAC/BlB,qBAAA,CAAsBN,KAAK;MAC7B;IACF;EACF;EACA,MAAMX,cAAA,GAAmBjjB,OAAA,IAAwC;IAAA,IAAAqlB,kBAAA;IAC/D,MAAMzB,KAAA,IAAAyB,kBAAA,GAAQxB,iBAAA,CAAkBC,WAAA,EAAa9jB,OAAO,eAAAqlB,kBAAA,cAAAA,kBAAA,GAAK1B,mBAAA,CAAoB3jB,OAAc;IAC3F,OAAOklB,WAAA,CAAYtB,KAAK;EAC1B;EACA1nB,MAAA,CAAO+mB,cAAA,EAAgB;IACrBtmB,SAAA,EAAWA,CAAA,KAAMsmB;EACnB,CAAC;EACD,MAAMI,aAAA,GAAiBrjB,OAAA,IAA8E;IACnG,MAAM4jB,KAAA,GAAQC,iBAAA,CAAkBC,WAAA,EAAa9jB,OAAO;IACpD,IAAI4jB,KAAA,EAAO;MACTA,KAAA,CAAM/Z,WAAA,CAAY;MAClB,IAAI7J,OAAA,CAAQolB,YAAA,EAAc;QACxBlB,qBAAA,CAAsBN,KAAK;MAC7B;IACF;IACA,OAAO,CAAC,CAACA,KAAA;EACX;EACA1nB,MAAA,CAAOmnB,aAAA,EAAe;IACpB1mB,SAAA,EAAWA,CAAA,KAAM0mB;EACnB,CAAC;EACD,MAAMiC,cAAA,GAAiB,MAAAA,CAAO1B,KAAA,EAAwDzkB,MAAA,EAAiBomB,GAAA,EAAoB/B,gBAAA,KAAsC;IAC/J,MAAMgC,sBAAA,GAAyB,IAAI5T,eAAA,CAAgB;IACnD,MAAMsR,IAAA,GAAOF,iBAAA,CAAkBC,cAAA,EAA6CuC,sBAAA,CAAuBhU,MAAM;IACzG,MAAMiU,gBAAA,GAAmC,EAAC;IAC1C,IAAI;MACF7B,KAAA,CAAMhV,OAAA,CAAQvL,GAAA,CAAImiB,sBAAsB;MACxC,MAAM9S,OAAA,CAAQI,OAAA,CAAQ8Q,KAAA,CAAMN,MAAA,CAAOnkB,MAAA;MAAA;MAEnCjD,MAAA,CAAO,CAAC,GAAGqpB,GAAA,EAAK;QACd/B,gBAAA;QACAjS,SAAA,EAAWA,CAAC4R,SAAA,EAAsCza,OAAA,KAAqBwa,IAAA,CAAKC,SAAA,EAAWza,OAAO,EAAEuK,IAAA,CAAKyS,OAAO;QAC5GxC,IAAA;QACAL,KAAA,EAAOd,WAAA,CAAYyD,sBAAA,CAAuBhU,MAAM;QAChDwQ,KAAA,EAAOH,WAAA,CAAiB2D,sBAAA,CAAuBhU,MAAM;QACrDC,KAAA;QACAD,MAAA,EAAQgU,sBAAA,CAAuBhU,MAAA;QAC/BmU,IAAA,EAAMvD,UAAA,CAAWoD,sBAAA,CAAuBhU,MAAA,EAAQiU,gBAAgB;QAChE5b,WAAA,EAAa+Z,KAAA,CAAM/Z,WAAA;QACnBH,SAAA,EAAWA,CAAA,KAAM;UACfoa,WAAA,CAAYvhB,GAAA,CAAIqhB,KAAA,CAAM3T,EAAA,EAAI2T,KAAK;QACjC;QACAM,qBAAA,EAAuBA,CAAA,KAAM;UAC3BN,KAAA,CAAMhV,OAAA,CAAQpF,OAAA,CAAQ,CAACgZ,UAAA,EAAYrd,CAAA,EAAG5C,GAAA,KAAQ;YAC5C,IAAIigB,UAAA,KAAegD,sBAAA,EAAwB;cACzCzE,yBAAA,CAA0ByB,UAAA,EAAYxC,iBAAiB;cACvDzd,GAAA,CAAIuH,MAAA,CAAO0Y,UAAU;YACvB;UACF,CAAC;QACH;QACAO,MAAA,EAAQA,CAAA,KAAM;UACZhC,yBAAA,CAA0ByE,sBAAA,EAAwBxF,iBAAiB;UACnE4D,KAAA,CAAMhV,OAAA,CAAQ9E,MAAA,CAAO0b,sBAAsB;QAC7C;QACAI,gBAAA,EAAkBA,CAAA,KAAM;UACtBxE,cAAA,CAAeoE,sBAAA,CAAuBhU,MAAM;QAC9C;MACF,CAAC,CAAC,CAAC;IACL,SAASqU,aAAA,EAAe;MACtB,IAAI,EAAEA,aAAA,YAAyB3F,cAAA,GAAiB;QAC9CmE,iBAAA,CAAkB3D,OAAA,EAASmF,aAAA,EAAe;UACxCC,QAAA,EAAU;QACZ,CAAC;MACH;IACF,UAAE;MACA,MAAMpT,OAAA,CAAQqT,GAAA,CAAIN,gBAAgB;MAClC1E,yBAAA,CAA0ByE,sBAAA,EAAwBvF,iBAAiB;MACnE2D,KAAA,CAAMhV,OAAA,CAAQ9E,MAAA,CAAO0b,sBAAsB;IAC7C;EACF;EACA,MAAMQ,uBAAA,GAA0B7B,6BAAA,CAA8BL,WAAW;EACzE,MAAMtZ,UAAA,GAAyE+a,GAAA,IAAOtlB,IAAA,IAAQd,MAAA,IAAU;IACtG,IAAI,CAACsgB,SAAA,CAAStgB,MAAM,GAAG;MAErB,OAAOc,IAAA,CAAKd,MAAM;IACpB;IACA,IAAIulB,WAAA,CAAYzmB,KAAA,CAAMkB,MAAM,GAAG;MAC7B,OAAO8jB,cAAA,CAAe9jB,MAAA,CAAON,OAAc;IAC7C;IACA,IAAI8lB,iBAAA,CAAkB1mB,KAAA,CAAMkB,MAAM,GAAG;MACnC6mB,uBAAA,CAAwB;MACxB;IACF;IACA,IAAIpB,cAAA,CAAe3mB,KAAA,CAAMkB,MAAM,GAAG;MAChC,OAAOkkB,aAAA,CAAclkB,MAAA,CAAON,OAAO;IACrC;IAGA,IAAIonB,aAAA,GAAuDV,GAAA,CAAIxf,QAAA,CAAS;IAIxE,MAAMyd,gBAAA,GAAmBA,CAAA,KAAiB;MACxC,IAAIyC,aAAA,KAAkB/D,kBAAA,EAAoB;QACxC,MAAM,IAAI3jB,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,OAAAO,MAAA,CAAOijB,GAAG,wDAAqD;MACpJ;MACA,OAAO8D,aAAA;IACT;IACA,IAAIzhB,MAAA;IACJ,IAAI;MAEFA,MAAA,GAASvE,IAAA,CAAKd,MAAM;MACpB,IAAI2kB,WAAA,CAAY9T,IAAA,GAAO,GAAG;QACxB,MAAMkW,YAAA,GAAeX,GAAA,CAAIxf,QAAA,CAAS;QAElC,MAAMogB,eAAA,GAAkB1pB,KAAA,CAAMsnB,IAAA,CAAKD,WAAA,CAAY/c,MAAA,CAAO,CAAC;QACvD,WAAW6c,KAAA,IAASuC,eAAA,EAAiB;UACnC,IAAIC,WAAA,GAAc;UAClB,IAAI;YACFA,WAAA,GAAcxC,KAAA,CAAMT,SAAA,CAAUhkB,MAAA,EAAQ+mB,YAAA,EAAcD,aAAa;UACnE,SAASI,cAAA,EAAgB;YACvBD,WAAA,GAAc;YACd/B,iBAAA,CAAkB3D,OAAA,EAAS2F,cAAA,EAAgB;cACzCP,QAAA,EAAU;YACZ,CAAC;UACH;UACA,IAAI,CAACM,WAAA,EAAa;YAChB;UACF;UACAd,cAAA,CAAe1B,KAAA,EAAOzkB,MAAA,EAAQomB,GAAA,EAAK/B,gBAAgB;QACrD;MACF;IACF,UAAE;MAEAyC,aAAA,GAAgB/D,kBAAA;IAClB;IACA,OAAO1d,MAAA;EACT;EACA,OAAO;IACLgG,UAAA;IACAyY,cAAA;IACAI,aAAA;IACAiD,cAAA,EAAgBN;EAClB;AACF;;;AIvWA,SAASjpB,OAAA,IAAAwpB,QAAA,QAAe;AAOxB,IAAMC,qBAAA,GAA8Ghc,UAAA,KAA4F;EAC9MA,UAAA;EACAic,OAAA,EAAS,mBAAIzQ,GAAA,CAAI;AACnB;AACA,IAAM0Q,aAAA,GAAiBC,UAAA,IAAwBxnB,MAAA;EAAA,IAAAynB,aAAA;EAAA,OAI1C,CAAAznB,MAAA,aAAAA,MAAA,gBAAAynB,aAAA,GAAAznB,MAAA,CAAQL,IAAA,cAAA8nB,aAAA,uBAARA,aAAA,CAAcD,UAAA,MAAeA,UAAA;AAAA;AAC3B,IAAME,uBAAA,GAA0BA,CAAA,KAA2I;EAChL,MAAMF,UAAA,GAAa5W,MAAA,CAAO;EAC1B,MAAM+W,aAAA,GAAgB,mBAAI9Q,GAAA,CAAgF;EAC1G,MAAM+Q,cAAA,GAAiB9qB,MAAA,CAAOC,MAAA,CAAOgC,YAAA,CAAa,yBAAyB;IAAA,SAAA8oB,MAAA,GAAAjrB,SAAA,CAAAQ,MAAA,EAAI0qB,WAAA,OAAAxqB,KAAA,CAAAuqB,MAAA,GAAAE,MAAA,MAAAA,MAAA,GAAAF,MAAA,EAAAE,MAAA;MAAAD,WAAA,CAAAC,MAAA,IAAAnrB,SAAA,CAAAmrB,MAAA;IAAA;IAAA,OAAyD;MACtIroB,OAAA,EAASooB,WAAA;MACTnoB,IAAA,EAAM;QACJ6nB;MACF;IACF;EAAA,CAAE,GAAG;IACHhqB,SAAA,EAAWA,CAAA,KAAMoqB;EACnB,CAAC;EACD,MAAMI,aAAA,GAAgBlrB,MAAA,CAAOC,MAAA,CAAO,SAASkrB,eAAA,EAAsE;IAAA,SAAAC,MAAA,GAAAtrB,SAAA,CAAAQ,MAAA,EAArD0qB,WAAA,OAAAxqB,KAAA,CAAA4qB,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAAL,WAAA,CAAAK,MAAA,IAAAvrB,SAAA,CAAAurB,MAAA;IAAA;IAC5DL,WAAA,CAAYzd,OAAA,CAAQyB,WAAA,IAAc;MAChC/I,mBAAA,CAAoB4kB,aAAA,EAAe7b,WAAA,EAAYub,qBAAqB;IACtE,CAAC;EACH,GAAG;IACD7pB,SAAA,EAAWA,CAAA,KAAMwqB;EACnB,CAAC;EACD,MAAMI,kBAAA,GAA0DhC,GAAA,IAAO;IACrE,MAAMiC,iBAAA,GAAoB/qB,KAAA,CAAMsnB,IAAA,CAAK+C,aAAA,CAAc/f,MAAA,CAAO,CAAC,EAAE5E,GAAA,CAAIyhB,KAAA,IAAS1hB,mBAAA,CAAoB0hB,KAAA,CAAM6C,OAAA,EAASlB,GAAA,EAAK3B,KAAA,CAAMpZ,UAAU,CAAC;IACnI,OAAO+b,QAAA,CAAQ,GAAGiB,iBAAiB;EACrC;EACA,MAAMC,gBAAA,GAAmB7Z,OAAA,CAAQmZ,cAAA,EAAgBL,aAAA,CAAcC,UAAU,CAAC;EAC1E,MAAMnc,UAAA,GAAqD+a,GAAA,IAAOtlB,IAAA,IAAQd,MAAA,IAAU;IAClF,IAAIsoB,gBAAA,CAAiBtoB,MAAM,GAAG;MAC5BgoB,aAAA,CAAc,GAAGhoB,MAAA,CAAON,OAAO;MAC/B,OAAO0mB,GAAA,CAAIxb,QAAA;IACb;IACA,OAAOwd,kBAAA,CAAmBhC,GAAG,EAAEtlB,IAAI,EAAEd,MAAM;EAC7C;EACA,OAAO;IACLqL,UAAA;IACA2c,aAAA;IACAJ,cAAA;IACAJ;EACF;AACF;;;ACnDA,SAAS1pB,eAAA,IAAAyqB,gBAAA,QAAuB;AAqOhC,IAAMC,WAAA,GAAeC,cAAA,IAA8E,iBAAiBA,cAAA,IAAkB,OAAOA,cAAA,CAAevT,WAAA,KAAgB;AAC5K,IAAMwT,WAAA,GAAeC,MAAA,IAA6CA,MAAA,CAAOjY,OAAA,CAAQkY,UAAA,IAAcJ,WAAA,CAAYI,UAAU,IAAI,CAAC,CAACA,UAAA,CAAW1T,WAAA,EAAa0T,UAAA,CAAWxd,OAAO,CAAU,IAAItO,MAAA,CAAO2K,OAAA,CAAQmhB,UAAU,CAAC;AAC7M,IAAMC,cAAA,GAAiBzmB,MAAA,CAAOmS,GAAA,CAAI,0BAA0B;AAC5D,IAAMuU,YAAA,GAAgB5rB,KAAA,IAAe,CAAC,CAACA,KAAA,IAAS,CAAC,CAACA,KAAA,CAAM2rB,cAAc;AACtE,IAAME,aAAA,GAAgB,mBAAIhS,OAAA,CAAwB;AAClD,IAAMiS,gBAAA,GAAmBA,CAAwBniB,KAAA,EAAcoiB,UAAA,EAAmDC,iBAAA,KAAoDnmB,mBAAA,CAAoBgmB,aAAA,EAAeliB,KAAA,EAAO,MAAM,IAAIsiB,KAAA,CAAMtiB,KAAA,EAAO;EACrO1D,GAAA,EAAKA,CAACimB,MAAA,EAAQC,IAAA,EAAMC,QAAA,KAAa;IAC/B,IAAID,IAAA,KAASR,cAAA,EAAgB,OAAOO,MAAA;IACpC,MAAM/jB,MAAA,GAASkkB,OAAA,CAAQpmB,GAAA,CAAIimB,MAAA,EAAQC,IAAA,EAAMC,QAAQ;IACjD,IAAI,OAAOjkB,MAAA,KAAW,aAAa;MACjC,MAAMmkB,MAAA,GAASN,iBAAA,CAAkBG,IAAI;MACrC,IAAI,OAAOG,MAAA,KAAW,aAAa,OAAOA,MAAA;MAC1C,MAAMpe,OAAA,GAAU6d,UAAA,CAAWI,IAAI;MAC/B,IAAIje,OAAA,EAAS;QAEX,MAAMqe,aAAA,GAAgBre,OAAA,CAAQ,QAAW;UACvCpM,IAAA,EAAM4R,MAAA,CAAO;QACf,CAAC;QACD,IAAI,OAAO6Y,aAAA,KAAkB,aAAa;UACxC,MAAM,IAAIrqB,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,EAAE,mCAAAO,MAAA,CAAkCspB,IAAA,CAAKvpB,QAAA,CAAS,CAAC,uRAAuS;QAC5a;QACAopB,iBAAA,CAAkBG,IAAI,IAAII,aAAA;QAC1B,OAAOA,aAAA;MACT;IACF;IACA,OAAOpkB,MAAA;EACT;AACF,CAAC,CAAC;AACF,IAAMrJ,QAAA,GAAY6K,KAAA,IAAe;EAC/B,IAAI,CAACiiB,YAAA,CAAajiB,KAAK,GAAG;IACxB,MAAM,IAAIzH,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,sCAAsC;EAC/H;EACA,OAAOqH,KAAA,CAAMgiB,cAAc;AAC7B;AACA,IAAMa,WAAA,GAAc,CAAC;AACrB,IAAMC,WAAA,GAA4C,SAAAA,CAAA;EAAA,IAAC9iB,KAAA,GAAAjK,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAQ8sB,WAAA;EAAA,OAAgB7iB,KAAA;AAAA;AACpE,SAAS+iB,cAAA,EAAkI;EAAA,SAAAC,MAAA,GAAAjtB,SAAA,CAAAQ,MAAA,EAAhEurB,MAAA,OAAArrB,KAAA,CAAAusB,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;IAAAnB,MAAA,CAAAmB,MAAA,IAAAltB,SAAA,CAAAktB,MAAA;EAAA;EAChF,MAAMb,UAAA,GAAansB,MAAA,CAAOitB,WAAA,CAAqBrB,WAAA,CAAYC,MAAM,CAAC;EAClE,MAAMqB,UAAA,GAAaA,CAAA,KAAMltB,MAAA,CAAOqD,IAAA,CAAK8oB,UAAU,EAAE7rB,MAAA,GAASmrB,gBAAA,CAAgBU,UAAU,IAAIU,WAAA;EACxF,IAAIve,OAAA,GAAU4e,UAAA,CAAW;EACzB,SAASC,gBAAgBpjB,KAAA,EAAgC7G,MAAA,EAAuB;IAC9E,OAAOoL,OAAA,CAAQvE,KAAA,EAAO7G,MAAM;EAC9B;EACAiqB,eAAA,CAAgBC,oBAAA,GAAuB,MAAMD,eAAA;EAC7C,MAAMf,iBAAA,GAAkD,CAAC;EACzD,MAAM7Q,MAAA,GAAS,SAAAA,CAACpS,KAAA,EAA2E;IAAA,IAAtDgS,MAAA,GAAArb,SAAA,CAAAQ,MAAA,QAAAR,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAuB,CAAC;IAC3D,MAAM;MACJsY,WAAA;MACA9J,OAAA,EAAS+e;IACX,IAAIlkB,KAAA;IACJ,MAAMmkB,cAAA,GAAiBnB,UAAA,CAAW/T,WAAW;IAC7C,IAAI,CAAC+C,MAAA,CAAOoS,gBAAA,IAAoBD,cAAA,IAAkBA,cAAA,KAAmBD,eAAA,EAAiB;MACpF,IAAI,OAAO9qB,OAAA,KAAY,eAAeA,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAe;QAC5EyB,OAAA,CAAQpB,KAAA,yDAAAG,MAAA,CAAgEmV,WAAW,iDAAgD;MACrI;MACA,OAAO+U,eAAA;IACT;IACA,IAAIhS,MAAA,CAAOoS,gBAAA,IAAoBD,cAAA,KAAmBD,eAAA,EAAiB;MACjE,OAAOjB,iBAAA,CAAkBhU,WAAW;IACtC;IACA+T,UAAA,CAAW/T,WAAW,IAAIiV,eAAA;IAC1B/e,OAAA,GAAU4e,UAAA,CAAW;IACrB,OAAOC,eAAA;EACT;EACA,MAAMjtB,QAAA,GAAWF,MAAA,CAAOC,MAAA,CAAO,SAASutB,aAAkEC,UAAA,EAAkDhT,WAAA,EAA8D;IACxN,OAAO,SAASiT,UAAS3jB,KAAA,EAA6B;MAAA,SAAA4jB,MAAA,GAAA7tB,SAAA,CAAAQ,MAAA,EAAZqb,IAAA,OAAAnb,KAAA,CAAAmtB,MAAA,OAAAA,MAAA,WAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;QAAAjS,IAAA,CAAAiS,MAAA,QAAA9tB,SAAA,CAAA8tB,MAAA;MAAA;MACxC,OAAOH,UAAA,CAAWvB,gBAAA,CAAiBzR,WAAA,GAAcA,WAAA,CAAY1Q,KAAA,EAAc,GAAG4R,IAAI,IAAI5R,KAAA,EAAOoiB,UAAA,EAAYC,iBAAiB,GAAG,GAAGzQ,IAAI;IACtI;EACF,GAAG;IACDzc;EACF,CAAC;EACD,OAAOc,MAAA,CAAOC,MAAA,CAAOktB,eAAA,EAAiB;IACpC5R,MAAA;IACArb;EACF,CAAC;AACH;;;AC3SO,SAASwC,uBAAuBwhB,IAAA,EAAc;EACnD,wCAAAjhB,MAAA,CAAwCihB,IAAI,uDAAAjhB,MAAA,CAAoDihB,IAAI;AACtG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}