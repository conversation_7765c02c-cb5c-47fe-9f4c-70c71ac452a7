{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from) {\n  for (var i = 0, il = from.length, j = to.length; i < il; i++, j++) to[j] = from[i];\n  return to;\n};\nimport { ClassRegistry } from './class-registry';\nimport { Registry } from './registry';\nimport { CustomTransformerRegistry } from './custom-transformer-registry';\nimport { applyReferentialEqualityAnnotations, applyValueAnnotations, generateReferentialEqualityAnnotations, walker } from './plainer';\nimport { copy } from 'copy-anything';\nvar SuperJSON = /** @class */function () {\n  /**\n   * @param dedupeReferentialEqualities  If true, SuperJSON will make sure only one instance of referentially equal objects are serialized and the rest are replaced with `null`.\n   */\n  function SuperJSON(_a) {\n    var _b = _a === void 0 ? {} : _a,\n      _c = _b.dedupe,\n      dedupe = _c === void 0 ? false : _c;\n    this.classRegistry = new ClassRegistry();\n    this.symbolRegistry = new Registry(function (s) {\n      var _a;\n      return (_a = s.description) !== null && _a !== void 0 ? _a : '';\n    });\n    this.customTransformerRegistry = new CustomTransformerRegistry();\n    this.allowedErrorProps = [];\n    this.dedupe = dedupe;\n  }\n  SuperJSON.prototype.serialize = function (object) {\n    var identities = new Map();\n    var output = walker(object, identities, this, this.dedupe);\n    var res = {\n      json: output.transformedValue\n    };\n    if (output.annotations) {\n      res.meta = __assign(__assign({}, res.meta), {\n        values: output.annotations\n      });\n    }\n    var equalityAnnotations = generateReferentialEqualityAnnotations(identities, this.dedupe);\n    if (equalityAnnotations) {\n      res.meta = __assign(__assign({}, res.meta), {\n        referentialEqualities: equalityAnnotations\n      });\n    }\n    return res;\n  };\n  SuperJSON.prototype.deserialize = function (payload) {\n    var json = payload.json,\n      meta = payload.meta;\n    var result = copy(json);\n    if (meta === null || meta === void 0 ? void 0 : meta.values) {\n      result = applyValueAnnotations(result, meta.values, this);\n    }\n    if (meta === null || meta === void 0 ? void 0 : meta.referentialEqualities) {\n      result = applyReferentialEqualityAnnotations(result, meta.referentialEqualities);\n    }\n    return result;\n  };\n  SuperJSON.prototype.stringify = function (object) {\n    return JSON.stringify(this.serialize(object));\n  };\n  SuperJSON.prototype.parse = function (string) {\n    return this.deserialize(JSON.parse(string));\n  };\n  SuperJSON.prototype.registerClass = function (v, options) {\n    this.classRegistry.register(v, options);\n  };\n  SuperJSON.prototype.registerSymbol = function (v, identifier) {\n    this.symbolRegistry.register(v, identifier);\n  };\n  SuperJSON.prototype.registerCustom = function (transformer, name) {\n    this.customTransformerRegistry.register(__assign({\n      name: name\n    }, transformer));\n  };\n  SuperJSON.prototype.allowErrorProps = function () {\n    var _a;\n    var props = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      props[_i] = arguments[_i];\n    }\n    (_a = this.allowedErrorProps).push.apply(_a, __spreadArray([], __read(props)));\n  };\n  SuperJSON.defaultInstance = new SuperJSON();\n  SuperJSON.serialize = SuperJSON.defaultInstance.serialize.bind(SuperJSON.defaultInstance);\n  SuperJSON.deserialize = SuperJSON.defaultInstance.deserialize.bind(SuperJSON.defaultInstance);\n  SuperJSON.stringify = SuperJSON.defaultInstance.stringify.bind(SuperJSON.defaultInstance);\n  SuperJSON.parse = SuperJSON.defaultInstance.parse.bind(SuperJSON.defaultInstance);\n  SuperJSON.registerClass = SuperJSON.defaultInstance.registerClass.bind(SuperJSON.defaultInstance);\n  SuperJSON.registerSymbol = SuperJSON.defaultInstance.registerSymbol.bind(SuperJSON.defaultInstance);\n  SuperJSON.registerCustom = SuperJSON.defaultInstance.registerCustom.bind(SuperJSON.defaultInstance);\n  SuperJSON.allowErrorProps = SuperJSON.defaultInstance.allowErrorProps.bind(SuperJSON.defaultInstance);\n  return SuperJSON;\n}();\nexport default SuperJSON;\nexport { SuperJSON };\nexport var serialize = SuperJSON.serialize;\nexport var deserialize = SuperJSON.deserialize;\nexport var stringify = SuperJSON.stringify;\nexport var parse = SuperJSON.parse;\nexport var registerClass = SuperJSON.registerClass;\nexport var registerCustom = SuperJSON.registerCustom;\nexport var registerSymbol = SuperJSON.registerSymbol;\nexport var allowErrorProps = SuperJSON.allowErrorProps;", "map": {"version": 3, "names": ["ClassRegistry", "Registry", "CustomTransformerRegistry", "applyReferentialEqualityAnnotations", "applyValueAnnotations", "generateReferentialEqualityAnnotations", "walker", "copy", "SuperJSON", "_a", "_b", "_c", "dedupe", "classRegistry", "symbolRegistry", "s", "description", "customTransformerRegistry", "allowedErrorProps", "prototype", "serialize", "object", "identities", "Map", "output", "res", "json", "transformedValue", "annotations", "meta", "__assign", "values", "equalityAnnotations", "referentialEqualities", "deserialize", "payload", "result", "stringify", "JSON", "parse", "string", "registerClass", "v", "options", "register", "registerSymbol", "identifier", "registerCustom", "transformer", "name", "allowErrorProps", "props", "_i", "arguments", "length", "push", "apply", "__spread<PERSON><PERSON>y", "__read", "defaultInstance", "bind"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/superjson/src/index.ts"], "sourcesContent": ["import { Class, JSONValue, SuperJSONResult, SuperJSONValue } from './types';\nimport { ClassRegistry, RegisterOptions } from './class-registry';\nimport { Registry } from './registry';\nimport {\n  CustomTransfomer,\n  CustomTransformerRegistry,\n} from './custom-transformer-registry';\nimport {\n  applyReferentialEqualityAnnotations,\n  applyValueAnnotations,\n  generateReferentialEqualityAnnotations,\n  walker,\n} from './plainer';\nimport { copy } from 'copy-anything';\n\nexport default class SuperJSON {\n  /**\n   * If true, SuperJSON will make sure only one instance of referentially equal objects are serialized and the rest are replaced with `null`.\n   */\n  private readonly dedupe: boolean;\n\n  /**\n   * @param dedupeReferentialEqualities  If true, SuperJSON will make sure only one instance of referentially equal objects are serialized and the rest are replaced with `null`.\n   */\n  constructor({\n    dedupe = false,\n  }: {\n    dedupe?: boolean;\n  } = {}) {\n    this.dedupe = dedupe;\n  }\n\n  serialize(object: SuperJSONValue): SuperJSONResult {\n    const identities = new Map<any, any[][]>();\n    const output = walker(object, identities, this, this.dedupe);\n    const res: SuperJSONResult = {\n      json: output.transformedValue,\n    };\n\n    if (output.annotations) {\n      res.meta = {\n        ...res.meta,\n        values: output.annotations,\n      };\n    }\n\n    const equalityAnnotations = generateReferentialEqualityAnnotations(\n      identities,\n      this.dedupe\n    );\n    if (equalityAnnotations) {\n      res.meta = {\n        ...res.meta,\n        referentialEqualities: equalityAnnotations,\n      };\n    }\n\n    return res;\n  }\n\n  deserialize<T = unknown>(payload: SuperJSONResult): T {\n    const { json, meta } = payload;\n\n    let result: T = copy(json) as any;\n\n    if (meta?.values) {\n      result = applyValueAnnotations(result, meta.values, this);\n    }\n\n    if (meta?.referentialEqualities) {\n      result = applyReferentialEqualityAnnotations(\n        result,\n        meta.referentialEqualities\n      );\n    }\n\n    return result;\n  }\n\n  stringify(object: SuperJSONValue): string {\n    return JSON.stringify(this.serialize(object));\n  }\n\n  parse<T = unknown>(string: string): T {\n    return this.deserialize(JSON.parse(string));\n  }\n\n  readonly classRegistry = new ClassRegistry();\n  registerClass(v: Class, options?: RegisterOptions | string) {\n    this.classRegistry.register(v, options);\n  }\n\n  readonly symbolRegistry = new Registry<Symbol>(s => s.description ?? '');\n  registerSymbol(v: Symbol, identifier?: string) {\n    this.symbolRegistry.register(v, identifier);\n  }\n\n  readonly customTransformerRegistry = new CustomTransformerRegistry();\n  registerCustom<I, O extends JSONValue>(\n    transformer: Omit<CustomTransfomer<I, O>, 'name'>,\n    name: string\n  ) {\n    this.customTransformerRegistry.register({\n      name,\n      ...transformer,\n    });\n  }\n\n  readonly allowedErrorProps: string[] = [];\n  allowErrorProps(...props: string[]) {\n    this.allowedErrorProps.push(...props);\n  }\n\n  private static defaultInstance = new SuperJSON();\n  static serialize = SuperJSON.defaultInstance.serialize.bind(\n    SuperJSON.defaultInstance\n  );\n  static deserialize = SuperJSON.defaultInstance.deserialize.bind(\n    SuperJSON.defaultInstance\n  );\n  static stringify = SuperJSON.defaultInstance.stringify.bind(\n    SuperJSON.defaultInstance\n  );\n  static parse = SuperJSON.defaultInstance.parse.bind(\n    SuperJSON.defaultInstance\n  );\n  static registerClass = SuperJSON.defaultInstance.registerClass.bind(\n    SuperJSON.defaultInstance\n  );\n  static registerSymbol = SuperJSON.defaultInstance.registerSymbol.bind(\n    SuperJSON.defaultInstance\n  );\n  static registerCustom = SuperJSON.defaultInstance.registerCustom.bind(\n    SuperJSON.defaultInstance\n  );\n  static allowErrorProps = SuperJSON.defaultInstance.allowErrorProps.bind(\n    SuperJSON.defaultInstance\n  );\n}\n\nexport { SuperJSON };\n\nexport const serialize = SuperJSON.serialize;\nexport const deserialize = SuperJSON.deserialize;\n\nexport const stringify = SuperJSON.stringify;\nexport const parse = SuperJSON.parse;\n\nexport const registerClass = SuperJSON.registerClass;\nexport const registerCustom = SuperJSON.registerCustom;\nexport const registerSymbol = SuperJSON.registerSymbol;\nexport const allowErrorProps = SuperJSON.allowErrorProps;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAASA,aAAa,QAAyB,kBAAkB;AACjE,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAEEC,yBAAyB,QACpB,+BAA+B;AACtC,SACEC,mCAAmC,EACnCC,qBAAqB,EACrBC,sCAAsC,EACtCC,MAAM,QACD,WAAW;AAClB,SAASC,IAAI,QAAQ,eAAe;AAEpC,IAAAC,SAAA;EAME;;;EAGA,SAAAA,UAAYC,EAIN;QAJMC,EAAA,GAAAD,EAAA,cAIR,EAAE,GAAAA,EAAA;MAHJE,EAAA,GAAAD,EAAA,CAAAE,MAAc;MAAdA,MAAM,GAAAD,EAAA,cAAG,KAAK,GAAAA,EAAA;IA8DP,KAAAE,aAAa,GAAG,IAAIb,aAAa,EAAE;IAKnC,KAAAc,cAAc,GAAG,IAAIb,QAAQ,CAAS,UAAAc,CAAC;MAAA,IAAAN,EAAA;MAAI,QAAAA,EAAA,GAAAM,CAAC,CAACC,WAAW,cAAAP,EAAA,cAAAA,EAAA,GAAI,EAAE;IAAA,EAAC;IAK/D,KAAAQ,yBAAyB,GAAG,IAAIf,yBAAyB,EAAE;IAW3D,KAAAgB,iBAAiB,GAAa,EAAE;IA/EvC,IAAI,CAACN,MAAM,GAAGA,MAAM;EACtB;EAEAJ,SAAA,CAAAW,SAAA,CAAAC,SAAS,GAAT,UAAUC,MAAsB;IAC9B,IAAMC,UAAU,GAAG,IAAIC,GAAG,EAAgB;IAC1C,IAAMC,MAAM,GAAGlB,MAAM,CAACe,MAAM,EAAEC,UAAU,EAAE,IAAI,EAAE,IAAI,CAACV,MAAM,CAAC;IAC5D,IAAMa,GAAG,GAAoB;MAC3BC,IAAI,EAAEF,MAAM,CAACG;KACd;IAED,IAAIH,MAAM,CAACI,WAAW,EAAE;MACtBH,GAAG,CAACI,IAAI,GAAAC,QAAA,CAAAA,QAAA,KACHL,GAAG,CAACI,IAAI;QACXE,MAAM,EAAEP,MAAM,CAACI;MAAW,EAC3B;;IAGH,IAAMI,mBAAmB,GAAG3B,sCAAsC,CAChEiB,UAAU,EACV,IAAI,CAACV,MAAM,CACZ;IACD,IAAIoB,mBAAmB,EAAE;MACvBP,GAAG,CAACI,IAAI,GAAAC,QAAA,CAAAA,QAAA,KACHL,GAAG,CAACI,IAAI;QACXI,qBAAqB,EAAED;MAAmB,EAC3C;;IAGH,OAAOP,GAAG;EACZ,CAAC;EAEDjB,SAAA,CAAAW,SAAA,CAAAe,WAAW,GAAX,UAAyBC,OAAwB;IACvC,IAAAT,IAAI,GAAWS,OAAO,CAAAT,IAAlB;MAAEG,IAAI,GAAKM,OAAO,CAAAN,IAAZ;IAElB,IAAIO,MAAM,GAAM7B,IAAI,CAACmB,IAAI,CAAQ;IAEjC,IAAIG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,MAAM,EAAE;MAChBK,MAAM,GAAGhC,qBAAqB,CAACgC,MAAM,EAAEP,IAAI,CAACE,MAAM,EAAE,IAAI,CAAC;;IAG3D,IAAIF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,qBAAqB,EAAE;MAC/BG,MAAM,GAAGjC,mCAAmC,CAC1CiC,MAAM,EACNP,IAAI,CAACI,qBAAqB,CAC3B;;IAGH,OAAOG,MAAM;EACf,CAAC;EAED5B,SAAA,CAAAW,SAAA,CAAAkB,SAAS,GAAT,UAAUhB,MAAsB;IAC9B,OAAOiB,IAAI,CAACD,SAAS,CAAC,IAAI,CAACjB,SAAS,CAACC,MAAM,CAAC,CAAC;EAC/C,CAAC;EAEDb,SAAA,CAAAW,SAAA,CAAAoB,KAAK,GAAL,UAAmBC,MAAc;IAC/B,OAAO,IAAI,CAACN,WAAW,CAACI,IAAI,CAACC,KAAK,CAACC,MAAM,CAAC,CAAC;EAC7C,CAAC;EAGDhC,SAAA,CAAAW,SAAA,CAAAsB,aAAa,GAAb,UAAcC,CAAQ,EAAEC,OAAkC;IACxD,IAAI,CAAC9B,aAAa,CAAC+B,QAAQ,CAACF,CAAC,EAAEC,OAAO,CAAC;EACzC,CAAC;EAGDnC,SAAA,CAAAW,SAAA,CAAA0B,cAAc,GAAd,UAAeH,CAAS,EAAEI,UAAmB;IAC3C,IAAI,CAAChC,cAAc,CAAC8B,QAAQ,CAACF,CAAC,EAAEI,UAAU,CAAC;EAC7C,CAAC;EAGDtC,SAAA,CAAAW,SAAA,CAAA4B,cAAc,GAAd,UACEC,WAAiD,EACjDC,IAAY;IAEZ,IAAI,CAAChC,yBAAyB,CAAC2B,QAAQ,CAAAd,QAAA;MACrCmB,IAAI,EAAAA;IAAA,GACDD,WAAW,EACd;EACJ,CAAC;EAGDxC,SAAA,CAAAW,SAAA,CAAA+B,eAAe,GAAf;;IAAgB,IAAAC,KAAA;SAAA,IAAAC,EAAA,IAAkB,EAAlBA,EAAA,GAAAC,SAAA,CAAAC,MAAkB,EAAlBF,EAAA,EAAkB;MAAlBD,KAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IACd,CAAA3C,EAAA,OAAI,CAACS,iBAAiB,EAACqC,IAAI,CAAAC,KAAA,CAAA/C,EAAA,EAAAgD,aAAA,KAAAC,MAAA,CAAIP,KAAK;EACtC,CAAC;EAEc3C,SAAA,CAAAmD,eAAe,GAAG,IAAInD,SAAS,EAAE;EACzCA,SAAA,CAAAY,SAAS,GAAGZ,SAAS,CAACmD,eAAe,CAACvC,SAAS,CAACwC,IAAI,CACzDpD,SAAS,CAACmD,eAAe,CAC1B;EACMnD,SAAA,CAAA0B,WAAW,GAAG1B,SAAS,CAACmD,eAAe,CAACzB,WAAW,CAAC0B,IAAI,CAC7DpD,SAAS,CAACmD,eAAe,CAC1B;EACMnD,SAAA,CAAA6B,SAAS,GAAG7B,SAAS,CAACmD,eAAe,CAACtB,SAAS,CAACuB,IAAI,CACzDpD,SAAS,CAACmD,eAAe,CAC1B;EACMnD,SAAA,CAAA+B,KAAK,GAAG/B,SAAS,CAACmD,eAAe,CAACpB,KAAK,CAACqB,IAAI,CACjDpD,SAAS,CAACmD,eAAe,CAC1B;EACMnD,SAAA,CAAAiC,aAAa,GAAGjC,SAAS,CAACmD,eAAe,CAAClB,aAAa,CAACmB,IAAI,CACjEpD,SAAS,CAACmD,eAAe,CAC1B;EACMnD,SAAA,CAAAqC,cAAc,GAAGrC,SAAS,CAACmD,eAAe,CAACd,cAAc,CAACe,IAAI,CACnEpD,SAAS,CAACmD,eAAe,CAC1B;EACMnD,SAAA,CAAAuC,cAAc,GAAGvC,SAAS,CAACmD,eAAe,CAACZ,cAAc,CAACa,IAAI,CACnEpD,SAAS,CAACmD,eAAe,CAC1B;EACMnD,SAAA,CAAA0C,eAAe,GAAG1C,SAAS,CAACmD,eAAe,CAACT,eAAe,CAACU,IAAI,CACrEpD,SAAS,CAACmD,eAAe,CAC1B;EACH,OAAAnD,SAAC;CAAA,CA3HD;eAAqBA,SAAS;AA6H9B,SAASA,SAAS;AAElB,OAAO,IAAMY,SAAS,GAAGZ,SAAS,CAACY,SAAS;AAC5C,OAAO,IAAMc,WAAW,GAAG1B,SAAS,CAAC0B,WAAW;AAEhD,OAAO,IAAMG,SAAS,GAAG7B,SAAS,CAAC6B,SAAS;AAC5C,OAAO,IAAME,KAAK,GAAG/B,SAAS,CAAC+B,KAAK;AAEpC,OAAO,IAAME,aAAa,GAAGjC,SAAS,CAACiC,aAAa;AACpD,OAAO,IAAMM,cAAc,GAAGvC,SAAS,CAACuC,cAAc;AACtD,OAAO,IAAMF,cAAc,GAAGrC,SAAS,CAACqC,cAAc;AACtD,OAAO,IAAMK,eAAe,GAAG1C,SAAS,CAAC0C,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}