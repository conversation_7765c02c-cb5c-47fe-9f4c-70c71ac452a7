{"ast": null, "code": "import getMapData from './_getMapData.js';\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  var result = getMapData(this, key)['delete'](key);\n  this.size -= result ? 1 : 0;\n  return result;\n}\nexport default mapCacheDelete;", "map": {"version": 3, "names": ["getMapData", "mapCacheDelete", "key", "result", "size"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/lodash-es/_mapCacheDelete.js"], "sourcesContent": ["import getMapData from './_getMapData.js';\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  var result = getMapData(this, key)['delete'](key);\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\nexport default mapCacheDelete;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,GAAG,EAAE;EAC3B,IAAIC,MAAM,GAAGH,UAAU,CAAC,IAAI,EAAEE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAACA,GAAG,CAAC;EACjD,IAAI,CAACE,IAAI,IAAID,MAAM,GAAG,CAAC,GAAG,CAAC;EAC3B,OAAOA,MAAM;AACf;AAEA,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}