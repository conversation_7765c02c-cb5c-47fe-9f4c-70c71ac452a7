{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * This function mimics the behavior of the `defaultProps` static property in React.\n * Functional components do not have a defaultProps property, so this function is useful to resolve default props.\n *\n * The common recommendation is to use ES6 destructuring with default values in the function signature,\n * but you need to be careful there and make sure you destructure all the individual properties\n * and not the whole object. See the test file for example.\n *\n * And because destructuring all properties one by one is a faff, and it's easy to miss one property,\n * this function exists.\n *\n * @param realProps - the props object passed to the component by the user\n * @param defaultProps - the default props object defined in the component by Recharts\n * @returns - the props object with all the default props resolved. All `undefined` values are replaced with the default value.\n */\nexport function resolveDefaultProps(realProps, defaultProps) {\n  /*\n   * To avoid mutating the original `realProps` object passed to the function, create a shallow copy of it.\n   * `resolvedProps` will be modified directly with the defaults.\n   */\n  var resolvedProps = _objectSpread({}, realProps);\n  /*\n   * Since the function guarantees `D extends Partial<T>`, this assignment is safe.\n   * It allows TypeScript to work with the well-defined `Partial<T>` type inside the loop,\n   * making subsequent type inference (especially for `dp[key]`) much more straightforward for the compiler.\n   * This is a key step to improve type safety *without* value assertions later.\n   */\n  var dp = defaultProps;\n  /*\n   * `Object.keys` doesn't preserve strong key types - it always returns Array<string>.\n   * However, due to the `D extends Partial<T>` constraint,\n   * we know these keys *must* also be valid keys of `T`.\n   * This assertion informs TypeScript of this relationship, avoiding type errors when using `key` to index `acc` (type T).\n   *\n   * Type assertions are not sound but in this case it's necessary\n   * as `Object.keys` does not do what we want it to do.\n   */\n  var keys = Object.keys(defaultProps);\n  var withDefaults = keys.reduce((acc, key) => {\n    if (acc[key] === undefined && dp[key] !== undefined) {\n      acc[key] = dp[key];\n    }\n    return acc;\n  }, resolvedProps);\n  /*\n   * And again type assertions are not safe but here we have done the runtime work\n   * so let's bypass the lack of static type safety and tell the compiler what happened.\n   */\n  return withDefaults;\n}\n\n/**\n * Helper type to extract the keys of T that are required.\n * It iterates through each key K in T. If Pick<T, K> cannot be assigned an empty object {},\n * it means K is required, so we keep K; otherwise, we discard it (never).\n * [keyof T] at the end creates a union of the kept keys.\n */\n\n/**\n * Helper type to extract the keys of T that are optional.\n * It iterates through each key K in T. If Pick<T, K> can be assigned an empty object {},\n * it means K is optional (or potentially missing), so we keep K; otherwise, we discard it (never).\n * [keyof T] at the end creates a union of the kept keys.\n */\n\n/**\n * Helper type to ensure keys of D exist in T.\n * For each key K in D, if K is also a key of T, keep the type D[K].\n * If K is NOT a key of T, map it to type `never`.\n * An object cannot have a property of type `never`, effectively disallowing extra keys.\n */\n\n/**\n * This type will take a source type `Props` and a default type `Defaults` and will return a new type\n * where all properties that are optional in `Props` but required in `Defaults` are made required in the result.\n * Properties that are required in `Props` and optional in `Defaults` will remain required.\n * Properties that are optional in both `Props` and `Defaults` will remain optional.\n *\n * This is useful for creating a type that represents the resolved props of a component with default props.\n */", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "resolveDefaultProps", "realProps", "defaultProps", "resolvedProps", "dp", "with<PERSON><PERSON><PERSON><PERSON>", "reduce", "acc", "key", "undefined"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/recharts/es6/util/resolveDefaultProps.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * This function mimics the behavior of the `defaultProps` static property in React.\n * Functional components do not have a defaultProps property, so this function is useful to resolve default props.\n *\n * The common recommendation is to use ES6 destructuring with default values in the function signature,\n * but you need to be careful there and make sure you destructure all the individual properties\n * and not the whole object. See the test file for example.\n *\n * And because destructuring all properties one by one is a faff, and it's easy to miss one property,\n * this function exists.\n *\n * @param realProps - the props object passed to the component by the user\n * @param defaultProps - the default props object defined in the component by Recharts\n * @returns - the props object with all the default props resolved. All `undefined` values are replaced with the default value.\n */\nexport function resolveDefaultProps(realProps, defaultProps) {\n  /*\n   * To avoid mutating the original `realProps` object passed to the function, create a shallow copy of it.\n   * `resolvedProps` will be modified directly with the defaults.\n   */\n  var resolvedProps = _objectSpread({}, realProps);\n  /*\n   * Since the function guarantees `D extends Partial<T>`, this assignment is safe.\n   * It allows TypeScript to work with the well-defined `Partial<T>` type inside the loop,\n   * making subsequent type inference (especially for `dp[key]`) much more straightforward for the compiler.\n   * This is a key step to improve type safety *without* value assertions later.\n   */\n  var dp = defaultProps;\n  /*\n   * `Object.keys` doesn't preserve strong key types - it always returns Array<string>.\n   * However, due to the `D extends Partial<T>` constraint,\n   * we know these keys *must* also be valid keys of `T`.\n   * This assertion informs TypeScript of this relationship, avoiding type errors when using `key` to index `acc` (type T).\n   *\n   * Type assertions are not sound but in this case it's necessary\n   * as `Object.keys` does not do what we want it to do.\n   */\n  var keys = Object.keys(defaultProps);\n  var withDefaults = keys.reduce((acc, key) => {\n    if (acc[key] === undefined && dp[key] !== undefined) {\n      acc[key] = dp[key];\n    }\n    return acc;\n  }, resolvedProps);\n  /*\n   * And again type assertions are not safe but here we have done the runtime work\n   * so let's bypass the lack of static type safety and tell the compiler what happened.\n   */\n  return withDefaults;\n}\n\n/**\n * Helper type to extract the keys of T that are required.\n * It iterates through each key K in T. If Pick<T, K> cannot be assigned an empty object {},\n * it means K is required, so we keep K; otherwise, we discard it (never).\n * [keyof T] at the end creates a union of the kept keys.\n */\n\n/**\n * Helper type to extract the keys of T that are optional.\n * It iterates through each key K in T. If Pick<T, K> can be assigned an empty object {},\n * it means K is optional (or potentially missing), so we keep K; otherwise, we discard it (never).\n * [keyof T] at the end creates a union of the kept keys.\n */\n\n/**\n * Helper type to ensure keys of D exist in T.\n * For each key K in D, if K is also a key of T, keep the type D[K].\n * If K is NOT a key of T, map it to type `never`.\n * An object cannot have a property of type `never`, effectively disallowing extra keys.\n */\n\n/**\n * This type will take a source type `Props` and a default type `Defaults` and will return a new type\n * where all properties that are optional in `Props` but required in `Defaults` are made required in the result.\n * Properties that are required in `Props` and optional in `Defaults` will remain required.\n * Properties that are optional in both `Props` and `Defaults` will remain optional.\n *\n * This is useful for creating a type that represents the resolved props of a component with default props.\n */"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS8B,mBAAmBA,CAACC,SAAS,EAAEC,YAAY,EAAE;EAC3D;AACF;AACA;AACA;EACE,IAAIC,aAAa,GAAGvB,aAAa,CAAC,CAAC,CAAC,EAAEqB,SAAS,CAAC;EAChD;AACF;AACA;AACA;AACA;AACA;EACE,IAAIG,EAAE,GAAGF,YAAY;EACrB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI9B,IAAI,GAAGD,MAAM,CAACC,IAAI,CAAC8B,YAAY,CAAC;EACpC,IAAIG,YAAY,GAAGjC,IAAI,CAACkC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAC3C,IAAID,GAAG,CAACC,GAAG,CAAC,KAAKC,SAAS,IAAIL,EAAE,CAACI,GAAG,CAAC,KAAKC,SAAS,EAAE;MACnDF,GAAG,CAACC,GAAG,CAAC,GAAGJ,EAAE,CAACI,GAAG,CAAC;IACpB;IACA,OAAOD,GAAG;EACZ,CAAC,EAAEJ,aAAa,CAAC;EACjB;AACF;AACA;AACA;EACE,OAAOE,YAAY;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}