{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport descending from \"./descending.js\";\nexport default function bisector(f) {\n  let compare1, compare2, delta;\n\n  // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n  if (f.length !== 2) {\n    compare1 = ascending;\n    compare2 = (d, x) => ascending(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === ascending || f === descending ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n  function left(a, x) {\n    let lo = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    let hi = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : a.length;\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = lo + hi >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n  function right(a, x) {\n    let lo = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    let hi = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : a.length;\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = lo + hi >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n  function center(a, x) {\n    let lo = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    let hi = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : a.length;\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n  return {\n    left,\n    center,\n    right\n  };\n}\nfunction zero() {\n  return 0;\n}", "map": {"version": 3, "names": ["ascending", "descending", "bisector", "f", "compare1", "compare2", "delta", "length", "d", "x", "zero", "left", "a", "lo", "arguments", "undefined", "hi", "mid", "right", "center", "i"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-array/src/bisector.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport descending from \"./descending.js\";\n\nexport default function bisector(f) {\n  let compare1, compare2, delta;\n\n  // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n  if (f.length !== 2) {\n    compare1 = ascending;\n    compare2 = (d, x) => ascending(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === ascending || f === descending ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction zero() {\n  return 0;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,UAAU,MAAM,iBAAiB;AAExC,eAAe,SAASC,QAAQA,CAACC,CAAC,EAAE;EAClC,IAAIC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK;;EAE7B;EACA;EACA;EACA;EACA;EACA,IAAIH,CAAC,CAACI,MAAM,KAAK,CAAC,EAAE;IAClBH,QAAQ,GAAGJ,SAAS;IACpBK,QAAQ,GAAGA,CAACG,CAAC,EAAEC,CAAC,KAAKT,SAAS,CAACG,CAAC,CAACK,CAAC,CAAC,EAAEC,CAAC,CAAC;IACvCH,KAAK,GAAGA,CAACE,CAAC,EAAEC,CAAC,KAAKN,CAAC,CAACK,CAAC,CAAC,GAAGC,CAAC;EAC5B,CAAC,MAAM;IACLL,QAAQ,GAAGD,CAAC,KAAKH,SAAS,IAAIG,CAAC,KAAKF,UAAU,GAAGE,CAAC,GAAGO,IAAI;IACzDL,QAAQ,GAAGF,CAAC;IACZG,KAAK,GAAGH,CAAC;EACX;EAEA,SAASQ,IAAIA,CAACC,CAAC,EAAEH,CAAC,EAAyB;IAAA,IAAvBI,EAAE,GAAAC,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;IAAA,IAAEE,EAAE,GAAAF,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGF,CAAC,CAACL,MAAM;IACvC,IAAIM,EAAE,GAAGG,EAAE,EAAE;MACX,IAAIZ,QAAQ,CAACK,CAAC,EAAEA,CAAC,CAAC,KAAK,CAAC,EAAE,OAAOO,EAAE;MACnC,GAAG;QACD,MAAMC,GAAG,GAAIJ,EAAE,GAAGG,EAAE,KAAM,CAAC;QAC3B,IAAIX,QAAQ,CAACO,CAAC,CAACK,GAAG,CAAC,EAAER,CAAC,CAAC,GAAG,CAAC,EAAEI,EAAE,GAAGI,GAAG,GAAG,CAAC,CAAC,KACrCD,EAAE,GAAGC,GAAG;MACf,CAAC,QAAQJ,EAAE,GAAGG,EAAE;IAClB;IACA,OAAOH,EAAE;EACX;EAEA,SAASK,KAAKA,CAACN,CAAC,EAAEH,CAAC,EAAyB;IAAA,IAAvBI,EAAE,GAAAC,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;IAAA,IAAEE,EAAE,GAAAF,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGF,CAAC,CAACL,MAAM;IACxC,IAAIM,EAAE,GAAGG,EAAE,EAAE;MACX,IAAIZ,QAAQ,CAACK,CAAC,EAAEA,CAAC,CAAC,KAAK,CAAC,EAAE,OAAOO,EAAE;MACnC,GAAG;QACD,MAAMC,GAAG,GAAIJ,EAAE,GAAGG,EAAE,KAAM,CAAC;QAC3B,IAAIX,QAAQ,CAACO,CAAC,CAACK,GAAG,CAAC,EAAER,CAAC,CAAC,IAAI,CAAC,EAAEI,EAAE,GAAGI,GAAG,GAAG,CAAC,CAAC,KACtCD,EAAE,GAAGC,GAAG;MACf,CAAC,QAAQJ,EAAE,GAAGG,EAAE;IAClB;IACA,OAAOH,EAAE;EACX;EAEA,SAASM,MAAMA,CAACP,CAAC,EAAEH,CAAC,EAAyB;IAAA,IAAvBI,EAAE,GAAAC,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;IAAA,IAAEE,EAAE,GAAAF,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGF,CAAC,CAACL,MAAM;IACzC,MAAMa,CAAC,GAAGT,IAAI,CAACC,CAAC,EAAEH,CAAC,EAAEI,EAAE,EAAEG,EAAE,GAAG,CAAC,CAAC;IAChC,OAAOI,CAAC,GAAGP,EAAE,IAAIP,KAAK,CAACM,CAAC,CAACQ,CAAC,GAAG,CAAC,CAAC,EAAEX,CAAC,CAAC,GAAG,CAACH,KAAK,CAACM,CAAC,CAACQ,CAAC,CAAC,EAAEX,CAAC,CAAC,GAAGW,CAAC,GAAG,CAAC,GAAGA,CAAC;EACnE;EAEA,OAAO;IAACT,IAAI;IAAEQ,MAAM;IAAED;EAAK,CAAC;AAC9B;AAEA,SAASR,IAAIA,CAAA,EAAG;EACd,OAAO,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}