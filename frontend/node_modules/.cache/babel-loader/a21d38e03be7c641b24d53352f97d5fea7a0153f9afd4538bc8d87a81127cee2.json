{"ast": null, "code": "/**\n * @license React\n * use-sync-external-store-shim/with-selector.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\nvar React = require(\"react\"),\n  shim = require(\"use-sync-external-store/shim\");\nfunction is(x, y) {\n  return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;\n}\nvar objectIs = \"function\" === typeof Object.is ? Object.is : is,\n  useSyncExternalStore = shim.useSyncExternalStore,\n  useRef = React.useRef,\n  useEffect = React.useEffect,\n  useMemo = React.useMemo,\n  useDebugValue = React.useDebugValue;\nexports.useSyncExternalStoreWithSelector = function (subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n  var instRef = useRef(null);\n  if (null === instRef.current) {\n    var inst = {\n      hasValue: !1,\n      value: null\n    };\n    instRef.current = inst;\n  } else inst = instRef.current;\n  instRef = useMemo(function () {\n    function memoizedSelector(nextSnapshot) {\n      if (!hasMemo) {\n        hasMemo = !0;\n        memoizedSnapshot = nextSnapshot;\n        nextSnapshot = selector(nextSnapshot);\n        if (void 0 !== isEqual && inst.hasValue) {\n          var currentSelection = inst.value;\n          if (isEqual(currentSelection, nextSnapshot)) return memoizedSelection = currentSelection;\n        }\n        return memoizedSelection = nextSnapshot;\n      }\n      currentSelection = memoizedSelection;\n      if (objectIs(memoizedSnapshot, nextSnapshot)) return currentSelection;\n      var nextSelection = selector(nextSnapshot);\n      if (void 0 !== isEqual && isEqual(currentSelection, nextSelection)) return memoizedSnapshot = nextSnapshot, currentSelection;\n      memoizedSnapshot = nextSnapshot;\n      return memoizedSelection = nextSelection;\n    }\n    var hasMemo = !1,\n      memoizedSnapshot,\n      memoizedSelection,\n      maybeGetServerSnapshot = void 0 === getServerSnapshot ? null : getServerSnapshot;\n    return [function () {\n      return memoizedSelector(getSnapshot());\n    }, null === maybeGetServerSnapshot ? void 0 : function () {\n      return memoizedSelector(maybeGetServerSnapshot());\n    }];\n  }, [getSnapshot, getServerSnapshot, selector, isEqual]);\n  var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n  useEffect(function () {\n    inst.hasValue = !0;\n    inst.value = value;\n  }, [value]);\n  useDebugValue(value);\n  return value;\n};", "map": {"version": 3, "names": ["React", "require", "shim", "is", "x", "y", "objectIs", "Object", "useSyncExternalStore", "useRef", "useEffect", "useMemo", "useDebugValue", "exports", "useSyncExternalStoreWithSelector", "subscribe", "getSnapshot", "getServerSnapshot", "selector", "isEqual", "instRef", "current", "inst", "hasValue", "value", "memoizedSelector", "nextSnapshot", "hasMemo", "memoizedSnapshot", "currentSelection", "memoizedSelection", "nextSelection", "maybeGetServerSnapshot"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim/with-selector.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar React = require(\"react\"),\n  shim = require(\"use-sync-external-store/shim\");\nfunction is(x, y) {\n  return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n}\nvar objectIs = \"function\" === typeof Object.is ? Object.is : is,\n  useSyncExternalStore = shim.useSyncExternalStore,\n  useRef = React.useRef,\n  useEffect = React.useEffect,\n  useMemo = React.useMemo,\n  useDebugValue = React.useDebugValue;\nexports.useSyncExternalStoreWithSelector = function (\n  subscribe,\n  getSnapshot,\n  getServerSnapshot,\n  selector,\n  isEqual\n) {\n  var instRef = useRef(null);\n  if (null === instRef.current) {\n    var inst = { hasValue: !1, value: null };\n    instRef.current = inst;\n  } else inst = instRef.current;\n  instRef = useMemo(\n    function () {\n      function memoizedSelector(nextSnapshot) {\n        if (!hasMemo) {\n          hasMemo = !0;\n          memoizedSnapshot = nextSnapshot;\n          nextSnapshot = selector(nextSnapshot);\n          if (void 0 !== isEqual && inst.hasValue) {\n            var currentSelection = inst.value;\n            if (isEqual(currentSelection, nextSnapshot))\n              return (memoizedSelection = currentSelection);\n          }\n          return (memoizedSelection = nextSnapshot);\n        }\n        currentSelection = memoizedSelection;\n        if (objectIs(memoizedSnapshot, nextSnapshot)) return currentSelection;\n        var nextSelection = selector(nextSnapshot);\n        if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n          return (memoizedSnapshot = nextSnapshot), currentSelection;\n        memoizedSnapshot = nextSnapshot;\n        return (memoizedSelection = nextSelection);\n      }\n      var hasMemo = !1,\n        memoizedSnapshot,\n        memoizedSelection,\n        maybeGetServerSnapshot =\n          void 0 === getServerSnapshot ? null : getServerSnapshot;\n      return [\n        function () {\n          return memoizedSelector(getSnapshot());\n        },\n        null === maybeGetServerSnapshot\n          ? void 0\n          : function () {\n              return memoizedSelector(maybeGetServerSnapshot());\n            }\n      ];\n    },\n    [getSnapshot, getServerSnapshot, selector, isEqual]\n  );\n  var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n  useEffect(\n    function () {\n      inst.hasValue = !0;\n      inst.value = value;\n    },\n    [value]\n  );\n  useDebugValue(value);\n  return value;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AACZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;EAC1BC,IAAI,GAAGD,OAAO,CAAC,8BAA8B,CAAC;AAChD,SAASE,EAAEA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAChB,OAAQD,CAAC,KAAKC,CAAC,KAAK,CAAC,KAAKD,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGC,CAAC,CAAC,IAAMD,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAE;AAC1E;AACA,IAAIC,QAAQ,GAAG,UAAU,KAAK,OAAOC,MAAM,CAACJ,EAAE,GAAGI,MAAM,CAACJ,EAAE,GAAGA,EAAE;EAC7DK,oBAAoB,GAAGN,IAAI,CAACM,oBAAoB;EAChDC,MAAM,GAAGT,<PERSON>AAK,<PERSON><PERSON><PERSON>,<PERSON>AM;EACrBC,SAAS,GAAGV,KAAK,CAACU,SAAS;EAC3BC,OAAO,GAAGX,KAAK,CAACW,OAAO;EACvBC,aAAa,GAAGZ,KAAK,CAACY,aAAa;AACrCC,OAAO,CAACC,gCAAgC,GAAG,UACzCC,SAAS,EACTC,WAAW,EACXC,iBAAiB,EACjBC,QAAQ,EACRC,OAAO,EACP;EACA,IAAIC,OAAO,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC1B,IAAI,IAAI,KAAKW,OAAO,CAACC,OAAO,EAAE;IAC5B,IAAIC,IAAI,GAAG;MAAEC,QAAQ,EAAE,CAAC,CAAC;MAAEC,KAAK,EAAE;IAAK,CAAC;IACxCJ,OAAO,CAACC,OAAO,GAAGC,IAAI;EACxB,CAAC,MAAMA,IAAI,GAAGF,OAAO,CAACC,OAAO;EAC7BD,OAAO,GAAGT,OAAO,CACf,YAAY;IACV,SAASc,gBAAgBA,CAACC,YAAY,EAAE;MACtC,IAAI,CAACC,OAAO,EAAE;QACZA,OAAO,GAAG,CAAC,CAAC;QACZC,gBAAgB,GAAGF,YAAY;QAC/BA,YAAY,GAAGR,QAAQ,CAACQ,YAAY,CAAC;QACrC,IAAI,KAAK,CAAC,KAAKP,OAAO,IAAIG,IAAI,CAACC,QAAQ,EAAE;UACvC,IAAIM,gBAAgB,GAAGP,IAAI,CAACE,KAAK;UACjC,IAAIL,OAAO,CAACU,gBAAgB,EAAEH,YAAY,CAAC,EACzC,OAAQI,iBAAiB,GAAGD,gBAAgB;QAChD;QACA,OAAQC,iBAAiB,GAAGJ,YAAY;MAC1C;MACAG,gBAAgB,GAAGC,iBAAiB;MACpC,IAAIxB,QAAQ,CAACsB,gBAAgB,EAAEF,YAAY,CAAC,EAAE,OAAOG,gBAAgB;MACrE,IAAIE,aAAa,GAAGb,QAAQ,CAACQ,YAAY,CAAC;MAC1C,IAAI,KAAK,CAAC,KAAKP,OAAO,IAAIA,OAAO,CAACU,gBAAgB,EAAEE,aAAa,CAAC,EAChE,OAAQH,gBAAgB,GAAGF,YAAY,EAAGG,gBAAgB;MAC5DD,gBAAgB,GAAGF,YAAY;MAC/B,OAAQI,iBAAiB,GAAGC,aAAa;IAC3C;IACA,IAAIJ,OAAO,GAAG,CAAC,CAAC;MACdC,gBAAgB;MAChBE,iBAAiB;MACjBE,sBAAsB,GACpB,KAAK,CAAC,KAAKf,iBAAiB,GAAG,IAAI,GAAGA,iBAAiB;IAC3D,OAAO,CACL,YAAY;MACV,OAAOQ,gBAAgB,CAACT,WAAW,CAAC,CAAC,CAAC;IACxC,CAAC,EACD,IAAI,KAAKgB,sBAAsB,GAC3B,KAAK,CAAC,GACN,YAAY;MACV,OAAOP,gBAAgB,CAACO,sBAAsB,CAAC,CAAC,CAAC;IACnD,CAAC,CACN;EACH,CAAC,EACD,CAAChB,WAAW,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,OAAO,CACpD,CAAC;EACD,IAAIK,KAAK,GAAGhB,oBAAoB,CAACO,SAAS,EAAEK,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;EACnEV,SAAS,CACP,YAAY;IACVY,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClBD,IAAI,CAACE,KAAK,GAAGA,KAAK;EACpB,CAAC,EACD,CAACA,KAAK,CACR,CAAC;EACDZ,aAAa,CAACY,KAAK,CAAC;EACpB,OAAOA,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}