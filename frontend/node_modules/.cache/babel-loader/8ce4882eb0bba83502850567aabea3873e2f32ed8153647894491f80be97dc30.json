{"ast": null, "code": "class Subscribable {\n  constructor() {\n    this.listeners = new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    const identity = {\n      listener\n    };\n    this.listeners.add(identity);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(identity);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {// Do nothing\n  }\n  onUnsubscribe() {// Do nothing\n  }\n}\nexport { Subscribable };", "map": {"version": 3, "names": ["Subscribable", "constructor", "listeners", "Set", "subscribe", "bind", "listener", "identity", "add", "onSubscribe", "delete", "onUnsubscribe", "hasListeners", "size"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@tanstack/query-core/src/subscribable.ts"], "sourcesContent": ["type Listener = () => void\n\nexport class Subscribable<TListener extends Function = Listener> {\n  protected listeners: Set<{ listener: TListener }>\n\n  constructor() {\n    this.listeners = new Set()\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    const identity = { listener }\n    this.listeners.add(identity)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(identity)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n"], "mappings": "AAEO,MAAMA,YAAN,CAA0D;EAG/DC,WAAWA,CAAA,EAAG;IACZ,KAAKC,SAAL,GAAiB,IAAIC,GAAJ,EAAjB;IACA,IAAK,CAAAC,SAAL,GAAiB,IAAK,CAAAA,SAAL,CAAeC,IAAf,CAAoB,IAApB,CAAjB;EACD;EAEDD,SAASA,CAACE,QAAD,EAAkC;IACzC,MAAMC,QAAQ,GAAG;MAAED;KAAnB;IACA,KAAKJ,SAAL,CAAeM,GAAf,CAAmBD,QAAnB;IAEA,KAAKE,WAAL;IAEA,OAAO,MAAM;MACX,KAAKP,SAAL,CAAeQ,MAAf,CAAsBH,QAAtB;MACA,KAAKI,aAAL;KAFF;EAID;EAEDC,YAAYA,CAAA,EAAY;IACtB,OAAO,IAAK,CAAAV,SAAL,CAAeW,IAAf,GAAsB,CAA7B;EACD;EAESJ,WAAWA,CAAA,EAAS;EAAA;EAIpBE,aAAaA,CAAA,EAAS;EAAA;AA5B+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}