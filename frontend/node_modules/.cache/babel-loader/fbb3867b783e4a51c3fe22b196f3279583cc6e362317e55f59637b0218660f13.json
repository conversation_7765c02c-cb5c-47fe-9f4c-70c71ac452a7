{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst unset = require('./unset.js');\nconst cloneDeep = require('../../object/cloneDeep.js');\nfunction omit(obj) {\n  if (obj == null) {\n    return {};\n  }\n  const result = cloneDeep.cloneDeep(obj);\n  for (let i = 0; i < (arguments.length <= 1 ? 0 : arguments.length - 1); i++) {\n    let keys = i + 1 < 1 || arguments.length <= i + 1 ? undefined : arguments[i + 1];\n    switch (typeof keys) {\n      case 'object':\n        {\n          if (!Array.isArray(keys)) {\n            keys = Array.from(keys);\n          }\n          for (let j = 0; j < keys.length; j++) {\n            const key = keys[j];\n            unset.unset(result, key);\n          }\n          break;\n        }\n      case 'string':\n      case 'symbol':\n      case 'number':\n        {\n          unset.unset(result, keys);\n          break;\n        }\n    }\n  }\n  return result;\n}\nexports.omit = omit;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "unset", "require", "cloneDeep", "omit", "obj", "result", "i", "arguments", "length", "keys", "undefined", "Array", "isArray", "from", "j", "key"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/es-toolkit/dist/compat/object/omit.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst unset = require('./unset.js');\nconst cloneDeep = require('../../object/cloneDeep.js');\n\nfunction omit(obj, ...keysArr) {\n    if (obj == null) {\n        return {};\n    }\n    const result = cloneDeep.cloneDeep(obj);\n    for (let i = 0; i < keysArr.length; i++) {\n        let keys = keysArr[i];\n        switch (typeof keys) {\n            case 'object': {\n                if (!Array.isArray(keys)) {\n                    keys = Array.from(keys);\n                }\n                for (let j = 0; j < keys.length; j++) {\n                    const key = keys[j];\n                    unset.unset(result, key);\n                }\n                break;\n            }\n            case 'string':\n            case 'symbol':\n            case 'number': {\n                unset.unset(result, keys);\n                break;\n            }\n        }\n    }\n    return result;\n}\n\nexports.omit = omit;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AACnC,MAAMC,SAAS,GAAGD,OAAO,CAAC,2BAA2B,CAAC;AAEtD,SAASE,IAAIA,CAACC,GAAG,EAAc;EAC3B,IAAIA,GAAG,IAAI,IAAI,EAAE;IACb,OAAO,CAAC,CAAC;EACb;EACA,MAAMC,MAAM,GAAGH,SAAS,CAACA,SAAS,CAACE,GAAG,CAAC;EACvC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAAC,SAAA,CAAAC,MAAA,YAAAD,SAAA,CAAAC,MAAA,KAAiB,EAAEF,CAAC,EAAE,EAAE;IACrC,IAAIG,IAAI,GAAWH,CAAC,YAAAC,SAAA,CAAAC,MAAA,IAADF,CAAC,OAAAI,SAAA,GAAAH,SAAA,CAADD,CAAC,KAAC;IACrB,QAAQ,OAAOG,IAAI;MACf,KAAK,QAAQ;QAAE;UACX,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;YACtBA,IAAI,GAAGE,KAAK,CAACE,IAAI,CAACJ,IAAI,CAAC;UAC3B;UACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,CAACD,MAAM,EAAEM,CAAC,EAAE,EAAE;YAClC,MAAMC,GAAG,GAAGN,IAAI,CAACK,CAAC,CAAC;YACnBd,KAAK,CAACA,KAAK,CAACK,MAAM,EAAEU,GAAG,CAAC;UAC5B;UACA;QACJ;MACA,KAAK,QAAQ;MACb,KAAK,QAAQ;MACb,KAAK,QAAQ;QAAE;UACXf,KAAK,CAACA,KAAK,CAACK,MAAM,EAAEI,IAAI,CAAC;UACzB;QACJ;IACJ;EACJ;EACA,OAAOJ,MAAM;AACjB;AAEAT,OAAO,CAACO,IAAI,GAAGA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}