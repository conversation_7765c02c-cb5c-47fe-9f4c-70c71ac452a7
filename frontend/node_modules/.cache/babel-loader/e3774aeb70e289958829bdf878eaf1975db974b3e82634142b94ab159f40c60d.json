{"ast": null, "code": "export default function pairs(values) {\n  let pairof = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : pair;\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\nexport function pair(a, b) {\n  return [a, b];\n}", "map": {"version": 3, "names": ["pairs", "values", "pairof", "arguments", "length", "undefined", "pair", "previous", "first", "value", "push", "a", "b"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-array/src/pairs.js"], "sourcesContent": ["export default function pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\n\nexport function pair(a, b) {\n  return [a, b];\n}\n"], "mappings": "AAAA,eAAe,SAASA,KAAKA,CAACC,MAAM,EAAiB;EAAA,IAAfC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,IAAI;EACjD,MAAMN,KAAK,GAAG,EAAE;EAChB,IAAIO,QAAQ;EACZ,IAAIC,KAAK,GAAG,KAAK;EACjB,KAAK,MAAMC,KAAK,IAAIR,MAAM,EAAE;IAC1B,IAAIO,KAAK,EAAER,KAAK,CAACU,IAAI,CAACR,MAAM,CAACK,QAAQ,EAAEE,KAAK,CAAC,CAAC;IAC9CF,QAAQ,GAAGE,KAAK;IAChBD,KAAK,GAAG,IAAI;EACd;EACA,OAAOR,KAAK;AACd;AAEA,OAAO,SAASM,IAAIA,CAACK,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAO,CAACD,CAAC,EAAEC,CAAC,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}