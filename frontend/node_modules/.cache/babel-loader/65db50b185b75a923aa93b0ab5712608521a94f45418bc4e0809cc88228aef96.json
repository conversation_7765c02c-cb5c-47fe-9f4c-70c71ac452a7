{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst debounce = require('./debounce.js');\nfunction throttle(func) {\n  let throttleMs = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (typeof options !== 'object') {\n    options = {};\n  }\n  const {\n    leading = true,\n    trailing = true\n  } = options;\n  return debounce.debounce(func, throttleMs, {\n    leading,\n    trailing,\n    maxWait: throttleMs\n  });\n}\nexports.throttle = throttle;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "debounce", "require", "throttle", "func", "throttleMs", "arguments", "length", "undefined", "options", "leading", "trailing", "max<PERSON><PERSON>"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/es-toolkit/dist/compat/function/throttle.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst debounce = require('./debounce.js');\n\nfunction throttle(func, throttleMs = 0, options = {}) {\n    if (typeof options !== 'object') {\n        options = {};\n    }\n    const { leading = true, trailing = true } = options;\n    return debounce.debounce(func, throttleMs, {\n        leading,\n        trailing,\n        maxWait: throttleMs,\n    });\n}\n\nexports.throttle = throttle;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,QAAQ,GAAGC,OAAO,CAAC,eAAe,CAAC;AAEzC,SAASC,QAAQA,CAACC,IAAI,EAAgC;EAAA,IAA9BC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAChD,IAAI,OAAOG,OAAO,KAAK,QAAQ,EAAE;IAC7BA,OAAO,GAAG,CAAC,CAAC;EAChB;EACA,MAAM;IAAEC,OAAO,GAAG,IAAI;IAAEC,QAAQ,GAAG;EAAK,CAAC,GAAGF,OAAO;EACnD,OAAOR,QAAQ,CAACA,QAAQ,CAACG,IAAI,EAAEC,UAAU,EAAE;IACvCK,OAAO;IACPC,QAAQ;IACRC,OAAO,EAAEP;EACb,CAAC,CAAC;AACN;AAEAR,OAAO,CAACM,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}