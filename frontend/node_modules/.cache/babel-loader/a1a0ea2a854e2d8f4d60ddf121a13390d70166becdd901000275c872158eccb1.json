{"ast": null, "code": "import { InternSet } from \"internmap\";\nexport default function union() {\n  const set = new InternSet();\n  for (var _len = arguments.length, others = new Array(_len), _key = 0; _key < _len; _key++) {\n    others[_key] = arguments[_key];\n  }\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}", "map": {"version": 3, "names": ["InternSet", "union", "set", "_len", "arguments", "length", "others", "Array", "_key", "other", "o", "add"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-array/src/union.js"], "sourcesContent": ["import {InternSet} from \"internmap\";\n\nexport default function union(...others) {\n  const set = new InternSet();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,WAAW;AAEnC,eAAe,SAASC,KAAKA,CAAA,EAAY;EACvC,MAAMC,GAAG,GAAG,IAAIF,SAAS,CAAC,CAAC;EAAC,SAAAG,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADGC,MAAM,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAANF,MAAM,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAErC,KAAK,MAAMC,KAAK,IAAIH,MAAM,EAAE;IAC1B,KAAK,MAAMI,CAAC,IAAID,KAAK,EAAE;MACrBP,GAAG,CAACS,GAAG,CAACD,CAAC,CAAC;IACZ;EACF;EACA,OAAOR,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}