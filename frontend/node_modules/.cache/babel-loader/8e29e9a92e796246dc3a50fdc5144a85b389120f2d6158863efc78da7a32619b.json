{"ast": null, "code": "export var combineTooltipPayloadConfigurations = (tooltipState, tooltipEventType, trigger, defaultIndex) => {\n  // if tooltip reacts to axis interaction, then we display all items at the same time.\n  if (tooltipEventType === 'axis') {\n    return tooltipState.tooltipItemPayloads;\n  }\n  /*\n   * By now we already know that tooltipEventType is 'item', so we can only search in itemInteractions.\n   * item means that only the hovered or clicked item will be present in the tooltip.\n   */\n  if (tooltipState.tooltipItemPayloads.length === 0) {\n    // No point filtering if the payload is empty\n    return [];\n  }\n  var filterByDataKey;\n  if (trigger === 'hover') {\n    filterByDataKey = tooltipState.itemInteraction.hover.dataKey;\n  } else {\n    filterByDataKey = tooltipState.itemInteraction.click.dataKey;\n  }\n  if (filterByDataKey == null && defaultIndex != null) {\n    /*\n     * So when we use `defaultIndex` - we don't have a dataKey to filter by because user did not hover over anything yet.\n     * In that case let's display the first item in the tooltip; after all, this is `item` interaction case,\n     * so we should display only one item at a time instead of all.\n     */\n    return [tooltipState.tooltipItemPayloads[0]];\n  }\n  return tooltipState.tooltipItemPayloads.filter(tpc => {\n    var _tpc$settings;\n    return ((_tpc$settings = tpc.settings) === null || _tpc$settings === void 0 ? void 0 : _tpc$settings.dataKey) === filterByDataKey;\n  });\n};", "map": {"version": 3, "names": ["combineTooltipPayloadConfigurations", "tooltipState", "tooltipEventType", "trigger", "defaultIndex", "tooltipItemPayloads", "length", "filterByDataKey", "itemInteraction", "hover", "dataKey", "click", "filter", "tpc", "_tpc$settings", "settings"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/recharts/es6/state/selectors/combiners/combineTooltipPayloadConfigurations.js"], "sourcesContent": ["export var combineTooltipPayloadConfigurations = (tooltipState, tooltipEventType, trigger, defaultIndex) => {\n  // if tooltip reacts to axis interaction, then we display all items at the same time.\n  if (tooltipEventType === 'axis') {\n    return tooltipState.tooltipItemPayloads;\n  }\n  /*\n   * By now we already know that tooltipEventType is 'item', so we can only search in itemInteractions.\n   * item means that only the hovered or clicked item will be present in the tooltip.\n   */\n  if (tooltipState.tooltipItemPayloads.length === 0) {\n    // No point filtering if the payload is empty\n    return [];\n  }\n  var filterByDataKey;\n  if (trigger === 'hover') {\n    filterByDataKey = tooltipState.itemInteraction.hover.dataKey;\n  } else {\n    filterByDataKey = tooltipState.itemInteraction.click.dataKey;\n  }\n  if (filterByDataKey == null && defaultIndex != null) {\n    /*\n     * So when we use `defaultIndex` - we don't have a dataKey to filter by because user did not hover over anything yet.\n     * In that case let's display the first item in the tooltip; after all, this is `item` interaction case,\n     * so we should display only one item at a time instead of all.\n     */\n    return [tooltipState.tooltipItemPayloads[0]];\n  }\n  return tooltipState.tooltipItemPayloads.filter(tpc => {\n    var _tpc$settings;\n    return ((_tpc$settings = tpc.settings) === null || _tpc$settings === void 0 ? void 0 : _tpc$settings.dataKey) === filterByDataKey;\n  });\n};"], "mappings": "AAAA,OAAO,IAAIA,mCAAmC,GAAGA,CAACC,YAAY,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,YAAY,KAAK;EAC1G;EACA,IAAIF,gBAAgB,KAAK,MAAM,EAAE;IAC/B,OAAOD,YAAY,CAACI,mBAAmB;EACzC;EACA;AACF;AACA;AACA;EACE,IAAIJ,YAAY,CAACI,mBAAmB,CAACC,MAAM,KAAK,CAAC,EAAE;IACjD;IACA,OAAO,EAAE;EACX;EACA,IAAIC,eAAe;EACnB,IAAIJ,OAAO,KAAK,OAAO,EAAE;IACvBI,eAAe,GAAGN,YAAY,CAACO,eAAe,CAACC,KAAK,CAACC,OAAO;EAC9D,CAAC,MAAM;IACLH,eAAe,GAAGN,YAAY,CAACO,eAAe,CAACG,KAAK,CAACD,OAAO;EAC9D;EACA,IAAIH,eAAe,IAAI,IAAI,IAAIH,YAAY,IAAI,IAAI,EAAE;IACnD;AACJ;AACA;AACA;AACA;IACI,OAAO,CAACH,YAAY,CAACI,mBAAmB,CAAC,CAAC,CAAC,CAAC;EAC9C;EACA,OAAOJ,YAAY,CAACI,mBAAmB,CAACO,MAAM,CAACC,GAAG,IAAI;IACpD,IAAIC,aAAa;IACjB,OAAO,CAAC,CAACA,aAAa,GAAGD,GAAG,CAACE,QAAQ,MAAM,IAAI,IAAID,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACJ,OAAO,MAAMH,eAAe;EACnI,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}