{"ast": null, "code": "var DoubleIndexedKV = /** @class */function () {\n  function DoubleIndexedKV() {\n    this.keyToValue = new Map();\n    this.valueToKey = new Map();\n  }\n  DoubleIndexedKV.prototype.set = function (key, value) {\n    this.keyToValue.set(key, value);\n    this.valueToKey.set(value, key);\n  };\n  DoubleIndexedKV.prototype.getByKey = function (key) {\n    return this.keyToValue.get(key);\n  };\n  DoubleIndexedKV.prototype.getByValue = function (value) {\n    return this.valueToKey.get(value);\n  };\n  DoubleIndexedKV.prototype.clear = function () {\n    this.keyToValue.clear();\n    this.valueToKey.clear();\n  };\n  return DoubleIndexedKV;\n}();\nexport { DoubleIndexedKV };", "map": {"version": 3, "names": ["DoubleIndexedKV", "keyToValue", "Map", "valueToKey", "prototype", "set", "key", "value", "get<PERSON><PERSON><PERSON><PERSON>", "get", "getByValue", "clear"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/superjson/src/double-indexed-kv.ts"], "sourcesContent": ["export class DoubleIndexedKV<K, V> {\n  keyToValue = new Map<K, V>();\n  valueToKey = new Map<V, K>();\n\n  set(key: K, value: V) {\n    this.keyToValue.set(key, value);\n    this.valueToKey.set(value, key);\n  }\n\n  getByKey(key: K): V | undefined {\n    return this.keyToValue.get(key);\n  }\n\n  getByValue(value: V): K | undefined {\n    return this.valueToKey.get(value);\n  }\n\n  clear() {\n    this.keyToValue.clear();\n    this.valueToKey.clear();\n  }\n}\n"], "mappings": "AAAA,IAAAA,eAAA;EAAA,SAAAA,gBAAA;IACE,KAAAC,UAAU,GAAG,IAAIC,GAAG,EAAQ;IAC5B,KAAAC,UAAU,GAAG,IAAID,GAAG,EAAQ;EAmB9B;EAjBEF,eAAA,CAAAI,SAAA,CAAAC,GAAG,GAAH,UAAIC,GAAM,EAAEC,KAAQ;IAClB,IAAI,CAACN,UAAU,CAACI,GAAG,CAACC,GAAG,EAAEC,KAAK,CAAC;IAC/B,IAAI,CAACJ,UAAU,CAACE,GAAG,CAACE,KAAK,EAAED,GAAG,CAAC;EACjC,CAAC;EAEDN,eAAA,CAAAI,SAAA,CAAAI,QAAQ,GAAR,UAASF,GAAM;IACb,OAAO,IAAI,CAACL,UAAU,CAACQ,GAAG,CAACH,GAAG,CAAC;EACjC,CAAC;EAEDN,eAAA,CAAAI,SAAA,CAAAM,UAAU,GAAV,UAAWH,KAAQ;IACjB,OAAO,IAAI,CAACJ,UAAU,CAACM,GAAG,CAACF,KAAK,CAAC;EACnC,CAAC;EAEDP,eAAA,CAAAI,SAAA,CAAAO,KAAK,GAAL;IACE,IAAI,CAACV,UAAU,CAACU,KAAK,EAAE;IACvB,IAAI,CAACR,UAAU,CAACQ,KAAK,EAAE;EACzB,CAAC;EACH,OAAAX,eAAC;AAAD,CAAC,CArBD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}