{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nfunction valuesOfObj(record) {\n  if ('values' in Object) {\n    // eslint-disable-next-line es5/no-es6-methods\n    return Object.values(record);\n  }\n  var values = [];\n  // eslint-disable-next-line no-restricted-syntax\n  for (var key in record) {\n    if (record.hasOwnProperty(key)) {\n      values.push(record[key]);\n    }\n  }\n  return values;\n}\nexport function find(record, predicate) {\n  var values = valuesOfObj(record);\n  if ('find' in values) {\n    // eslint-disable-next-line es5/no-es6-methods\n    return values.find(predicate);\n  }\n  var valuesNotNever = values;\n  for (var i = 0; i < valuesNotNever.length; i++) {\n    var value = valuesNotNever[i];\n    if (predicate(value)) {\n      return value;\n    }\n  }\n  return undefined;\n}\nexport function forEach(record, run) {\n  Object.entries(record).forEach(function (_a) {\n    var _b = __read(_a, 2),\n      key = _b[0],\n      value = _b[1];\n    return run(value, key);\n  });\n}\nexport function includes(arr, value) {\n  return arr.indexOf(value) !== -1;\n}\nexport function findArr(record, predicate) {\n  for (var i = 0; i < record.length; i++) {\n    var value = record[i];\n    if (predicate(value)) {\n      return value;\n    }\n  }\n  return undefined;\n}", "map": {"version": 3, "names": ["valuesOfObj", "record", "Object", "values", "key", "hasOwnProperty", "push", "find", "predicate", "valuesNotNever", "i", "length", "value", "undefined", "for<PERSON>ach", "run", "entries", "_a", "_b", "__read", "includes", "arr", "indexOf", "findArr"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/superjson/src/util.ts"], "sourcesContent": ["function valuesOfObj<T>(record: Record<string, T>): T[] {\n  if ('values' in Object) {\n    // eslint-disable-next-line es5/no-es6-methods\n    return Object.values(record);\n  }\n\n  const values: T[] = [];\n\n  // eslint-disable-next-line no-restricted-syntax\n  for (const key in record) {\n    if (record.hasOwnProperty(key)) {\n      values.push(record[key]);\n    }\n  }\n\n  return values;\n}\n\nexport function find<T>(\n  record: Record<string, T>,\n  predicate: (v: T) => boolean\n): T | undefined {\n  const values = valuesOfObj(record);\n  if ('find' in values) {\n    // eslint-disable-next-line es5/no-es6-methods\n    return values.find(predicate);\n  }\n\n  const valuesNotNever = values as T[];\n\n  for (let i = 0; i < valuesNotNever.length; i++) {\n    const value = valuesNotNever[i];\n    if (predicate(value)) {\n      return value;\n    }\n  }\n\n  return undefined;\n}\n\nexport function forEach<T>(\n  record: Record<string, T>,\n  run: (v: T, key: string) => void\n) {\n  Object.entries(record).forEach(([key, value]) => run(value, key));\n}\n\nexport function includes<T>(arr: T[], value: T) {\n  return arr.indexOf(value) !== -1;\n}\n\nexport function findArr<T>(\n  record: T[],\n  predicate: (v: T) => boolean\n): T | undefined {\n  for (let i = 0; i < record.length; i++) {\n    const value = record[i];\n    if (predicate(value)) {\n      return value;\n    }\n  }\n\n  return undefined;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,WAAWA,CAAIC,MAAyB;EAC/C,IAAI,QAAQ,IAAIC,MAAM,EAAE;IACtB;IACA,OAAOA,MAAM,CAACC,MAAM,CAACF,MAAM,CAAC;;EAG9B,IAAME,MAAM,GAAQ,EAAE;EAEtB;EACA,KAAK,IAAMC,GAAG,IAAIH,MAAM,EAAE;IACxB,IAAIA,MAAM,CAACI,cAAc,CAACD,GAAG,CAAC,EAAE;MAC9BD,MAAM,CAACG,IAAI,CAACL,MAAM,CAACG,GAAG,CAAC,CAAC;;;EAI5B,OAAOD,MAAM;AACf;AAEA,OAAM,SAAUI,IAAIA,CAClBN,MAAyB,EACzBO,SAA4B;EAE5B,IAAML,MAAM,GAAGH,WAAW,CAACC,MAAM,CAAC;EAClC,IAAI,MAAM,IAAIE,MAAM,EAAE;IACpB;IACA,OAAOA,MAAM,CAACI,IAAI,CAACC,SAAS,CAAC;;EAG/B,IAAMC,cAAc,GAAGN,MAAa;EAEpC,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAME,KAAK,GAAGH,cAAc,CAACC,CAAC,CAAC;IAC/B,IAAIF,SAAS,CAACI,KAAK,CAAC,EAAE;MACpB,OAAOA,KAAK;;;EAIhB,OAAOC,SAAS;AAClB;AAEA,OAAM,SAAUC,OAAOA,CACrBb,MAAyB,EACzBc,GAAgC;EAEhCb,MAAM,CAACc,OAAO,CAACf,MAAM,CAAC,CAACa,OAAO,CAAC,UAACG,EAAY;QAAZC,EAAA,GAAAC,MAAA,CAAAF,EAAA,IAAY;MAAXb,GAAG,GAAAc,EAAA;MAAEN,KAAK,GAAAM,EAAA;IAAM,OAAAH,GAAG,CAACH,KAAK,EAAER,GAAG,CAAC;EAAf,CAAe,CAAC;AACnE;AAEA,OAAM,SAAUgB,QAAQA,CAAIC,GAAQ,EAAET,KAAQ;EAC5C,OAAOS,GAAG,CAACC,OAAO,CAACV,KAAK,CAAC,KAAK,CAAC,CAAC;AAClC;AAEA,OAAM,SAAUW,OAAOA,CACrBtB,MAAW,EACXO,SAA4B;EAE5B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,MAAM,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAME,KAAK,GAAGX,MAAM,CAACS,CAAC,CAAC;IACvB,IAAIF,SAAS,CAACI,KAAK,CAAC,EAAE;MACpB,OAAOA,KAAK;;;EAIhB,OAAOC,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}