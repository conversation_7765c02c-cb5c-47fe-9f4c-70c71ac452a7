{"ast": null, "code": "export class InternMap extends Map {\n  constructor(entries) {\n    let key = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : keyof;\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\nexport class InternSet extends Set {\n  constructor(values) {\n    let key = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : keyof;\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\nfunction intern_get(_ref, value) {\n  let {\n    _intern,\n    _key\n  } = _ref;\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\nfunction intern_set(_ref2, value) {\n  let {\n    _intern,\n    _key\n  } = _ref2;\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\nfunction intern_delete(_ref3, value) {\n  let {\n    _intern,\n    _key\n  } = _ref3;\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n    _intern.delete(key);\n  }\n  return value;\n}\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}", "map": {"version": 3, "names": ["InternMap", "Map", "constructor", "entries", "key", "arguments", "length", "undefined", "keyof", "Object", "defineProperties", "_intern", "value", "_key", "set", "get", "intern_get", "has", "intern_set", "delete", "intern_delete", "InternSet", "Set", "values", "add", "_ref", "_ref2", "_ref3", "valueOf"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/internmap/src/index.js"], "sourcesContent": ["export class InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\n\nexport class InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\n\nfunction intern_get({_intern, _key}, value) {\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\n\nfunction intern_set({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\n\nfunction intern_delete({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n    _intern.delete(key);\n  }\n  return value;\n}\n\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n"], "mappings": "AAAA,OAAO,MAAMA,SAAS,SAASC,GAAG,CAAC;EACjCC,WAAWA,CAACC,OAAO,EAAe;IAAA,IAAbC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,KAAK;IAC9B,KAAK,CAAC,CAAC;IACPC,MAAM,CAACC,gBAAgB,CAAC,IAAI,EAAE;MAACC,OAAO,EAAE;QAACC,KAAK,EAAE,IAAIX,GAAG,CAAC;MAAC,CAAC;MAAEY,IAAI,EAAE;QAACD,KAAK,EAAER;MAAG;IAAC,CAAC,CAAC;IAChF,IAAID,OAAO,IAAI,IAAI,EAAE,KAAK,MAAM,CAACC,GAAG,EAAEQ,KAAK,CAAC,IAAIT,OAAO,EAAE,IAAI,CAACW,GAAG,CAACV,GAAG,EAAEQ,KAAK,CAAC;EAC/E;EACAG,GAAGA,CAACX,GAAG,EAAE;IACP,OAAO,KAAK,CAACW,GAAG,CAACC,UAAU,CAAC,IAAI,EAAEZ,GAAG,CAAC,CAAC;EACzC;EACAa,GAAGA,CAACb,GAAG,EAAE;IACP,OAAO,KAAK,CAACa,GAAG,CAACD,UAAU,CAAC,IAAI,EAAEZ,GAAG,CAAC,CAAC;EACzC;EACAU,GAAGA,CAACV,GAAG,EAAEQ,KAAK,EAAE;IACd,OAAO,KAAK,CAACE,GAAG,CAACI,UAAU,CAAC,IAAI,EAAEd,GAAG,CAAC,EAAEQ,KAAK,CAAC;EAChD;EACAO,MAAMA,CAACf,GAAG,EAAE;IACV,OAAO,KAAK,CAACe,MAAM,CAACC,aAAa,CAAC,IAAI,EAAEhB,GAAG,CAAC,CAAC;EAC/C;AACF;AAEA,OAAO,MAAMiB,SAAS,SAASC,GAAG,CAAC;EACjCpB,WAAWA,CAACqB,MAAM,EAAe;IAAA,IAAbnB,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,KAAK;IAC7B,KAAK,CAAC,CAAC;IACPC,MAAM,CAACC,gBAAgB,CAAC,IAAI,EAAE;MAACC,OAAO,EAAE;QAACC,KAAK,EAAE,IAAIX,GAAG,CAAC;MAAC,CAAC;MAAEY,IAAI,EAAE;QAACD,KAAK,EAAER;MAAG;IAAC,CAAC,CAAC;IAChF,IAAImB,MAAM,IAAI,IAAI,EAAE,KAAK,MAAMX,KAAK,IAAIW,MAAM,EAAE,IAAI,CAACC,GAAG,CAACZ,KAAK,CAAC;EACjE;EACAK,GAAGA,CAACL,KAAK,EAAE;IACT,OAAO,KAAK,CAACK,GAAG,CAACD,UAAU,CAAC,IAAI,EAAEJ,KAAK,CAAC,CAAC;EAC3C;EACAY,GAAGA,CAACZ,KAAK,EAAE;IACT,OAAO,KAAK,CAACY,GAAG,CAACN,UAAU,CAAC,IAAI,EAAEN,KAAK,CAAC,CAAC;EAC3C;EACAO,MAAMA,CAACP,KAAK,EAAE;IACZ,OAAO,KAAK,CAACO,MAAM,CAACC,aAAa,CAAC,IAAI,EAAER,KAAK,CAAC,CAAC;EACjD;AACF;AAEA,SAASI,UAAUA,CAAAS,IAAA,EAAkBb,KAAK,EAAE;EAAA,IAAxB;IAACD,OAAO;IAAEE;EAAI,CAAC,GAAAY,IAAA;EACjC,MAAMrB,GAAG,GAAGS,IAAI,CAACD,KAAK,CAAC;EACvB,OAAOD,OAAO,CAACM,GAAG,CAACb,GAAG,CAAC,GAAGO,OAAO,CAACI,GAAG,CAACX,GAAG,CAAC,GAAGQ,KAAK;AACpD;AAEA,SAASM,UAAUA,CAAAQ,KAAA,EAAkBd,KAAK,EAAE;EAAA,IAAxB;IAACD,OAAO;IAAEE;EAAI,CAAC,GAAAa,KAAA;EACjC,MAAMtB,GAAG,GAAGS,IAAI,CAACD,KAAK,CAAC;EACvB,IAAID,OAAO,CAACM,GAAG,CAACb,GAAG,CAAC,EAAE,OAAOO,OAAO,CAACI,GAAG,CAACX,GAAG,CAAC;EAC7CO,OAAO,CAACG,GAAG,CAACV,GAAG,EAAEQ,KAAK,CAAC;EACvB,OAAOA,KAAK;AACd;AAEA,SAASQ,aAAaA,CAAAO,KAAA,EAAkBf,KAAK,EAAE;EAAA,IAAxB;IAACD,OAAO;IAAEE;EAAI,CAAC,GAAAc,KAAA;EACpC,MAAMvB,GAAG,GAAGS,IAAI,CAACD,KAAK,CAAC;EACvB,IAAID,OAAO,CAACM,GAAG,CAACb,GAAG,CAAC,EAAE;IACpBQ,KAAK,GAAGD,OAAO,CAACI,GAAG,CAACX,GAAG,CAAC;IACxBO,OAAO,CAACQ,MAAM,CAACf,GAAG,CAAC;EACrB;EACA,OAAOQ,KAAK;AACd;AAEA,SAASJ,KAAKA,CAACI,KAAK,EAAE;EACpB,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACgB,OAAO,CAAC,CAAC,GAAGhB,KAAK;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}