{"ast": null, "code": "import min from \"./min.js\";\nexport default function transpose(matrix) {\n  if (!(n = matrix.length)) return [];\n  for (var i = -1, m = min(matrix, length), transpose = new Array(m); ++i < m;) {\n    for (var j = -1, n, row = transpose[i] = new Array(n); ++j < n;) {\n      row[j] = matrix[j][i];\n    }\n  }\n  return transpose;\n}\nfunction length(d) {\n  return d.length;\n}", "map": {"version": 3, "names": ["min", "transpose", "matrix", "n", "length", "i", "m", "Array", "j", "row", "d"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-array/src/transpose.js"], "sourcesContent": ["import min from \"./min.js\";\n\nexport default function transpose(matrix) {\n  if (!(n = matrix.length)) return [];\n  for (var i = -1, m = min(matrix, length), transpose = new Array(m); ++i < m;) {\n    for (var j = -1, n, row = transpose[i] = new Array(n); ++j < n;) {\n      row[j] = matrix[j][i];\n    }\n  }\n  return transpose;\n}\n\nfunction length(d) {\n  return d.length;\n}\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAE1B,eAAe,SAASC,SAASA,CAACC,MAAM,EAAE;EACxC,IAAI,EAAEC,CAAC,GAAGD,MAAM,CAACE,MAAM,CAAC,EAAE,OAAO,EAAE;EACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAGN,GAAG,CAACE,MAAM,EAAEE,MAAM,CAAC,EAAEH,SAAS,GAAG,IAAIM,KAAK,CAACD,CAAC,CAAC,EAAE,EAAED,CAAC,GAAGC,CAAC,GAAG;IAC5E,KAAK,IAAIE,CAAC,GAAG,CAAC,CAAC,EAAEL,CAAC,EAAEM,GAAG,GAAGR,SAAS,CAACI,CAAC,CAAC,GAAG,IAAIE,KAAK,CAACJ,CAAC,CAAC,EAAE,EAAEK,CAAC,GAAGL,CAAC,GAAG;MAC/DM,GAAG,CAACD,CAAC,CAAC,GAAGN,MAAM,CAACM,CAAC,CAAC,CAACH,CAAC,CAAC;IACvB;EACF;EACA,OAAOJ,SAAS;AAClB;AAEA,SAASG,MAAMA,CAACM,CAAC,EAAE;EACjB,OAAOA,CAAC,CAACN,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}