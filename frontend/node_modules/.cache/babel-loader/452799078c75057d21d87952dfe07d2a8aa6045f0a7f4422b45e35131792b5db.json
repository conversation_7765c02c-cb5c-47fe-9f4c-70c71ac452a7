{"ast": null, "code": "import formatDecimal from \"./formatDecimal.js\";\nimport formatPrefixAuto from \"./formatPrefixAuto.js\";\nimport formatRounded from \"./formatRounded.js\";\nexport default {\n  \"%\": (x, p) => (x * 100).toFixed(p),\n  \"b\": x => Math.round(x).toString(2),\n  \"c\": x => x + \"\",\n  \"d\": formatDecimal,\n  \"e\": (x, p) => x.toExponential(p),\n  \"f\": (x, p) => x.toFixed(p),\n  \"g\": (x, p) => x.toPrecision(p),\n  \"o\": x => Math.round(x).toString(8),\n  \"p\": (x, p) => formatRounded(x * 100, p),\n  \"r\": formatRounded,\n  \"s\": formatPrefixAuto,\n  \"X\": x => Math.round(x).toString(16).toUpperCase(),\n  \"x\": x => Math.round(x).toString(16)\n};", "map": {"version": 3, "names": ["formatDecimal", "formatPrefixAuto", "formatRounded", "%", "x", "p", "toFixed", "Math", "round", "toString", "e", "toExponential", "f", "g", "toPrecision", "toUpperCase"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-format/src/formatTypes.js"], "sourcesContent": ["import formatDecimal from \"./formatDecimal.js\";\nimport formatPrefixAuto from \"./formatPrefixAuto.js\";\nimport formatRounded from \"./formatRounded.js\";\n\nexport default {\n  \"%\": (x, p) => (x * 100).toFixed(p),\n  \"b\": (x) => Math.round(x).toString(2),\n  \"c\": (x) => x + \"\",\n  \"d\": formatDecimal,\n  \"e\": (x, p) => x.toExponential(p),\n  \"f\": (x, p) => x.toFixed(p),\n  \"g\": (x, p) => x.toPrecision(p),\n  \"o\": (x) => Math.round(x).toString(8),\n  \"p\": (x, p) => formatRounded(x * 100, p),\n  \"r\": formatRounded,\n  \"s\": formatPrefixAuto,\n  \"X\": (x) => Math.round(x).toString(16).toUpperCase(),\n  \"x\": (x) => Math.round(x).toString(16)\n};\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,aAAa,MAAM,oBAAoB;AAE9C,eAAe;EACb,GAAG,EAAEC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,GAAG,GAAG,EAAEE,OAAO,CAACD,CAAC,CAAC;EACnC,GAAG,EAAGD,CAAC,IAAKG,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;EACrC,GAAG,EAAGL,CAAC,IAAKA,CAAC,GAAG,EAAE;EAClB,GAAG,EAAEJ,aAAa;EAClB,GAAG,EAAEU,CAACN,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACO,aAAa,CAACN,CAAC,CAAC;EACjC,GAAG,EAAEO,CAACR,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,OAAO,CAACD,CAAC,CAAC;EAC3B,GAAG,EAAEQ,CAACT,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACU,WAAW,CAACT,CAAC,CAAC;EAC/B,GAAG,EAAGD,CAAC,IAAKG,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;EACrC,GAAG,EAAEJ,CAACD,CAAC,EAAEC,CAAC,KAAKH,aAAa,CAACE,CAAC,GAAG,GAAG,EAAEC,CAAC,CAAC;EACxC,GAAG,EAAEH,aAAa;EAClB,GAAG,EAAED,gBAAgB;EACrB,GAAG,EAAGG,CAAC,IAAKG,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC,CAACK,QAAQ,CAAC,EAAE,CAAC,CAACM,WAAW,CAAC,CAAC;EACpD,GAAG,EAAGX,CAAC,IAAKG,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC,CAACK,QAAQ,CAAC,EAAE;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}