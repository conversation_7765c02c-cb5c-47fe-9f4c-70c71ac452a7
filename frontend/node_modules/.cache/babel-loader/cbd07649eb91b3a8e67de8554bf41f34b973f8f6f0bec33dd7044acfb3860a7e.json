{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport { Registry } from './registry';\nvar ClassRegistry = /** @class */function (_super) {\n  __extends(ClassRegistry, _super);\n  function ClassRegistry() {\n    var _this = _super.call(this, function (c) {\n      return c.name;\n    }) || this;\n    _this.classToAllowedProps = new Map();\n    return _this;\n  }\n  ClassRegistry.prototype.register = function (value, options) {\n    if (typeof options === 'object') {\n      if (options.allowProps) {\n        this.classToAllowedProps.set(value, options.allowProps);\n      }\n      _super.prototype.register.call(this, value, options.identifier);\n    } else {\n      _super.prototype.register.call(this, value, options);\n    }\n  };\n  ClassRegistry.prototype.getAllowedProps = function (value) {\n    return this.classToAllowedProps.get(value);\n  };\n  return ClassRegistry;\n}(Registry);\nexport { ClassRegistry };", "map": {"version": 3, "names": ["Registry", "ClassRegistry", "_super", "__extends", "_this", "call", "c", "name", "classToAllowedProps", "Map", "prototype", "register", "value", "options", "allowProps", "set", "identifier", "getAllowedProps", "get"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/superjson/src/class-registry.ts"], "sourcesContent": ["import { Registry } from './registry';\nimport { Class } from './types';\n\nexport interface RegisterOptions {\n  identifier?: string;\n  allowProps?: string[];\n}\n\nexport class ClassRegistry extends Registry<Class> {\n  constructor() {\n    super(c => c.name);\n  }\n\n  private classToAllowedProps = new Map<Class, string[]>();\n\n  register(value: Class, options?: string | RegisterOptions): void {\n    if (typeof options === 'object') {\n      if (options.allowProps) {\n        this.classToAllowedProps.set(value, options.allowProps);\n      }\n\n      super.register(value, options.identifier);\n    } else {\n      super.register(value, options);\n    }\n  }\n\n  getAllowedProps(value: Class): string[] | undefined {\n    return this.classToAllowedProps.get(value);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,QAAQ,YAAY;AAQrC,IAAAC,aAAA,0BAAAC,MAAA;EAAmCC,SAAA,CAAAF,aAAA,EAAAC,MAAA;EACjC,SAAAD,cAAA;IAAA,IAAAG,KAAA,GACEF,MAAA,CAAAG,IAAA,OAAM,UAAAC,CAAC;MAAI,OAAAA,CAAC,CAACC,IAAI;IAAN,CAAM,CAAC;IAGZH,KAAA,CAAAI,mBAAmB,GAAG,IAAIC,GAAG,EAAmB;;EAFxD;EAIAR,aAAA,CAAAS,SAAA,CAAAC,QAAQ,GAAR,UAASC,KAAY,EAAEC,OAAkC;IACvD,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,IAAIA,OAAO,CAACC,UAAU,EAAE;QACtB,IAAI,CAACN,mBAAmB,CAACO,GAAG,CAACH,KAAK,EAAEC,OAAO,CAACC,UAAU,CAAC;;MAGzDZ,MAAA,CAAAQ,SAAA,CAAMC,QAAQ,CAAAN,IAAA,OAACO,KAAK,EAAEC,OAAO,CAACG,UAAU,CAAC;KAC1C,MAAM;MACLd,MAAA,CAAAQ,SAAA,CAAMC,QAAQ,CAAAN,IAAA,OAACO,KAAK,EAAEC,OAAO,CAAC;;EAElC,CAAC;EAEDZ,aAAA,CAAAS,SAAA,CAAAO,eAAe,GAAf,UAAgBL,KAAY;IAC1B,OAAO,IAAI,CAACJ,mBAAmB,CAACU,GAAG,CAACN,KAAK,CAAC;EAC5C,CAAC;EACH,OAAAX,aAAC;AAAD,CAAC,CAtBkCD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}