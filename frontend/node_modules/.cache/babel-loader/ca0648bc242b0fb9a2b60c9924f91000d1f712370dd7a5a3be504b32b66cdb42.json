{"ast": null, "code": "const defaultLogger = console;\nexport { defaultLogger };", "map": {"version": 3, "names": ["defaultLogger", "console"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@tanstack/query-core/src/logger.ts"], "sourcesContent": ["export interface Logger {\n  log: LogFunction\n  warn: LogFunction\n  error: LogFunction\n}\n\ntype LogFunction = (...args: any[]) => void\n\nexport const defaultLogger: Logger = console\n"], "mappings": "AAQO,MAAMA,aAAqB,GAAGC,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}