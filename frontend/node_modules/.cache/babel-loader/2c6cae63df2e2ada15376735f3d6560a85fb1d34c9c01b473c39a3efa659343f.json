{"ast": null, "code": "import { isValidTimeout, isServer } from './utils.mjs';\nclass Removable {\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.cacheTime);\n    }\n  }\n  updateCacheTime(newCacheTime) {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(this.cacheTime || 0, newCacheTime != null ? newCacheTime : isServer ? Infinity : 5 * 60 * 1000);\n  }\n  clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  }\n}\nexport { Removable };", "map": {"version": 3, "names": ["Removable", "destroy", "clearGcTimeout", "scheduleGc", "isValidTimeout", "cacheTime", "gcTimeout", "setTimeout", "optionalRemove", "updateCacheTime", "newCacheTime", "Math", "max", "isServer", "Infinity", "clearTimeout", "undefined"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/@tanstack/query-core/src/removable.ts"], "sourcesContent": ["import { isServer, isValidTimeout } from './utils'\n\nexport abstract class Removable {\n  cacheTime!: number\n  private gcTimeout?: ReturnType<typeof setTimeout>\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.cacheTime)\n    }\n  }\n\n  protected updateCacheTime(newCacheTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(\n      this.cacheTime || 0,\n      newCacheTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout)\n      this.gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n"], "mappings": ";AAEO,MAAeA,SAAf,CAAyB;EAI9BC,OAAOA,CAAA,EAAS;IACd,KAAKC,cAAL;EACD;EAESC,UAAUA,CAAA,EAAS;IAC3B,KAAKD,cAAL;IAEA,IAAIE,cAAc,CAAC,IAAK,CAAAC,SAAN,CAAlB,EAAoC;MAClC,KAAKC,SAAL,GAAiBC,UAAU,CAAC,MAAM;QAChC,KAAKC,cAAL;OADyB,EAExB,IAAK,CAAAH,SAFmB,CAA3B;IAGD;EACF;EAESI,eAAeA,CAACC,YAAD,EAAyC;IAChE;IACA,IAAK,CAAAL,SAAL,GAAiBM,IAAI,CAACC,GAAL,CACf,KAAKP,SAAL,IAAkB,CADH,EAEfK,YAFe,IAEf,OAAAA,YAFe,GAEEG,QAAQ,GAAGC,QAAH,GAAc,CAAI,KAAJ,GAAS,IAFjC,CAAjB;EAID;EAESZ,cAAcA,CAAA,EAAG;IACzB,IAAI,KAAKI,SAAT,EAAoB;MAClBS,YAAY,CAAC,IAAK,CAAAT,SAAN,CAAZ;MACA,IAAK,CAAAA,SAAL,GAAiBU,SAAjB;IACD;EACF;AA/B6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}