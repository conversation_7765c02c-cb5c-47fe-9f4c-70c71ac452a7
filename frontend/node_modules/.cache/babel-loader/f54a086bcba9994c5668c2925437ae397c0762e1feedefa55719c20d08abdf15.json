{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction flatten(arr) {\n  let depth = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  const result = [];\n  const flooredDepth = Math.floor(depth);\n  const recursive = (arr, currentDepth) => {\n    for (let i = 0; i < arr.length; i++) {\n      const item = arr[i];\n      if (Array.isArray(item) && currentDepth < flooredDepth) {\n        recursive(item, currentDepth + 1);\n      } else {\n        result.push(item);\n      }\n    }\n  };\n  recursive(arr, 0);\n  return result;\n}\nexports.flatten = flatten;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "flatten", "arr", "depth", "arguments", "length", "undefined", "result", "flooredDepth", "Math", "floor", "recursive", "<PERSON><PERSON><PERSON><PERSON>", "i", "item", "Array", "isArray", "push"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/es-toolkit/dist/array/flatten.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction flatten(arr, depth = 1) {\n    const result = [];\n    const flooredDepth = Math.floor(depth);\n    const recursive = (arr, currentDepth) => {\n        for (let i = 0; i < arr.length; i++) {\n            const item = arr[i];\n            if (Array.isArray(item) && currentDepth < flooredDepth) {\n                recursive(item, currentDepth + 1);\n            }\n            else {\n                result.push(item);\n            }\n        }\n    };\n    recursive(arr, 0);\n    return result;\n}\n\nexports.flatten = flatten;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,OAAOA,CAACC,GAAG,EAAa;EAAA,IAAXC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAC3B,MAAMG,MAAM,GAAG,EAAE;EACjB,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACP,KAAK,CAAC;EACtC,MAAMQ,SAAS,GAAGA,CAACT,GAAG,EAAEU,YAAY,KAAK;IACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,GAAG,CAACG,MAAM,EAAEQ,CAAC,EAAE,EAAE;MACjC,MAAMC,IAAI,GAAGZ,GAAG,CAACW,CAAC,CAAC;MACnB,IAAIE,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,IAAIF,YAAY,GAAGJ,YAAY,EAAE;QACpDG,SAAS,CAACG,IAAI,EAAEF,YAAY,GAAG,CAAC,CAAC;MACrC,CAAC,MACI;QACDL,MAAM,CAACU,IAAI,CAACH,IAAI,CAAC;MACrB;IACJ;EACJ,CAAC;EACDH,SAAS,CAACT,GAAG,EAAE,CAAC,CAAC;EACjB,OAAOK,MAAM;AACjB;AAEAV,OAAO,CAACI,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}