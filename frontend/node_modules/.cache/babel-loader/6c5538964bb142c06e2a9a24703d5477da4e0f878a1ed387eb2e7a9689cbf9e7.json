{"ast": null, "code": "import { useRef } from 'react';\nimport { uniqueId } from './DataUtils';\n\n/**\n * This hook returns a unique animation id for the object input.\n * If input changes (as in, reference equality is different), the animation id will change.\n * If input does not change, the animation id will not change.\n *\n * This is useful for animations. The Animate component\n * does have a `shouldReAnimate` prop but that doesn't seem to be doing what the name implies.\n * Also, we don't always want to re-animate on every render;\n * we only want to re-animate when the input changes. Not the internal state (e.g. `isAnimating`).\n *\n * @param input The object to check for changes. Uses reference equality (=== operator)\n * @param prefix Optional prefix to use for the animation id\n * @returns A unique animation id\n */\nexport function useAnimationId(input) {\n  var prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'animation-';\n  var animationId = useRef(uniqueId(prefix));\n  var prevProps = useRef(input);\n  if (prevProps.current !== input) {\n    animationId.current = uniqueId(prefix);\n    prevProps.current = input;\n  }\n  return animationId.current;\n}", "map": {"version": 3, "names": ["useRef", "uniqueId", "useAnimationId", "input", "prefix", "arguments", "length", "undefined", "animationId", "prevProps", "current"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/recharts/es6/util/useAnimationId.js"], "sourcesContent": ["import { useRef } from 'react';\nimport { uniqueId } from './DataUtils';\n\n/**\n * This hook returns a unique animation id for the object input.\n * If input changes (as in, reference equality is different), the animation id will change.\n * If input does not change, the animation id will not change.\n *\n * This is useful for animations. The Animate component\n * does have a `shouldReAnimate` prop but that doesn't seem to be doing what the name implies.\n * Also, we don't always want to re-animate on every render;\n * we only want to re-animate when the input changes. Not the internal state (e.g. `isAnimating`).\n *\n * @param input The object to check for changes. Uses reference equality (=== operator)\n * @param prefix Optional prefix to use for the animation id\n * @returns A unique animation id\n */\nexport function useAnimationId(input) {\n  var prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'animation-';\n  var animationId = useRef(uniqueId(prefix));\n  var prevProps = useRef(input);\n  if (prevProps.current !== input) {\n    animationId.current = uniqueId(prefix);\n    prevProps.current = input;\n  }\n  return animationId.current;\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,QAAQ,QAAQ,aAAa;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,YAAY;EAC7F,IAAIG,WAAW,GAAGR,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC;EAC1C,IAAIK,SAAS,GAAGT,MAAM,CAACG,KAAK,CAAC;EAC7B,IAAIM,SAAS,CAACC,OAAO,KAAKP,KAAK,EAAE;IAC/BK,WAAW,CAACE,OAAO,GAAGT,QAAQ,CAACG,MAAM,CAAC;IACtCK,SAAS,CAACC,OAAO,GAAGP,KAAK;EAC3B;EACA,OAAOK,WAAW,CAACE,OAAO;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}