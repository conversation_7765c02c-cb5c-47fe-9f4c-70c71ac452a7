{"ast": null, "code": "/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\nexport default freeGlobal;", "map": {"version": 3, "names": ["freeGlobal", "global", "Object"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/lodash-es/_freeGlobal.js"], "sourcesContent": ["/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\nexport default freeGlobal;\n"], "mappings": "AAAA;AACA,IAAIA,UAAU,GAAG,OAAOC,MAAM,IAAI,QAAQ,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAKA,MAAM,IAAID,MAAM;AAE1F,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}