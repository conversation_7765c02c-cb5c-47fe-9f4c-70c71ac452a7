{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport permute from \"./permute.js\";\nexport default function sort(values) {\n  for (var _len = arguments.length, F = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    F[_key - 1] = arguments[_key];\n  }\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f] = F;\n  if (f && f.length !== 2 || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascendingDefined(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascendingDefined(f[i], f[j]));\n    }\n    return permute(values, index);\n  }\n  return values.sort(compareDefined(f));\n}\nexport function compareDefined() {\n  let compare = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ascending;\n  if (compare === ascending) return ascendingDefined;\n  if (typeof compare !== \"function\") throw new TypeError(\"compare is not a function\");\n  return (a, b) => {\n    const x = compare(a, b);\n    if (x || x === 0) return x;\n    return (compare(b, b) === 0) - (compare(a, a) === 0);\n  };\n}\nexport function ascendingDefined(a, b) {\n  return (a == null || !(a >= a)) - (b == null || !(b >= b)) || (a < b ? -1 : a > b ? 1 : 0);\n}", "map": {"version": 3, "names": ["ascending", "permute", "sort", "values", "_len", "arguments", "length", "F", "Array", "_key", "Symbol", "iterator", "TypeError", "from", "f", "index", "Uint32Array", "d", "i", "map", "j", "c", "ascendingDefined", "compareDefined", "compare", "undefined", "a", "b", "x"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-array/src/sort.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport permute from \"./permute.js\";\n\nexport default function sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f] = F;\n  if ((f && f.length !== 2) || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascendingDefined(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascendingDefined(f[i], f[j]));\n    }\n    return permute(values, index);\n  }\n  return values.sort(compareDefined(f));\n}\n\nexport function compareDefined(compare = ascending) {\n  if (compare === ascending) return ascendingDefined;\n  if (typeof compare !== \"function\") throw new TypeError(\"compare is not a function\");\n  return (a, b) => {\n    const x = compare(a, b);\n    if (x || x === 0) return x;\n    return (compare(b, b) === 0) - (compare(a, a) === 0);\n  };\n}\n\nexport function ascendingDefined(a, b) {\n  return (a == null || !(a >= a)) - (b == null || !(b >= b)) || (a < b ? -1 : a > b ? 1 : 0);\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,OAAO,MAAM,cAAc;AAElC,eAAe,SAASC,IAAIA,CAACC,MAAM,EAAQ;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAHC,CAAC,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAADF,CAAC,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EACvC,IAAI,OAAON,MAAM,CAACO,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAChGT,MAAM,GAAGK,KAAK,CAACK,IAAI,CAACV,MAAM,CAAC;EAC3B,IAAI,CAACW,CAAC,CAAC,GAAGP,CAAC;EACX,IAAKO,CAAC,IAAIA,CAAC,CAACR,MAAM,KAAK,CAAC,IAAKC,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE;IACzC,MAAMS,KAAK,GAAGC,WAAW,CAACH,IAAI,CAACV,MAAM,EAAE,CAACc,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;IACnD,IAAIX,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE;MAChBC,CAAC,GAAGA,CAAC,CAACY,GAAG,CAACL,CAAC,IAAIX,MAAM,CAACgB,GAAG,CAACL,CAAC,CAAC,CAAC;MAC7BC,KAAK,CAACb,IAAI,CAAC,CAACgB,CAAC,EAAEE,CAAC,KAAK;QACnB,KAAK,MAAMN,CAAC,IAAIP,CAAC,EAAE;UACjB,MAAMc,CAAC,GAAGC,gBAAgB,CAACR,CAAC,CAACI,CAAC,CAAC,EAAEJ,CAAC,CAACM,CAAC,CAAC,CAAC;UACtC,IAAIC,CAAC,EAAE,OAAOA,CAAC;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLP,CAAC,GAAGX,MAAM,CAACgB,GAAG,CAACL,CAAC,CAAC;MACjBC,KAAK,CAACb,IAAI,CAAC,CAACgB,CAAC,EAAEE,CAAC,KAAKE,gBAAgB,CAACR,CAAC,CAACI,CAAC,CAAC,EAAEJ,CAAC,CAACM,CAAC,CAAC,CAAC,CAAC;IACpD;IACA,OAAOnB,OAAO,CAACE,MAAM,EAAEY,KAAK,CAAC;EAC/B;EACA,OAAOZ,MAAM,CAACD,IAAI,CAACqB,cAAc,CAACT,CAAC,CAAC,CAAC;AACvC;AAEA,OAAO,SAASS,cAAcA,CAAA,EAAsB;EAAA,IAArBC,OAAO,GAAAnB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoB,SAAA,GAAApB,SAAA,MAAGL,SAAS;EAChD,IAAIwB,OAAO,KAAKxB,SAAS,EAAE,OAAOsB,gBAAgB;EAClD,IAAI,OAAOE,OAAO,KAAK,UAAU,EAAE,MAAM,IAAIZ,SAAS,CAAC,2BAA2B,CAAC;EACnF,OAAO,CAACc,CAAC,EAAEC,CAAC,KAAK;IACf,MAAMC,CAAC,GAAGJ,OAAO,CAACE,CAAC,EAAEC,CAAC,CAAC;IACvB,IAAIC,CAAC,IAAIA,CAAC,KAAK,CAAC,EAAE,OAAOA,CAAC;IAC1B,OAAO,CAACJ,OAAO,CAACG,CAAC,EAAEA,CAAC,CAAC,KAAK,CAAC,KAAKH,OAAO,CAACE,CAAC,EAAEA,CAAC,CAAC,KAAK,CAAC,CAAC;EACtD,CAAC;AACH;AAEA,OAAO,SAASJ,gBAAgBA,CAACI,CAAC,EAAEC,CAAC,EAAE;EACrC,OAAO,CAACD,CAAC,IAAI,IAAI,IAAI,EAAEA,CAAC,IAAIA,CAAC,CAAC,KAAKC,CAAC,IAAI,IAAI,IAAI,EAAEA,CAAC,IAAIA,CAAC,CAAC,CAAC,KAAKD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}