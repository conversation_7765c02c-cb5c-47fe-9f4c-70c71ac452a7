{"ast": null, "code": "import { slice } from \"./array.js\";\nimport constant from \"./constant.js\";\nimport { bumpX, bumpY, bumpRadial } from \"./curve/bump.js\";\nimport { withPath } from \"./path.js\";\nimport { x as pointX, y as pointY } from \"./point.js\";\nfunction linkSource(d) {\n  return d.source;\n}\nfunction linkTarget(d) {\n  return d.target;\n}\nexport function link(curve) {\n  let source = linkSource,\n    target = linkTarget,\n    x = pointX,\n    y = pointY,\n    context = null,\n    output = null,\n    path = withPath(link);\n  function link() {\n    let buffer;\n    const argv = slice.call(arguments);\n    const s = source.apply(this, argv);\n    const t = target.apply(this, argv);\n    if (context == null) output = curve(buffer = path());\n    output.lineStart();\n    argv[0] = s, output.point(+x.apply(this, argv), +y.apply(this, argv));\n    argv[0] = t, output.point(+x.apply(this, argv), +y.apply(this, argv));\n    output.lineEnd();\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n  link.source = function (_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n  link.target = function (_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n  link.x = function (_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n  link.y = function (_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n  link.context = function (_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), link) : context;\n  };\n  return link;\n}\nexport function linkHorizontal() {\n  return link(bumpX);\n}\nexport function linkVertical() {\n  return link(bumpY);\n}\nexport function linkRadial() {\n  const l = link(bumpRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}", "map": {"version": 3, "names": ["slice", "constant", "bumpX", "bumpY", "bumpRadial", "with<PERSON><PERSON>", "x", "pointX", "y", "pointY", "linkSource", "d", "source", "linkTarget", "target", "link", "curve", "context", "output", "path", "buffer", "argv", "call", "arguments", "s", "apply", "t", "lineStart", "point", "lineEnd", "_", "length", "linkHorizontal", "linkVertical", "linkRadial", "l", "angle", "radius"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-shape/src/link.js"], "sourcesContent": ["import {slice} from \"./array.js\";\nimport constant from \"./constant.js\";\nimport {bumpX, bumpY, bumpRadial} from \"./curve/bump.js\";\nimport {withPath} from \"./path.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nexport function link(curve) {\n  let source = linkSource,\n      target = linkTarget,\n      x = pointX,\n      y = pointY,\n      context = null,\n      output = null,\n      path = withPath(link);\n\n  function link() {\n    let buffer;\n    const argv = slice.call(arguments);\n    const s = source.apply(this, argv);\n    const t = target.apply(this, argv);\n    if (context == null) output = curve(buffer = path());\n    output.lineStart();\n    argv[0] = s, output.point(+x.apply(this, argv), +y.apply(this, argv));\n    argv[0] = t, output.point(+x.apply(this, argv), +y.apply(this, argv));\n    output.lineEnd();\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), link) : context;\n  };\n\n  return link;\n}\n\nexport function linkHorizontal() {\n  return link(bumpX);\n}\n\nexport function linkVertical() {\n  return link(bumpY);\n}\n\nexport function linkRadial() {\n  const l = link(bumpRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,YAAY;AAChC,OAAOC,QAAQ,MAAM,eAAe;AACpC,SAAQC,KAAK,EAAEC,KAAK,EAAEC,UAAU,QAAO,iBAAiB;AACxD,SAAQC,QAAQ,QAAO,WAAW;AAClC,SAAQC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,MAAM,QAAO,YAAY;AAEnD,SAASC,UAAUA,CAACC,CAAC,EAAE;EACrB,OAAOA,CAAC,CAACC,MAAM;AACjB;AAEA,SAASC,UAAUA,CAACF,CAAC,EAAE;EACrB,OAAOA,CAAC,CAACG,MAAM;AACjB;AAEA,OAAO,SAASC,IAAIA,CAACC,KAAK,EAAE;EAC1B,IAAIJ,MAAM,GAAGF,UAAU;IACnBI,MAAM,GAAGD,UAAU;IACnBP,CAAC,GAAGC,MAAM;IACVC,CAAC,GAAGC,MAAM;IACVQ,OAAO,GAAG,IAAI;IACdC,MAAM,GAAG,IAAI;IACbC,IAAI,GAAGd,QAAQ,CAACU,IAAI,CAAC;EAEzB,SAASA,IAAIA,CAAA,EAAG;IACd,IAAIK,MAAM;IACV,MAAMC,IAAI,GAAGrB,KAAK,CAACsB,IAAI,CAACC,SAAS,CAAC;IAClC,MAAMC,CAAC,GAAGZ,MAAM,CAACa,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;IAClC,MAAMK,CAAC,GAAGZ,MAAM,CAACW,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;IAClC,IAAIJ,OAAO,IAAI,IAAI,EAAEC,MAAM,GAAGF,KAAK,CAACI,MAAM,GAAGD,IAAI,CAAC,CAAC,CAAC;IACpDD,MAAM,CAACS,SAAS,CAAC,CAAC;IAClBN,IAAI,CAAC,CAAC,CAAC,GAAGG,CAAC,EAAEN,MAAM,CAACU,KAAK,CAAC,CAACtB,CAAC,CAACmB,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC,EAAE,CAACb,CAAC,CAACiB,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC,CAAC;IACrEA,IAAI,CAAC,CAAC,CAAC,GAAGK,CAAC,EAAER,MAAM,CAACU,KAAK,CAAC,CAACtB,CAAC,CAACmB,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC,EAAE,CAACb,CAAC,CAACiB,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC,CAAC;IACrEH,MAAM,CAACW,OAAO,CAAC,CAAC;IAChB,IAAIT,MAAM,EAAE,OAAOF,MAAM,GAAG,IAAI,EAAEE,MAAM,GAAG,EAAE,IAAI,IAAI;EACvD;EAEAL,IAAI,CAACH,MAAM,GAAG,UAASkB,CAAC,EAAE;IACxB,OAAOP,SAAS,CAACQ,MAAM,IAAInB,MAAM,GAAGkB,CAAC,EAAEf,IAAI,IAAIH,MAAM;EACvD,CAAC;EAEDG,IAAI,CAACD,MAAM,GAAG,UAASgB,CAAC,EAAE;IACxB,OAAOP,SAAS,CAACQ,MAAM,IAAIjB,MAAM,GAAGgB,CAAC,EAAEf,IAAI,IAAID,MAAM;EACvD,CAAC;EAEDC,IAAI,CAACT,CAAC,GAAG,UAASwB,CAAC,EAAE;IACnB,OAAOP,SAAS,CAACQ,MAAM,IAAIzB,CAAC,GAAG,OAAOwB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG7B,QAAQ,CAAC,CAAC6B,CAAC,CAAC,EAAEf,IAAI,IAAIT,CAAC;EACtF,CAAC;EAEDS,IAAI,CAACP,CAAC,GAAG,UAASsB,CAAC,EAAE;IACnB,OAAOP,SAAS,CAACQ,MAAM,IAAIvB,CAAC,GAAG,OAAOsB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG7B,QAAQ,CAAC,CAAC6B,CAAC,CAAC,EAAEf,IAAI,IAAIP,CAAC;EACtF,CAAC;EAEDO,IAAI,CAACE,OAAO,GAAG,UAASa,CAAC,EAAE;IACzB,OAAOP,SAAS,CAACQ,MAAM,IAAID,CAAC,IAAI,IAAI,GAAGb,OAAO,GAAGC,MAAM,GAAG,IAAI,GAAGA,MAAM,GAAGF,KAAK,CAACC,OAAO,GAAGa,CAAC,CAAC,EAAEf,IAAI,IAAIE,OAAO;EAC/G,CAAC;EAED,OAAOF,IAAI;AACb;AAEA,OAAO,SAASiB,cAAcA,CAAA,EAAG;EAC/B,OAAOjB,IAAI,CAACb,KAAK,CAAC;AACpB;AAEA,OAAO,SAAS+B,YAAYA,CAAA,EAAG;EAC7B,OAAOlB,IAAI,CAACZ,KAAK,CAAC;AACpB;AAEA,OAAO,SAAS+B,UAAUA,CAAA,EAAG;EAC3B,MAAMC,CAAC,GAAGpB,IAAI,CAACX,UAAU,CAAC;EAC1B+B,CAAC,CAACC,KAAK,GAAGD,CAAC,CAAC7B,CAAC,EAAE,OAAO6B,CAAC,CAAC7B,CAAC;EACzB6B,CAAC,CAACE,MAAM,GAAGF,CAAC,CAAC3B,CAAC,EAAE,OAAO2B,CAAC,CAAC3B,CAAC;EAC1B,OAAO2B,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}