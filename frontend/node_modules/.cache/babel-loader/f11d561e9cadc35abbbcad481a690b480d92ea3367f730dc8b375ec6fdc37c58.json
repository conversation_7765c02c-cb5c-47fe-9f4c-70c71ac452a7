{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from) {\n  for (var i = 0, il = from.length, j = to.length; i < il; i++, j++) to[j] = from[i];\n  return to;\n};\nimport { isArray, isEmptyObject, isMap, isPlainObject, isPrimitive, isSet } from './is';\nimport { escapeKey, stringifyPath } from './pathstringifier';\nimport { isInstanceOfRegisteredClass, transformValue, untransformValue } from './transformer';\nimport { includes, forEach } from './util';\nimport { parsePath } from './pathstringifier';\nimport { getDeep, setDeep } from './accessDeep';\nfunction traverse(tree, walker, origin) {\n  if (origin === void 0) {\n    origin = [];\n  }\n  if (!tree) {\n    return;\n  }\n  if (!isArray(tree)) {\n    forEach(tree, function (subtree, key) {\n      return traverse(subtree, walker, __spreadArray(__spreadArray([], __read(origin)), __read(parsePath(key))));\n    });\n    return;\n  }\n  var _a = __read(tree, 2),\n    nodeValue = _a[0],\n    children = _a[1];\n  if (children) {\n    forEach(children, function (child, key) {\n      traverse(child, walker, __spreadArray(__spreadArray([], __read(origin)), __read(parsePath(key))));\n    });\n  }\n  walker(nodeValue, origin);\n}\nexport function applyValueAnnotations(plain, annotations, superJson) {\n  traverse(annotations, function (type, path) {\n    plain = setDeep(plain, path, function (v) {\n      return untransformValue(v, type, superJson);\n    });\n  });\n  return plain;\n}\nexport function applyReferentialEqualityAnnotations(plain, annotations) {\n  function apply(identicalPaths, path) {\n    var object = getDeep(plain, parsePath(path));\n    identicalPaths.map(parsePath).forEach(function (identicalObjectPath) {\n      plain = setDeep(plain, identicalObjectPath, function () {\n        return object;\n      });\n    });\n  }\n  if (isArray(annotations)) {\n    var _a = __read(annotations, 2),\n      root = _a[0],\n      other = _a[1];\n    root.forEach(function (identicalPath) {\n      plain = setDeep(plain, parsePath(identicalPath), function () {\n        return plain;\n      });\n    });\n    if (other) {\n      forEach(other, apply);\n    }\n  } else {\n    forEach(annotations, apply);\n  }\n  return plain;\n}\nvar isDeep = function (object, superJson) {\n  return isPlainObject(object) || isArray(object) || isMap(object) || isSet(object) || isInstanceOfRegisteredClass(object, superJson);\n};\nfunction addIdentity(object, path, identities) {\n  var existingSet = identities.get(object);\n  if (existingSet) {\n    existingSet.push(path);\n  } else {\n    identities.set(object, [path]);\n  }\n}\nexport function generateReferentialEqualityAnnotations(identitites, dedupe) {\n  var result = {};\n  var rootEqualityPaths = undefined;\n  identitites.forEach(function (paths) {\n    if (paths.length <= 1) {\n      return;\n    }\n    // if we're not deduping, all of these objects continue existing.\n    // putting the shortest path first makes it easier to parse for humans\n    // if we're deduping though, only the first entry will still exist, so we can't do this optimisation.\n    if (!dedupe) {\n      paths = paths.map(function (path) {\n        return path.map(String);\n      }).sort(function (a, b) {\n        return a.length - b.length;\n      });\n    }\n    var _a = __read(paths),\n      representativePath = _a[0],\n      identicalPaths = _a.slice(1);\n    if (representativePath.length === 0) {\n      rootEqualityPaths = identicalPaths.map(stringifyPath);\n    } else {\n      result[stringifyPath(representativePath)] = identicalPaths.map(stringifyPath);\n    }\n  });\n  if (rootEqualityPaths) {\n    if (isEmptyObject(result)) {\n      return [rootEqualityPaths];\n    } else {\n      return [rootEqualityPaths, result];\n    }\n  } else {\n    return isEmptyObject(result) ? undefined : result;\n  }\n}\nexport var walker = function (object, identities, superJson, dedupe, path, objectsInThisPath, seenObjects) {\n  var _a;\n  if (path === void 0) {\n    path = [];\n  }\n  if (objectsInThisPath === void 0) {\n    objectsInThisPath = [];\n  }\n  if (seenObjects === void 0) {\n    seenObjects = new Map();\n  }\n  var primitive = isPrimitive(object);\n  if (!primitive) {\n    addIdentity(object, path, identities);\n    var seen = seenObjects.get(object);\n    if (seen) {\n      // short-circuit result if we've seen this object before\n      return dedupe ? {\n        transformedValue: null\n      } : seen;\n    }\n  }\n  if (!isDeep(object, superJson)) {\n    var transformed_1 = transformValue(object, superJson);\n    var result_1 = transformed_1 ? {\n      transformedValue: transformed_1.value,\n      annotations: [transformed_1.type]\n    } : {\n      transformedValue: object\n    };\n    if (!primitive) {\n      seenObjects.set(object, result_1);\n    }\n    return result_1;\n  }\n  if (includes(objectsInThisPath, object)) {\n    // prevent circular references\n    return {\n      transformedValue: null\n    };\n  }\n  var transformationResult = transformValue(object, superJson);\n  var transformed = (_a = transformationResult === null || transformationResult === void 0 ? void 0 : transformationResult.value) !== null && _a !== void 0 ? _a : object;\n  var transformedValue = isArray(transformed) ? [] : {};\n  var innerAnnotations = {};\n  forEach(transformed, function (value, index) {\n    var recursiveResult = walker(value, identities, superJson, dedupe, __spreadArray(__spreadArray([], __read(path)), [index]), __spreadArray(__spreadArray([], __read(objectsInThisPath)), [object]), seenObjects);\n    transformedValue[index] = recursiveResult.transformedValue;\n    if (isArray(recursiveResult.annotations)) {\n      innerAnnotations[index] = recursiveResult.annotations;\n    } else if (isPlainObject(recursiveResult.annotations)) {\n      forEach(recursiveResult.annotations, function (tree, key) {\n        innerAnnotations[escapeKey(index) + '.' + key] = tree;\n      });\n    }\n  });\n  var result = isEmptyObject(innerAnnotations) ? {\n    transformedValue: transformedValue,\n    annotations: !!transformationResult ? [transformationResult.type] : undefined\n  } : {\n    transformedValue: transformedValue,\n    annotations: !!transformationResult ? [transformationResult.type, innerAnnotations] : innerAnnotations\n  };\n  if (!primitive) {\n    seenObjects.set(object, result);\n  }\n  return result;\n};", "map": {"version": 3, "names": ["isArray", "isEmptyObject", "isMap", "isPlainObject", "isPrimitive", "isSet", "<PERSON><PERSON><PERSON>", "stringifyPath", "isInstanceOfRegisteredClass", "transformValue", "untransformValue", "includes", "for<PERSON>ach", "parsePath", "getDeep", "setDeep", "traverse", "tree", "walker", "origin", "subtree", "key", "__spread<PERSON><PERSON>y", "__read", "_a", "nodeValue", "children", "child", "applyValueAnnotations", "plain", "annotations", "superJson", "type", "path", "v", "applyReferentialEqualityAnnotations", "apply", "identicalPaths", "object", "map", "identicalObjectPath", "root", "other", "identicalPath", "isDeep", "addIdentity", "identities", "existingSet", "get", "push", "set", "generateReferentialEqualityAnnotations", "identitites", "dedupe", "result", "rootEqualityPaths", "undefined", "paths", "length", "String", "sort", "a", "b", "representative<PERSON><PERSON>", "slice", "objectsInThisPath", "seenObjects", "Map", "primitive", "seen", "transformedValue", "transformed_1", "result_1", "value", "transformationResult", "transformed", "innerAnnotations", "index", "recursiveResult"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/superjson/src/plainer.ts"], "sourcesContent": ["import {\n  isArray,\n  isEmptyObject,\n  isMap,\n  isPlainObject,\n  isPrimitive,\n  isSet,\n} from './is';\nimport { escapeKey, stringifyPath } from './pathstringifier';\nimport {\n  isInstanceOfRegisteredClass,\n  transformValue,\n  TypeAnnotation,\n  untransformValue,\n} from './transformer';\nimport { includes, forEach } from './util';\nimport { parsePath } from './pathstringifier';\nimport { getDeep, setDeep } from './accessDeep';\nimport SuperJSON from '.';\n\ntype Tree<T> = InnerNode<T> | Leaf<T>;\ntype Leaf<T> = [T];\ntype InnerNode<T> = [T, Record<string, Tree<T>>];\n\nexport type MinimisedTree<T> = Tree<T> | Record<string, Tree<T>> | undefined;\n\nfunction traverse<T>(\n  tree: MinimisedTree<T>,\n  walker: (v: T, path: string[]) => void,\n  origin: string[] = []\n): void {\n  if (!tree) {\n    return;\n  }\n\n  if (!isArray(tree)) {\n    forEach(tree, (subtree, key) =>\n      traverse(subtree, walker, [...origin, ...parsePath(key)])\n    );\n    return;\n  }\n\n  const [nodeValue, children] = tree;\n  if (children) {\n    forEach(children, (child, key) => {\n      traverse(child, walker, [...origin, ...parsePath(key)]);\n    });\n  }\n\n  walker(nodeValue, origin);\n}\n\nexport function applyValueAnnotations(\n  plain: any,\n  annotations: MinimisedTree<TypeAnnotation>,\n  superJson: SuperJSON\n) {\n  traverse(annotations, (type, path) => {\n    plain = setDeep(plain, path, v => untransformValue(v, type, superJson));\n  });\n\n  return plain;\n}\n\nexport function applyReferentialEqualityAnnotations(\n  plain: any,\n  annotations: ReferentialEqualityAnnotations\n) {\n  function apply(identicalPaths: string[], path: string) {\n    const object = getDeep(plain, parsePath(path));\n\n    identicalPaths.map(parsePath).forEach(identicalObjectPath => {\n      plain = setDeep(plain, identicalObjectPath, () => object);\n    });\n  }\n\n  if (isArray(annotations)) {\n    const [root, other] = annotations;\n    root.forEach(identicalPath => {\n      plain = setDeep(plain, parsePath(identicalPath), () => plain);\n    });\n\n    if (other) {\n      forEach(other, apply);\n    }\n  } else {\n    forEach(annotations, apply);\n  }\n\n  return plain;\n}\n\nconst isDeep = (object: any, superJson: SuperJSON): boolean =>\n  isPlainObject(object) ||\n  isArray(object) ||\n  isMap(object) ||\n  isSet(object) ||\n  isInstanceOfRegisteredClass(object, superJson);\n\nfunction addIdentity(object: any, path: any[], identities: Map<any, any[][]>) {\n  const existingSet = identities.get(object);\n\n  if (existingSet) {\n    existingSet.push(path);\n  } else {\n    identities.set(object, [path]);\n  }\n}\n\ninterface Result {\n  transformedValue: any;\n  annotations?: MinimisedTree<TypeAnnotation>;\n}\n\nexport type ReferentialEqualityAnnotations =\n  | Record<string, string[]>\n  | [string[]]\n  | [string[], Record<string, string[]>];\n\nexport function generateReferentialEqualityAnnotations(\n  identitites: Map<any, any[][]>,\n  dedupe: boolean\n): ReferentialEqualityAnnotations | undefined {\n  const result: Record<string, string[]> = {};\n  let rootEqualityPaths: string[] | undefined = undefined;\n\n  identitites.forEach(paths => {\n    if (paths.length <= 1) {\n      return;\n    }\n\n    // if we're not deduping, all of these objects continue existing.\n    // putting the shortest path first makes it easier to parse for humans\n    // if we're deduping though, only the first entry will still exist, so we can't do this optimisation.\n    if (!dedupe) {\n      paths = paths\n        .map(path => path.map(String))\n        .sort((a, b) => a.length - b.length);\n    }\n\n    const [representativePath, ...identicalPaths] = paths;\n\n    if (representativePath.length === 0) {\n      rootEqualityPaths = identicalPaths.map(stringifyPath);\n    } else {\n      result[stringifyPath(representativePath)] = identicalPaths.map(\n        stringifyPath\n      );\n    }\n  });\n\n  if (rootEqualityPaths) {\n    if (isEmptyObject(result)) {\n      return [rootEqualityPaths];\n    } else {\n      return [rootEqualityPaths, result];\n    }\n  } else {\n    return isEmptyObject(result) ? undefined : result;\n  }\n}\n\nexport const walker = (\n  object: any,\n  identities: Map<any, any[][]>,\n  superJson: SuperJSON,\n  dedupe: boolean,\n  path: any[] = [],\n  objectsInThisPath: any[] = [],\n  seenObjects = new Map<unknown, Result>()\n): Result => {\n  const primitive = isPrimitive(object);\n\n  if (!primitive) {\n    addIdentity(object, path, identities);\n\n    const seen = seenObjects.get(object);\n    if (seen) {\n      // short-circuit result if we've seen this object before\n      return dedupe\n        ? {\n            transformedValue: null,\n          }\n        : seen;\n    }\n  }\n\n  if (!isDeep(object, superJson)) {\n    const transformed = transformValue(object, superJson);\n\n    const result: Result = transformed\n      ? {\n          transformedValue: transformed.value,\n          annotations: [transformed.type],\n        }\n      : {\n          transformedValue: object,\n        };\n    if (!primitive) {\n      seenObjects.set(object, result);\n    }\n    return result;\n  }\n\n  if (includes(objectsInThisPath, object)) {\n    // prevent circular references\n    return {\n      transformedValue: null,\n    };\n  }\n\n  const transformationResult = transformValue(object, superJson);\n  const transformed = transformationResult?.value ?? object;\n\n  const transformedValue: any = isArray(transformed) ? [] : {};\n  const innerAnnotations: Record<string, Tree<TypeAnnotation>> = {};\n\n  forEach(transformed, (value, index) => {\n    const recursiveResult = walker(\n      value,\n      identities,\n      superJson,\n      dedupe,\n      [...path, index],\n      [...objectsInThisPath, object],\n      seenObjects\n    );\n\n    transformedValue[index] = recursiveResult.transformedValue;\n\n    if (isArray(recursiveResult.annotations)) {\n      innerAnnotations[index] = recursiveResult.annotations;\n    } else if (isPlainObject(recursiveResult.annotations)) {\n      forEach(recursiveResult.annotations, (tree, key) => {\n        innerAnnotations[escapeKey(index) + '.' + key] = tree;\n      });\n    }\n  });\n\n  const result: Result = isEmptyObject(innerAnnotations)\n    ? {\n        transformedValue,\n        annotations: !!transformationResult\n          ? [transformationResult.type]\n          : undefined,\n      }\n    : {\n        transformedValue,\n        annotations: !!transformationResult\n          ? [transformationResult.type, innerAnnotations]\n          : innerAnnotations,\n      };\n  if (!primitive) {\n    seenObjects.set(object, result);\n  }\n\n  return result;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SACEA,OAAO,EACPC,aAAa,EACbC,KAAK,EACLC,aAAa,EACbC,WAAW,EACXC,KAAK,QACA,MAAM;AACb,SAASC,SAAS,EAAEC,aAAa,QAAQ,mBAAmB;AAC5D,SACEC,2BAA2B,EAC3BC,cAAc,EAEdC,gBAAgB,QACX,eAAe;AACtB,SAASC,QAAQ,EAAEC,OAAO,QAAQ,QAAQ;AAC1C,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,OAAO,EAAEC,OAAO,QAAQ,cAAc;AAS/C,SAASC,QAAQA,CACfC,IAAsB,EACtBC,MAAsC,EACtCC,MAAqB;EAArB,IAAAA,MAAA;IAAAA,MAAA,KAAqB;EAAA;EAErB,IAAI,CAACF,IAAI,EAAE;IACT;;EAGF,IAAI,CAACjB,OAAO,CAACiB,IAAI,CAAC,EAAE;IAClBL,OAAO,CAACK,IAAI,EAAE,UAACG,OAAO,EAAEC,GAAG;MACzB,OAAAL,QAAQ,CAACI,OAAO,EAAEF,MAAM,EAAAI,aAAA,CAAAA,aAAA,KAAAC,MAAA,CAAMJ,MAAM,IAAAI,MAAA,CAAKV,SAAS,CAACQ,GAAG,CAAC,GAAE;IAAzD,CAAyD,CAC1D;IACD;;EAGI,IAAAG,EAAA,GAAAD,MAAA,CAAwBN,IAAI;IAA3BQ,SAAS,GAAAD,EAAA;IAAEE,QAAQ,GAAAF,EAAA,GAAQ;EAClC,IAAIE,QAAQ,EAAE;IACZd,OAAO,CAACc,QAAQ,EAAE,UAACC,KAAK,EAAEN,GAAG;MAC3BL,QAAQ,CAACW,KAAK,EAAET,MAAM,EAAAI,aAAA,CAAAA,aAAA,KAAAC,MAAA,CAAMJ,MAAM,IAAAI,MAAA,CAAKV,SAAS,CAACQ,GAAG,CAAC,GAAE;IACzD,CAAC,CAAC;;EAGJH,MAAM,CAACO,SAAS,EAAEN,MAAM,CAAC;AAC3B;AAEA,OAAM,SAAUS,qBAAqBA,CACnCC,KAAU,EACVC,WAA0C,EAC1CC,SAAoB;EAEpBf,QAAQ,CAACc,WAAW,EAAE,UAACE,IAAI,EAAEC,IAAI;IAC/BJ,KAAK,GAAGd,OAAO,CAACc,KAAK,EAAEI,IAAI,EAAE,UAAAC,CAAC;MAAI,OAAAxB,gBAAgB,CAACwB,CAAC,EAAEF,IAAI,EAAED,SAAS,CAAC;IAApC,CAAoC,CAAC;EACzE,CAAC,CAAC;EAEF,OAAOF,KAAK;AACd;AAEA,OAAM,SAAUM,mCAAmCA,CACjDN,KAAU,EACVC,WAA2C;EAE3C,SAASM,KAAKA,CAACC,cAAwB,EAAEJ,IAAY;IACnD,IAAMK,MAAM,GAAGxB,OAAO,CAACe,KAAK,EAAEhB,SAAS,CAACoB,IAAI,CAAC,CAAC;IAE9CI,cAAc,CAACE,GAAG,CAAC1B,SAAS,CAAC,CAACD,OAAO,CAAC,UAAA4B,mBAAmB;MACvDX,KAAK,GAAGd,OAAO,CAACc,KAAK,EAAEW,mBAAmB,EAAE;QAAM,OAAAF,MAAM;MAAN,CAAM,CAAC;IAC3D,CAAC,CAAC;EACJ;EAEA,IAAItC,OAAO,CAAC8B,WAAW,CAAC,EAAE;IAClB,IAAAN,EAAA,GAAAD,MAAA,CAAgBO,WAAW;MAA1BW,IAAI,GAAAjB,EAAA;MAAEkB,KAAK,GAAAlB,EAAA,GAAe;IACjCiB,IAAI,CAAC7B,OAAO,CAAC,UAAA+B,aAAa;MACxBd,KAAK,GAAGd,OAAO,CAACc,KAAK,EAAEhB,SAAS,CAAC8B,aAAa,CAAC,EAAE;QAAM,OAAAd,KAAK;MAAL,CAAK,CAAC;IAC/D,CAAC,CAAC;IAEF,IAAIa,KAAK,EAAE;MACT9B,OAAO,CAAC8B,KAAK,EAAEN,KAAK,CAAC;;GAExB,MAAM;IACLxB,OAAO,CAACkB,WAAW,EAAEM,KAAK,CAAC;;EAG7B,OAAOP,KAAK;AACd;AAEA,IAAMe,MAAM,GAAG,SAAAA,CAACN,MAAW,EAAEP,SAAoB;EAC/C,OAAA5B,aAAa,CAACmC,MAAM,CAAC,IACrBtC,OAAO,CAACsC,MAAM,CAAC,IACfpC,KAAK,CAACoC,MAAM,CAAC,IACbjC,KAAK,CAACiC,MAAM,CAAC,IACb9B,2BAA2B,CAAC8B,MAAM,EAAEP,SAAS,CAAC;AAJ9C,CAI8C;AAEhD,SAASc,WAAWA,CAACP,MAAW,EAAEL,IAAW,EAAEa,UAA6B;EAC1E,IAAMC,WAAW,GAAGD,UAAU,CAACE,GAAG,CAACV,MAAM,CAAC;EAE1C,IAAIS,WAAW,EAAE;IACfA,WAAW,CAACE,IAAI,CAAChB,IAAI,CAAC;GACvB,MAAM;IACLa,UAAU,CAACI,GAAG,CAACZ,MAAM,EAAE,CAACL,IAAI,CAAC,CAAC;;AAElC;AAYA,OAAM,SAAUkB,sCAAsCA,CACpDC,WAA8B,EAC9BC,MAAe;EAEf,IAAMC,MAAM,GAA6B,EAAE;EAC3C,IAAIC,iBAAiB,GAAyBC,SAAS;EAEvDJ,WAAW,CAACxC,OAAO,CAAC,UAAA6C,KAAK;IACvB,IAAIA,KAAK,CAACC,MAAM,IAAI,CAAC,EAAE;MACrB;;IAGF;IACA;IACA;IACA,IAAI,CAACL,MAAM,EAAE;MACXI,KAAK,GAAGA,KAAK,CACVlB,GAAG,CAAC,UAAAN,IAAI;QAAI,OAAAA,IAAI,CAACM,GAAG,CAACoB,MAAM,CAAC;MAAhB,CAAgB,CAAC,CAC7BC,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAK,OAAAD,CAAC,CAACH,MAAM,GAAGI,CAAC,CAACJ,MAAM;MAAnB,CAAmB,CAAC;;IAGlC,IAAAlC,EAAA,GAAAD,MAAA,CAA0CkC,KAAK;MAA9CM,kBAAkB,GAAAvC,EAAA;MAAKa,cAAc,GAAAb,EAAA,CAAAwC,KAAA,GAAS;IAErD,IAAID,kBAAkB,CAACL,MAAM,KAAK,CAAC,EAAE;MACnCH,iBAAiB,GAAGlB,cAAc,CAACE,GAAG,CAAChC,aAAa,CAAC;KACtD,MAAM;MACL+C,MAAM,CAAC/C,aAAa,CAACwD,kBAAkB,CAAC,CAAC,GAAG1B,cAAc,CAACE,GAAG,CAC5DhC,aAAa,CACd;;EAEL,CAAC,CAAC;EAEF,IAAIgD,iBAAiB,EAAE;IACrB,IAAItD,aAAa,CAACqD,MAAM,CAAC,EAAE;MACzB,OAAO,CAACC,iBAAiB,CAAC;KAC3B,MAAM;MACL,OAAO,CAACA,iBAAiB,EAAED,MAAM,CAAC;;GAErC,MAAM;IACL,OAAOrD,aAAa,CAACqD,MAAM,CAAC,GAAGE,SAAS,GAAGF,MAAM;;AAErD;AAEA,OAAO,IAAMpC,MAAM,GAAG,SAAAA,CACpBoB,MAAW,EACXQ,UAA6B,EAC7Bf,SAAoB,EACpBsB,MAAe,EACfpB,IAAgB,EAChBgC,iBAA6B,EAC7BC,WAAwC;;EAFxC,IAAAjC,IAAA;IAAAA,IAAA,KAAgB;EAAA;EAChB,IAAAgC,iBAAA;IAAAA,iBAAA,KAA6B;EAAA;EAC7B,IAAAC,WAAA;IAAAA,WAAA,OAAkBC,GAAG,EAAmB;EAAA;EAExC,IAAMC,SAAS,GAAGhE,WAAW,CAACkC,MAAM,CAAC;EAErC,IAAI,CAAC8B,SAAS,EAAE;IACdvB,WAAW,CAACP,MAAM,EAAEL,IAAI,EAAEa,UAAU,CAAC;IAErC,IAAMuB,IAAI,GAAGH,WAAW,CAAClB,GAAG,CAACV,MAAM,CAAC;IACpC,IAAI+B,IAAI,EAAE;MACR;MACA,OAAOhB,MAAM,GACT;QACEiB,gBAAgB,EAAE;OACnB,GACDD,IAAI;;;EAIZ,IAAI,CAACzB,MAAM,CAACN,MAAM,EAAEP,SAAS,CAAC,EAAE;IAC9B,IAAMwC,aAAW,GAAG9D,cAAc,CAAC6B,MAAM,EAAEP,SAAS,CAAC;IAErD,IAAMyC,QAAM,GAAWD,aAAW,GAC9B;MACED,gBAAgB,EAAEC,aAAW,CAACE,KAAK;MACnC3C,WAAW,EAAE,CAACyC,aAAW,CAACvC,IAAI;KAC/B,GACD;MACEsC,gBAAgB,EAAEhC;KACnB;IACL,IAAI,CAAC8B,SAAS,EAAE;MACdF,WAAW,CAAChB,GAAG,CAACZ,MAAM,EAAEkC,QAAM,CAAC;;IAEjC,OAAOA,QAAM;;EAGf,IAAI7D,QAAQ,CAACsD,iBAAiB,EAAE3B,MAAM,CAAC,EAAE;IACvC;IACA,OAAO;MACLgC,gBAAgB,EAAE;KACnB;;EAGH,IAAMI,oBAAoB,GAAGjE,cAAc,CAAC6B,MAAM,EAAEP,SAAS,CAAC;EAC9D,IAAM4C,WAAW,GAAG,CAAAnD,EAAA,GAAAkD,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAED,KAAK,cAAAjD,EAAA,cAAAA,EAAA,GAAIc,MAAM;EAEzD,IAAMgC,gBAAgB,GAAQtE,OAAO,CAAC2E,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE;EAC5D,IAAMC,gBAAgB,GAAyC,EAAE;EAEjEhE,OAAO,CAAC+D,WAAW,EAAE,UAACF,KAAK,EAAEI,KAAK;IAChC,IAAMC,eAAe,GAAG5D,MAAM,CAC5BuD,KAAK,EACL3B,UAAU,EACVf,SAAS,EACTsB,MAAM,EAAA/B,aAAA,CAAAA,aAAA,KAAAC,MAAA,CACFU,IAAI,KAAE4C,KAAK,IAAAvD,aAAA,CAAAA,aAAA,KAAAC,MAAA,CACX0C,iBAAiB,KAAE3B,MAAM,IAC7B4B,WAAW,CACZ;IAEDI,gBAAgB,CAACO,KAAK,CAAC,GAAGC,eAAe,CAACR,gBAAgB;IAE1D,IAAItE,OAAO,CAAC8E,eAAe,CAAChD,WAAW,CAAC,EAAE;MACxC8C,gBAAgB,CAACC,KAAK,CAAC,GAAGC,eAAe,CAAChD,WAAW;KACtD,MAAM,IAAI3B,aAAa,CAAC2E,eAAe,CAAChD,WAAW,CAAC,EAAE;MACrDlB,OAAO,CAACkE,eAAe,CAAChD,WAAW,EAAE,UAACb,IAAI,EAAEI,GAAG;QAC7CuD,gBAAgB,CAACtE,SAAS,CAACuE,KAAK,CAAC,GAAG,GAAG,GAAGxD,GAAG,CAAC,GAAGJ,IAAI;MACvD,CAAC,CAAC;;EAEN,CAAC,CAAC;EAEF,IAAMqC,MAAM,GAAWrD,aAAa,CAAC2E,gBAAgB,CAAC,GAClD;IACEN,gBAAgB,EAAAA,gBAAA;IAChBxC,WAAW,EAAE,CAAC,CAAC4C,oBAAoB,GAC/B,CAACA,oBAAoB,CAAC1C,IAAI,CAAC,GAC3BwB;GACL,GACD;IACEc,gBAAgB,EAAAA,gBAAA;IAChBxC,WAAW,EAAE,CAAC,CAAC4C,oBAAoB,GAC/B,CAACA,oBAAoB,CAAC1C,IAAI,EAAE4C,gBAAgB,CAAC,GAC7CA;GACL;EACL,IAAI,CAACR,SAAS,EAAE;IACdF,WAAW,CAAChB,GAAG,CAACZ,MAAM,EAAEgB,MAAM,CAAC;;EAGjC,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}