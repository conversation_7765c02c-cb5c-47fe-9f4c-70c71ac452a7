{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isMatch = require('./isMatch.js');\nconst toKey = require('../_internal/toKey.js');\nconst cloneDeep = require('../object/cloneDeep.js');\nconst get = require('../object/get.js');\nconst has = require('../object/has.js');\nfunction matchesProperty(property, source) {\n  switch (typeof property) {\n    case 'object':\n      {\n        var _property;\n        if (Object.is((_property = property) === null || _property === void 0 ? void 0 : _property.valueOf(), -0)) {\n          property = '-0';\n        }\n        break;\n      }\n    case 'number':\n      {\n        property = toKey.toKey(property);\n        break;\n      }\n  }\n  source = cloneDeep.cloneDeep(source);\n  return function (target) {\n    const result = get.get(target, property);\n    if (result === undefined) {\n      return has.has(target, property);\n    }\n    if (source === undefined) {\n      return result === undefined;\n    }\n    return isMatch.isMatch(result, source);\n  };\n}\nexports.matchesProperty = matchesProperty;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isMatch", "require", "to<PERSON><PERSON>", "cloneDeep", "get", "has", "matchesProperty", "property", "source", "_property", "is", "valueOf", "target", "result", "undefined"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/es-toolkit/dist/compat/predicate/matchesProperty.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isMatch = require('./isMatch.js');\nconst toKey = require('../_internal/toKey.js');\nconst cloneDeep = require('../object/cloneDeep.js');\nconst get = require('../object/get.js');\nconst has = require('../object/has.js');\n\nfunction matchesProperty(property, source) {\n    switch (typeof property) {\n        case 'object': {\n            if (Object.is(property?.valueOf(), -0)) {\n                property = '-0';\n            }\n            break;\n        }\n        case 'number': {\n            property = toKey.toKey(property);\n            break;\n        }\n    }\n    source = cloneDeep.cloneDeep(source);\n    return function (target) {\n        const result = get.get(target, property);\n        if (result === undefined) {\n            return has.has(target, property);\n        }\n        if (source === undefined) {\n            return result === undefined;\n        }\n        return isMatch.isMatch(result, source);\n    };\n}\n\nexports.matchesProperty = matchesProperty;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;AACvC,MAAMC,KAAK,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAC9C,MAAME,SAAS,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AACnD,MAAMG,GAAG,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AACvC,MAAMI,GAAG,GAAGJ,OAAO,CAAC,kBAAkB,CAAC;AAEvC,SAASK,eAAeA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EACvC,QAAQ,OAAOD,QAAQ;IACnB,KAAK,QAAQ;MAAE;QAAA,IAAAE,SAAA;QACX,IAAIf,MAAM,CAACgB,EAAE,EAAAD,SAAA,GAACF,QAAQ,cAAAE,SAAA,uBAARA,SAAA,CAAUE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACpCJ,QAAQ,GAAG,IAAI;QACnB;QACA;MACJ;IACA,KAAK,QAAQ;MAAE;QACXA,QAAQ,GAAGL,KAAK,CAACA,KAAK,CAACK,QAAQ,CAAC;QAChC;MACJ;EACJ;EACAC,MAAM,GAAGL,SAAS,CAACA,SAAS,CAACK,MAAM,CAAC;EACpC,OAAO,UAAUI,MAAM,EAAE;IACrB,MAAMC,MAAM,GAAGT,GAAG,CAACA,GAAG,CAACQ,MAAM,EAAEL,QAAQ,CAAC;IACxC,IAAIM,MAAM,KAAKC,SAAS,EAAE;MACtB,OAAOT,GAAG,CAACA,GAAG,CAACO,MAAM,EAAEL,QAAQ,CAAC;IACpC;IACA,IAAIC,MAAM,KAAKM,SAAS,EAAE;MACtB,OAAOD,MAAM,KAAKC,SAAS;IAC/B;IACA,OAAOd,OAAO,CAACA,OAAO,CAACa,MAAM,EAAEL,MAAM,CAAC;EAC1C,CAAC;AACL;AAEAZ,OAAO,CAACU,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}