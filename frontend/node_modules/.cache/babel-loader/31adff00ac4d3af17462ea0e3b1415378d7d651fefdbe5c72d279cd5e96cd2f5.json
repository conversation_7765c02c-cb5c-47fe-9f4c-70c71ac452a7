{"ast": null, "code": "import * as React from 'react';\nimport { useRef } from 'react';\nimport { Provider } from 'react-redux';\nimport { createRechartsStore } from './store';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { RechartsReduxContext } from './RechartsReduxContext';\nexport function RechartsStoreProvider(_ref) {\n  var {\n    preloadedState,\n    children,\n    reduxStoreName\n  } = _ref;\n  var isPanorama = useIsPanorama();\n  /*\n   * Why the ref? Redux official documentation recommends to use store as a singleton,\n   * and reuse that everywhere: https://redux-toolkit.js.org/api/configureStore#basic-example\n   *\n   * Which is correct! Except that is considering deploying Redux in an app.\n   * Recharts as a library supports multiple charts on the same page.\n   * And each of these charts needs its own store independent of others!\n   *\n   * The alternative is to have everything in the store keyed by the chart id.\n   * Which would make working with everything a little bit more painful because we need the chart id everywhere.\n   */\n  var storeRef = useRef(null);\n\n  /*\n   * Panorama means that this chart is not its own chart, it's only a \"preview\"\n   * being rendered as a child of Brush.\n   * In such case, it should not have a store on its own - it should implicitly inherit\n   * whatever data is in the \"parent\" or \"root\" chart.\n   * Which here is represented by not having a Provider at all. All selectors will use the root store by default.\n   */\n  if (isPanorama) {\n    return children;\n  }\n  if (storeRef.current == null) {\n    storeRef.current = createRechartsStore(preloadedState, reduxStoreName);\n  }\n\n  // ts-expect-error React-Redux types demand that the context internal value is not null, but we have that as default.\n  var nonNullContext = RechartsReduxContext;\n  return /*#__PURE__*/React.createElement(Provider, {\n    context: nonNullContext,\n    store: storeRef.current\n  }, children);\n}", "map": {"version": 3, "names": ["React", "useRef", "Provider", "createRechartsStore", "useIsPanorama", "RechartsReduxContext", "RechartsStoreProvider", "_ref", "preloadedState", "children", "reduxStoreName", "isPanorama", "storeRef", "current", "nonNullContext", "createElement", "context", "store"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/recharts/es6/state/RechartsStoreProvider.js"], "sourcesContent": ["import * as React from 'react';\nimport { useRef } from 'react';\nimport { Provider } from 'react-redux';\nimport { createRechartsStore } from './store';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { RechartsReduxContext } from './RechartsReduxContext';\nexport function RechartsStoreProvider(_ref) {\n  var {\n    preloadedState,\n    children,\n    reduxStoreName\n  } = _ref;\n  var isPanorama = useIsPanorama();\n  /*\n   * Why the ref? Redux official documentation recommends to use store as a singleton,\n   * and reuse that everywhere: https://redux-toolkit.js.org/api/configureStore#basic-example\n   *\n   * Which is correct! Except that is considering deploying Redux in an app.\n   * Recharts as a library supports multiple charts on the same page.\n   * And each of these charts needs its own store independent of others!\n   *\n   * The alternative is to have everything in the store keyed by the chart id.\n   * Which would make working with everything a little bit more painful because we need the chart id everywhere.\n   */\n  var storeRef = useRef(null);\n\n  /*\n   * Panorama means that this chart is not its own chart, it's only a \"preview\"\n   * being rendered as a child of Brush.\n   * In such case, it should not have a store on its own - it should implicitly inherit\n   * whatever data is in the \"parent\" or \"root\" chart.\n   * Which here is represented by not having a Provider at all. All selectors will use the root store by default.\n   */\n  if (isPanorama) {\n    return children;\n  }\n  if (storeRef.current == null) {\n    storeRef.current = createRechartsStore(preloadedState, reduxStoreName);\n  }\n\n  // ts-expect-error React-Redux types demand that the context internal value is not null, but we have that as default.\n  var nonNullContext = RechartsReduxContext;\n  return /*#__PURE__*/React.createElement(Provider, {\n    context: nonNullContext,\n    store: storeRef.current\n  }, children);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,mBAAmB,QAAQ,SAAS;AAC7C,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,IAAI;IACFC,cAAc;IACdC,QAAQ;IACRC;EACF,CAAC,GAAGH,IAAI;EACR,IAAII,UAAU,GAAGP,aAAa,CAAC,CAAC;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIQ,QAAQ,GAAGX,MAAM,CAAC,IAAI,CAAC;;EAE3B;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIU,UAAU,EAAE;IACd,OAAOF,QAAQ;EACjB;EACA,IAAIG,QAAQ,CAACC,OAAO,IAAI,IAAI,EAAE;IAC5BD,QAAQ,CAACC,OAAO,GAAGV,mBAAmB,CAACK,cAAc,EAAEE,cAAc,CAAC;EACxE;;EAEA;EACA,IAAII,cAAc,GAAGT,oBAAoB;EACzC,OAAO,aAAaL,KAAK,CAACe,aAAa,CAACb,QAAQ,EAAE;IAChDc,OAAO,EAAEF,cAAc;IACvBG,KAAK,EAAEL,QAAQ,CAACC;EAClB,CAAC,EAAEJ,QAAQ,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}