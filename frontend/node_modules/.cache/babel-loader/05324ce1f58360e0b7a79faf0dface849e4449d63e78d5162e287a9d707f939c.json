{"ast": null, "code": "export { getNiceTickValues, getTickValuesFixedDomain } from './getNiceTickValues';", "map": {"version": 3, "names": ["getNiceTickValues", "getTickValuesFixedDomain"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/recharts/es6/util/scale/index.js"], "sourcesContent": ["export { getNiceTickValues, getTickValuesFixedDomain } from './getNiceTickValues';"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,wBAAwB,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}