{"ast": null, "code": "export default function (a, b) {\n  var d = new Date();\n  return a = +a, b = +b, function (t) {\n    return d.setTime(a * (1 - t) + b * t), d;\n  };\n}", "map": {"version": 3, "names": ["a", "b", "d", "Date", "t", "setTime"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/d3-interpolate/src/date.js"], "sourcesContent": ["export default function(a, b) {\n  var d = new Date;\n  return a = +a, b = +b, function(t) {\n    return d.setTime(a * (1 - t) + b * t), d;\n  };\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAIC,CAAC,GAAG,IAAIC,IAAI,CAAD,CAAC;EAChB,OAAOH,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC,EAAE,UAASG,CAAC,EAAE;IACjC,OAAOF,CAAC,CAACG,OAAO,CAACL,CAAC,IAAI,CAAC,GAAGI,CAAC,CAAC,GAAGH,CAAC,GAAGG,CAAC,CAAC,EAAEF,CAAC;EAC1C,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}