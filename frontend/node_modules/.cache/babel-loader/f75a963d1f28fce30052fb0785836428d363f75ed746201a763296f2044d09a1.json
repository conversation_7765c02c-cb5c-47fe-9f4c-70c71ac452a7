{"ast": null, "code": "var identity = i => i;\nexport var PLACE_HOLDER = {\n  '@@functional/placeholder': true\n};\nvar isPlaceHolder = val => val === PLACE_HOLDER;\nvar curry0 = fn => function _curried() {\n  if (arguments.length === 0 || arguments.length === 1 && isPlaceHolder(arguments.length <= 0 ? undefined : arguments[0])) {\n    return _curried;\n  }\n  return fn(...arguments);\n};\nvar curryN = (n, fn) => {\n  if (n === 1) {\n    return fn;\n  }\n  return curry0(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var argsLength = args.filter(arg => arg !== PLACE_HOLDER).length;\n    if (argsLength >= n) {\n      return fn(...args);\n    }\n    return curryN(n - argsLength, curry0(function () {\n      for (var _len2 = arguments.length, restArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        restArgs[_key2] = arguments[_key2];\n      }\n      var newArgs = args.map(arg => isPlaceHolder(arg) ? restArgs.shift() : arg);\n      return fn(...newArgs, ...restArgs);\n    }));\n  });\n};\nexport var curry = fn => curryN(fn.length, fn);\nexport var range = (begin, end) => {\n  var arr = [];\n  for (var i = begin; i < end; ++i) {\n    arr[i - begin] = i;\n  }\n  return arr;\n};\nexport var map = curry((fn, arr) => {\n  if (Array.isArray(arr)) {\n    return arr.map(fn);\n  }\n  return Object.keys(arr).map(key => arr[key]).map(fn);\n});\nexport var compose = function compose() {\n  for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    args[_key3] = arguments[_key3];\n  }\n  if (!args.length) {\n    return identity;\n  }\n  var fns = args.reverse();\n  // first function can receive multiply arguments\n  var firstFn = fns[0];\n  var tailsFn = fns.slice(1);\n  return function () {\n    return tailsFn.reduce((res, fn) => fn(res), firstFn(...arguments));\n  };\n};\nexport var reverse = arr => {\n  if (Array.isArray(arr)) {\n    return arr.reverse();\n  }\n\n  // can be string\n  return arr.split('').reverse().join('');\n};\nexport var memoize = fn => {\n  var lastArgs = null;\n  var lastResult = null;\n  return function () {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    if (lastArgs && args.every((val, i) => {\n      var _lastArgs;\n      return val === ((_lastArgs = lastArgs) === null || _lastArgs === void 0 ? void 0 : _lastArgs[i]);\n    })) {\n      return lastResult;\n    }\n    lastArgs = args;\n    lastResult = fn(...args);\n    return lastResult;\n  };\n};", "map": {"version": 3, "names": ["identity", "i", "PLACE_HOLDER", "isPlaceHolder", "val", "curry0", "fn", "_curried", "arguments", "length", "undefined", "curryN", "n", "_len", "args", "Array", "_key", "arg<PERSON><PERSON><PERSON><PERSON>", "filter", "arg", "_len2", "restArgs", "_key2", "newArgs", "map", "shift", "curry", "range", "begin", "end", "arr", "isArray", "Object", "keys", "key", "compose", "_len3", "_key3", "fns", "reverse", "firstFn", "tailsFn", "slice", "reduce", "res", "split", "join", "memoize", "lastArgs", "lastResult", "_len4", "_key4", "every", "_lastArgs"], "sources": ["/Users/<USER>/CascadeProjects/BestGadget/frontend/node_modules/recharts/es6/util/scale/util/utils.js"], "sourcesContent": ["var identity = i => i;\nexport var PLACE_HOLDER = {\n  '@@functional/placeholder': true\n};\nvar isPlaceHolder = val => val === PLACE_HOLDER;\nvar curry0 = fn => function _curried() {\n  if (arguments.length === 0 || arguments.length === 1 && isPlaceHolder(arguments.length <= 0 ? undefined : arguments[0])) {\n    return _curried;\n  }\n  return fn(...arguments);\n};\nvar curryN = (n, fn) => {\n  if (n === 1) {\n    return fn;\n  }\n  return curry0(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var argsLength = args.filter(arg => arg !== PLACE_HOLDER).length;\n    if (argsLength >= n) {\n      return fn(...args);\n    }\n    return curryN(n - argsLength, curry0(function () {\n      for (var _len2 = arguments.length, restArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        restArgs[_key2] = arguments[_key2];\n      }\n      var newArgs = args.map(arg => isPlaceHolder(arg) ? restArgs.shift() : arg);\n      return fn(...newArgs, ...restArgs);\n    }));\n  });\n};\nexport var curry = fn => curryN(fn.length, fn);\nexport var range = (begin, end) => {\n  var arr = [];\n  for (var i = begin; i < end; ++i) {\n    arr[i - begin] = i;\n  }\n  return arr;\n};\nexport var map = curry((fn, arr) => {\n  if (Array.isArray(arr)) {\n    return arr.map(fn);\n  }\n  return Object.keys(arr).map(key => arr[key]).map(fn);\n});\nexport var compose = function compose() {\n  for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    args[_key3] = arguments[_key3];\n  }\n  if (!args.length) {\n    return identity;\n  }\n  var fns = args.reverse();\n  // first function can receive multiply arguments\n  var firstFn = fns[0];\n  var tailsFn = fns.slice(1);\n  return function () {\n    return tailsFn.reduce((res, fn) => fn(res), firstFn(...arguments));\n  };\n};\nexport var reverse = arr => {\n  if (Array.isArray(arr)) {\n    return arr.reverse();\n  }\n\n  // can be string\n  return arr.split('').reverse().join('');\n};\nexport var memoize = fn => {\n  var lastArgs = null;\n  var lastResult = null;\n  return function () {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    if (lastArgs && args.every((val, i) => {\n      var _lastArgs;\n      return val === ((_lastArgs = lastArgs) === null || _lastArgs === void 0 ? void 0 : _lastArgs[i]);\n    })) {\n      return lastResult;\n    }\n    lastArgs = args;\n    lastResult = fn(...args);\n    return lastResult;\n  };\n};"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,CAAC,IAAIA,CAAC;AACrB,OAAO,IAAIC,YAAY,GAAG;EACxB,0BAA0B,EAAE;AAC9B,CAAC;AACD,IAAIC,aAAa,GAAGC,GAAG,IAAIA,GAAG,KAAKF,YAAY;AAC/C,IAAIG,MAAM,GAAGC,EAAE,IAAI,SAASC,QAAQA,CAAA,EAAG;EACrC,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,IAAID,SAAS,CAACC,MAAM,KAAK,CAAC,IAAIN,aAAa,CAACK,SAAS,CAACC,MAAM,IAAI,CAAC,GAAGC,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;IACvH,OAAOD,QAAQ;EACjB;EACA,OAAOD,EAAE,CAAC,GAAGE,SAAS,CAAC;AACzB,CAAC;AACD,IAAIG,MAAM,GAAGA,CAACC,CAAC,EAAEN,EAAE,KAAK;EACtB,IAAIM,CAAC,KAAK,CAAC,EAAE;IACX,OAAON,EAAE;EACX;EACA,OAAOD,MAAM,CAAC,YAAY;IACxB,KAAK,IAAIQ,IAAI,GAAGL,SAAS,CAACC,MAAM,EAAEK,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGR,SAAS,CAACQ,IAAI,CAAC;IAC9B;IACA,IAAIC,UAAU,GAAGH,IAAI,CAACI,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKjB,YAAY,CAAC,CAACO,MAAM;IAChE,IAAIQ,UAAU,IAAIL,CAAC,EAAE;MACnB,OAAON,EAAE,CAAC,GAAGQ,IAAI,CAAC;IACpB;IACA,OAAOH,MAAM,CAACC,CAAC,GAAGK,UAAU,EAAEZ,MAAM,CAAC,YAAY;MAC/C,KAAK,IAAIe,KAAK,GAAGZ,SAAS,CAACC,MAAM,EAAEY,QAAQ,GAAG,IAAIN,KAAK,CAACK,KAAK,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;QACjGD,QAAQ,CAACC,KAAK,CAAC,GAAGd,SAAS,CAACc,KAAK,CAAC;MACpC;MACA,IAAIC,OAAO,GAAGT,IAAI,CAACU,GAAG,CAACL,GAAG,IAAIhB,aAAa,CAACgB,GAAG,CAAC,GAAGE,QAAQ,CAACI,KAAK,CAAC,CAAC,GAAGN,GAAG,CAAC;MAC1E,OAAOb,EAAE,CAAC,GAAGiB,OAAO,EAAE,GAAGF,QAAQ,CAAC;IACpC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAIK,KAAK,GAAGpB,EAAE,IAAIK,MAAM,CAACL,EAAE,CAACG,MAAM,EAAEH,EAAE,CAAC;AAC9C,OAAO,IAAIqB,KAAK,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjC,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAK,IAAI7B,CAAC,GAAG2B,KAAK,EAAE3B,CAAC,GAAG4B,GAAG,EAAE,EAAE5B,CAAC,EAAE;IAChC6B,GAAG,CAAC7B,CAAC,GAAG2B,KAAK,CAAC,GAAG3B,CAAC;EACpB;EACA,OAAO6B,GAAG;AACZ,CAAC;AACD,OAAO,IAAIN,GAAG,GAAGE,KAAK,CAAC,CAACpB,EAAE,EAAEwB,GAAG,KAAK;EAClC,IAAIf,KAAK,CAACgB,OAAO,CAACD,GAAG,CAAC,EAAE;IACtB,OAAOA,GAAG,CAACN,GAAG,CAAClB,EAAE,CAAC;EACpB;EACA,OAAO0B,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAACN,GAAG,CAACU,GAAG,IAAIJ,GAAG,CAACI,GAAG,CAAC,CAAC,CAACV,GAAG,CAAClB,EAAE,CAAC;AACtD,CAAC,CAAC;AACF,OAAO,IAAI6B,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;EACtC,KAAK,IAAIC,KAAK,GAAG5B,SAAS,CAACC,MAAM,EAAEK,IAAI,GAAG,IAAIC,KAAK,CAACqB,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;IAC7FvB,IAAI,CAACuB,KAAK,CAAC,GAAG7B,SAAS,CAAC6B,KAAK,CAAC;EAChC;EACA,IAAI,CAACvB,IAAI,CAACL,MAAM,EAAE;IAChB,OAAOT,QAAQ;EACjB;EACA,IAAIsC,GAAG,GAAGxB,IAAI,CAACyB,OAAO,CAAC,CAAC;EACxB;EACA,IAAIC,OAAO,GAAGF,GAAG,CAAC,CAAC,CAAC;EACpB,IAAIG,OAAO,GAAGH,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC;EAC1B,OAAO,YAAY;IACjB,OAAOD,OAAO,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEtC,EAAE,KAAKA,EAAE,CAACsC,GAAG,CAAC,EAAEJ,OAAO,CAAC,GAAGhC,SAAS,CAAC,CAAC;EACpE,CAAC;AACH,CAAC;AACD,OAAO,IAAI+B,OAAO,GAAGT,GAAG,IAAI;EAC1B,IAAIf,KAAK,CAACgB,OAAO,CAACD,GAAG,CAAC,EAAE;IACtB,OAAOA,GAAG,CAACS,OAAO,CAAC,CAAC;EACtB;;EAEA;EACA,OAAOT,GAAG,CAACe,KAAK,CAAC,EAAE,CAAC,CAACN,OAAO,CAAC,CAAC,CAACO,IAAI,CAAC,EAAE,CAAC;AACzC,CAAC;AACD,OAAO,IAAIC,OAAO,GAAGzC,EAAE,IAAI;EACzB,IAAI0C,QAAQ,GAAG,IAAI;EACnB,IAAIC,UAAU,GAAG,IAAI;EACrB,OAAO,YAAY;IACjB,KAAK,IAAIC,KAAK,GAAG1C,SAAS,CAACC,MAAM,EAAEK,IAAI,GAAG,IAAIC,KAAK,CAACmC,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MAC7FrC,IAAI,CAACqC,KAAK,CAAC,GAAG3C,SAAS,CAAC2C,KAAK,CAAC;IAChC;IACA,IAAIH,QAAQ,IAAIlC,IAAI,CAACsC,KAAK,CAAC,CAAChD,GAAG,EAAEH,CAAC,KAAK;MACrC,IAAIoD,SAAS;MACb,OAAOjD,GAAG,MAAM,CAACiD,SAAS,GAAGL,QAAQ,MAAM,IAAI,IAAIK,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACpD,CAAC,CAAC,CAAC;IAClG,CAAC,CAAC,EAAE;MACF,OAAOgD,UAAU;IACnB;IACAD,QAAQ,GAAGlC,IAAI;IACfmC,UAAU,GAAG3C,EAAE,CAAC,GAAGQ,IAAI,CAAC;IACxB,OAAOmC,UAAU;EACnB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}