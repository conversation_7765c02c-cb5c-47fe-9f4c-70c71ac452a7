{"name": "best-gadget-frontend", "version": "0.1.0", "private": true, "dependencies": {"@heroicons/react": "^2.2.0", "@tanstack/react-query": "^4.36.1", "@tanstack/react-query-devtools": "^4.36.1", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.17", "axios": "^1.6.5", "clsx": "^2.1.0", "date-fns": "^2.30.0", "formik": "^2.4.5", "framer-motion": "^11.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.21.3", "react-scripts": "^5.0.1", "recharts": "^3.0.2", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.1", "typescript": "^4.9.5", "yup": "^1.3.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.11", "@types/node": "^20.11.0", "postcss": "^8.4.35"}}