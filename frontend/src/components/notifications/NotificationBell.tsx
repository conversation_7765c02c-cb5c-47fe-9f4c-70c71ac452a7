import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';
import { Bell, X, Check } from 'lucide-react';
import { UserRole } from '../../components/users/UserForm';

interface Notification {
  id: number;
  type: string;
  title: string;
  message: string;
  status: string;
  isDismissed: boolean;
  isResolved: boolean;
  related: any;
  createdAt: string;
}

const NotificationBell: React.FC = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [showNotifications, setShowNotifications] = useState(false);

  useEffect(() => {
    fetchNotifications();
    fetchUnreadCount();

    // Poll for updates every 5 minutes
    const interval = setInterval(() => {
      fetchNotifications();
      fetchUnreadCount();
    }, 300000);

    return () => clearInterval(interval);
  }, []);

  const fetchNotifications = async () => {
    try {
      const response = await axios.get('/api/notifications');
      setNotifications(response.data);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    }
  };

  const fetchUnreadCount = async () => {
    try {
      const response = await axios.get('/api/notifications/count');
      setUnreadCount(response.data.count);
    } catch (error) {
      console.error('Error fetching unread count:', error);
    }
  };

  const markAsRead = async (id: number) => {
    try {
      await axios.post(`/api/notifications/${id}/read`);
      fetchNotifications();
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const dismissNotification = async (id: number) => {
    try {
      await axios.post(`/api/notifications/${id}/dismiss`);
      fetchNotifications();
    } catch (error) {
      console.error('Error dismissing notification:', error);
    }
  };

  const resolveNotification = async (id: number) => {
    try {
      if (!user || (user.role !== UserRole.ADMIN && user.role !== UserRole.STORE_OWNER)) {
        return;
      }
      await axios.post(`/api/notifications/${id}/resolve`);
      fetchNotifications();
    } catch (error) {
      console.error('Error resolving notification:', error);
    }
  };

  return (
    <div className="relative inline-block text-left">
      <button
        onClick={() => setShowNotifications(!showNotifications)}
        className="flex items-center space-x-1"
      >
        <Bell className="h-6 w-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {unreadCount}
          </span>
        )}
      </button>

      {showNotifications && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg overflow-hidden z-50">
          <div className="p-4">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className="border-b last:border-0 p-3 hover:bg-gray-50 cursor-pointer"
              >
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-semibold">{notification.title}</p>
                    <p className="text-sm text-gray-600">{notification.message}</p>
                    <p className="text-xs text-gray-400 mt-1">
                      {new Date(notification.createdAt).toLocaleString()}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    {notification.status === 'unread' && (
                      <button
                        onClick={() => markAsRead(notification.id)}
                        className="text-blue-500 hover:text-blue-700"
                      >
                        Mark as read
                      </button>
                    )}
                    {notification.isDismissed ? (
                      <button
                        onClick={() => dismissNotification(notification.id)}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        <X size={16} />
                      </button>
                    ) : (
                      <button
                        onClick={() => dismissNotification(notification.id)}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        Dismiss
                      </button>
                    )}
                    {user && (user.role === UserRole.ADMIN || user.role === UserRole.STORE_OWNER) ? (
                      <button
                        onClick={() => resolveNotification(notification.id)}
                        className="text-green-500 hover:text-green-700"
                      >
                        <Check size={16} />
                      </button>
                    ) : null}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationBell;
