import { useFormik } from 'formik';
import * as Yup from 'yup';
import axios from 'axios';
import { useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

export interface Product {
  id?: number;
  name: string;
  category: string;
  sku: string;
  brand: string;
  purchasePrice: number;
  retailPrice: number;
  warranty: string;
}

interface ProductFormProps {
  onSubmit: (data: Product) => Promise<void>;
  onClose: () => void;
  product?: Product;
}

const ProductForm = ({ onSubmit, onClose, product }: ProductFormProps) => {
  const [loading, setLoading] = useState(false);

  const validationSchema = Yup.object({
    name: Yup.string().required('Product name is required'),
    category: Yup.string().required('Category is required'),
    sku: Yup.string().required('SKU is required'),
    brand: Yup.string().required('Brand is required'),
    purchasePrice: Yup.number().required('Purchase price is required'),
    retailPrice: Yup.number().required('Retail price is required'),
    warranty: Yup.string().required('Warranty is required'),
  });

  const formik = useFormik({
    initialValues: {
      name: product?.name || '',
      category: product?.category || '',
      sku: product?.sku || '',
      brand: product?.brand || '',
      purchasePrice: product?.purchasePrice || 0,
      retailPrice: product?.retailPrice || 0,
      warranty: product?.warranty || '',
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        await onSubmit(values as Product);
      } catch (error) {
        console.error('Error submitting form:', error);
      } finally {
        setLoading(false);
      }
    },
  });

  return (
    <form onSubmit={formik.handleSubmit} className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">
          {product ? 'Edit Product' : 'Add New Product'}
        </h3>
        <button
          type="button"
          onClick={onClose}
          className="text-gray-400 hover:text-gray-500"
        >
          <XMarkIcon className="h-5 w-5" aria-hidden="true" />
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Product Name
          </label>
          <input
            type="text"
            id="name"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('name')}
          />
          {formik.touched.name && formik.errors.name && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.name}</p>
          )}
        </div>

        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700">
            Category
          </label>
          <select
            id="category"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('category')}
          >
            <option value="">Select category</option>
            <option value="Electronics">Electronics</option>
            <option value="Mobile Phones">Mobile Phones</option>
            <option value="Accessories">Accessories</option>
            <option value="Computers">Computers</option>
            <option value="TVs">TVs</option>
            <option value="Audio">Audio</option>
            <option value="Gaming">Gaming</option>
          </select>
          {formik.touched.category && formik.errors.category && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.category}</p>
          )}
        </div>

        <div>
          <label htmlFor="sku" className="block text-sm font-medium text-gray-700">
            SKU
          </label>
          <input
            type="text"
            id="sku"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('sku')}
          />
          {formik.touched.sku && formik.errors.sku && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.sku}</p>
          )}
        </div>

        <div>
          <label htmlFor="brand" className="block text-sm font-medium text-gray-700">
            Brand
          </label>
          <input
            type="text"
            id="brand"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('brand')}
          />
          {formik.touched.brand && formik.errors.brand && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.brand}</p>
          )}
        </div>

        <div>
          <label htmlFor="purchasePrice" className="block text-sm font-medium text-gray-700">
            Purchase Price (RWF)
          </label>
          <input
            type="number"
            id="purchasePrice"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('purchasePrice')}
          />
          {formik.touched.purchasePrice && formik.errors.purchasePrice && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.purchasePrice}</p>
          )}
        </div>

        <div>
          <label htmlFor="retailPrice" className="block text-sm font-medium text-gray-700">
            Retail Price (RWF)
          </label>
          <input
            type="number"
            id="retailPrice"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('retailPrice')}
          />
          {formik.touched.retailPrice && formik.errors.retailPrice && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.retailPrice}</p>
          )}
        </div>

        <div className="col-span-2">
          <label htmlFor="warranty" className="block text-sm font-medium text-gray-700">
            Warranty
          </label>
          <input
            type="text"
            id="warranty"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('warranty')}
            placeholder="e.g., 1 year, 2 years, etc."
          />
          {formik.touched.warranty && formik.errors.warranty && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.warranty}</p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onClose}
          className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          {loading ? 'Saving...' : product ? 'Update Product' : 'Add Product'}
        </button>
      </div>
    </form>
  );
};

export default ProductForm;
