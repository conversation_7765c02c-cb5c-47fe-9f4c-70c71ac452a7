import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

export interface Store {
  id?: number;
  name: string;
  address: string;
  city: string;
  phoneNumber: string;
  email: string;
  openingHours: {
    monday: string;
    tuesday: string;
    wednesday: string;
    thursday: string;
    friday: string;
    saturday: string;
    sunday: string;
  };
}

interface StoreFormProps {
  onSubmit: (data: Store) => Promise<void>;
  onClose: () => void;
  store?: Store;
}

const StoreForm = ({ onSubmit, onClose, store }: StoreFormProps) => {
  const [loading, setLoading] = useState(false);

  const validationSchema = Yup.object({
    name: Yup.string().required('Store name is required'),
    address: Yup.string().required('Address is required'),
    city: Yup.string().required('City is required'),
    phoneNumber: Yup.string()
      .required('Phone number is required')
      .matches(/^[0-9]+$/, 'Phone number must only contain numbers'),
    email: Yup.string()
      .required('Email is required')
      .email('Invalid email address'),
    openingHours: Yup.object().shape({
      monday: Yup.string().required('Opening hours for Monday are required'),
      tuesday: Yup.string().required('Opening hours for Tuesday are required'),
      wednesday: Yup.string().required('Opening hours for Wednesday are required'),
      thursday: Yup.string().required('Opening hours for Thursday are required'),
      friday: Yup.string().required('Opening hours for Friday are required'),
      saturday: Yup.string().required('Opening hours for Saturday are required'),
      sunday: Yup.string().required('Opening hours for Sunday are required'),
    }),
  });

  const formik = useFormik({
    initialValues: {
      name: store?.name || '',
      address: store?.address || '',
      city: store?.city || '',
      phoneNumber: store?.phoneNumber || '',
      email: store?.email || '',
      openingHours: store?.openingHours || {
        monday: '',
        tuesday: '',
        wednesday: '',
        thursday: '',
        friday: '',
        saturday: '',
        sunday: '',
      },
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        await onSubmit(values as Store);
      } catch (error) {
        console.error('Error submitting form:', error);
      } finally {
        setLoading(false);
      }
    },
  });

  return (
    <form onSubmit={formik.handleSubmit} className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">
          {store ? 'Edit Store' : 'Add New Store'}
        </h3>
        <button
          type="button"
          onClick={onClose}
          className="text-gray-400 hover:text-gray-500"
        >
          <XMarkIcon className="h-5 w-5" aria-hidden="true" />
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Store Name
          </label>
          <input
            type="text"
            id="name"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('name')}
          />
          {formik.touched.name && formik.errors.name && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.name}</p>
          )}
        </div>

        <div>
          <label htmlFor="city" className="block text-sm font-medium text-gray-700">
            City
          </label>
          <input
            type="text"
            id="city"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('city')}
          />
          {formik.touched.city && formik.errors.city && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.city}</p>
          )}
        </div>

        <div className="col-span-2">
          <label htmlFor="address" className="block text-sm font-medium text-gray-700">
            Address
          </label>
          <textarea
            id="address"
            rows={3}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('address')}
          />
          {formik.touched.address && formik.errors.address && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.address}</p>
          )}
        </div>

        <div>
          <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
            Phone Number
          </label>
          <input
            type="tel"
            id="phoneNumber"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('phoneNumber')}
          />
          {formik.touched.phoneNumber && formik.errors.phoneNumber && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.phoneNumber}</p>
          )}
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            Email
          </label>
          <input
            type="email"
            id="email"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('email')}
          />
          {formik.touched.email && formik.errors.email && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.email}</p>
          )}
        </div>
      </div>

      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-4">Opening Hours</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => (
            <div key={day}>
              <label htmlFor={day} className="block text-sm font-medium text-gray-700">
                {day.charAt(0).toUpperCase() + day.slice(1)}
              </label>
              <input
                type="text"
                id={day}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                {...formik.getFieldProps(`openingHours.${day}`)}
              />
              {formik.touched.openingHours && formik.errors.openingHours && formik.errors.openingHours[day as keyof typeof formik.errors.openingHours] && (
                <p className="mt-1 text-sm text-red-600">{formik.errors.openingHours[day as keyof typeof formik.errors.openingHours]}</p>
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-end">
        <button
          type="button"
          onClick={onClose}
          className="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Saving...' : store ? 'Update Store' : 'Add Store'}
        </button>
      </div>
    </form>
  );
};

export default StoreForm;
