import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import axios from 'axios';

// Define user roles
export enum UserRole {
  ADMIN = 'admin',
  STORE_OWNER = 'store_owner',
  SALES_REP = 'sales_rep'
}

export interface User {
  id?: number;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  storeId?: number | null;
}

interface UserFormProps {
  onSubmit: (data: User) => Promise<void>;
  onClose: () => void;
  user?: User;
}

const UserForm = ({ onSubmit, onClose, user }: UserFormProps) => {
  const [loading, setLoading] = useState(false);
  const [stores, setStores] = useState<any[]>([]);

  // Fetch stores for the store selector
  const fetchStores = async () => {
    try {
      const response = await axios.get('/api/stores');
      setStores(response.data);
    } catch (error) {
      console.error('Error fetching stores:', error);
    }
  };

  // Fetch stores when component mounts
  if (stores.length === 0) {
    fetchStores();
  }

  const validationSchema = Yup.object({
    email: Yup.string()
      .required('Email is required')
      .email('Invalid email address'),
    firstName: Yup.string().required('First name is required'),
    lastName: Yup.string().required('Last name is required'),
    role: Yup.string()
      .required('Role is required')
      .oneOf([UserRole.ADMIN, UserRole.STORE_OWNER, UserRole.SALES_REP]),
    storeId: Yup.number()
      .when('role', {
        is: (role: UserRole) => role !== UserRole.ADMIN,
        then: Yup.number().required('Store is required for non-admin users'),
      }),
  });

  const formik = useFormik({
    initialValues: {
      email: user?.email || '',
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      role: user?.role || UserRole.SALES_REP,
      storeId: user?.storeId || null,
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        await onSubmit(values as User);
      } catch (error) {
        console.error('Error submitting form:', error);
      } finally {
        setLoading(false);
      }
    },
  });

  return (
    <form onSubmit={formik.handleSubmit} className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">
          {user ? 'Edit User' : 'Add New User'}
        </h3>
        <button
          type="button"
          onClick={onClose}
          className="text-gray-400 hover:text-gray-500"
        >
          <XMarkIcon className="h-5 w-5" aria-hidden="true" />
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
            First Name
          </label>
          <input
            type="text"
            id="firstName"
            name="firstName"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('firstName')}
          />
          {formik.touched.firstName && formik.errors.firstName && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.firstName}</p>
          )}
        </div>

        <div>
          <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
            Last Name
          </label>
          <input
            type="text"
            id="lastName"
            name="lastName"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('lastName')}
          />
          {formik.touched.lastName && formik.errors.lastName && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.lastName}</p>
          )}
        </div>

        <div className="col-span-2">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            Email
          </label>
          <input
            type="email"
            id="email"
            name="email"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('email')}
          />
          {formik.touched.email && formik.errors.email && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.email}</p>
          )}
        </div>

        <div>
          <label htmlFor="role" className="block text-sm font-medium text-gray-700">
            Role
          </label>
          <select
            id="role"
            name="role"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            {...formik.getFieldProps('role')}
          >
            <option value={UserRole.ADMIN}>Admin</option>
            <option value={UserRole.STORE_OWNER}>Store Owner</option>
            <option value={UserRole.SALES_REP}>Sales Rep</option>
          </select>
          {formik.touched.role && formik.errors.role && (
            <p className="mt-1 text-sm text-red-600">{formik.errors.role}</p>
          )}
        </div>

        {formik.values.role !== UserRole.ADMIN && (
          <div>
            <label htmlFor="storeId" className="block text-sm font-medium text-gray-700">
              Assigned Store
            </label>
            <select
              id="storeId"
              name="storeId"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              {...formik.getFieldProps('storeId')}
            >
              <option value="">Select a store</option>
              {stores.map((store) => (
                <option key={store.id} value={store.id}>
                  {store.name}
                </option>
              ))}
            </select>
            {formik.touched.storeId && formik.errors.storeId && (
              <p className="mt-1 text-sm text-red-600">{formik.errors.storeId}</p>
            )}
          </div>
        )}
      </div>

      <div className="flex justify-end">
        <button
          type="button"
          onClick={onClose}
          className="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Saving...' : user ? 'Update User' : 'Add User'}
        </button>
      </div>
    </form>
  );
};

export default UserForm;
