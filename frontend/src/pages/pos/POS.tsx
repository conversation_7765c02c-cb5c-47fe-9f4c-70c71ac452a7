import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';

interface CartItem {
  id: number;
  productId: number;
  name: string;
  sku: string;
  price: number;
  unitPrice: number;
  quantity: number;
  subtotal: number;
  currency: string;
}

const POS = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user || !user.storeId) {
      navigate('/unauthorized');
    }
  }, [user, navigate]);

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      const response = await axios.get(`/api/pos/search?query=${encodeURIComponent(query)}`);
      setSearchResults(response.data);
    } catch (err) {
      setError('Failed to search products');
      console.error(err);
    }
  };

  const addToCart = (product: any) => {
    const existingItem = cartItems.find(item => item.productId === product.id);
    if (existingItem) {
      setCartItems(cartItems.map(item =>
        item.productId === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCartItems([...cartItems, {
        productId: product.id,
        name: product.name,
        sku: product.sku,
        quantity: 1,
        unitPrice: product.retailPrice,
        subtotal: product.retailPrice
      }]);
    }
  };

  const removeFromCart = (productId: number) => {
    setCartItems(cartItems.filter(item => item.productId !== productId));
  };

  const updateQuantity = (productId: number, quantity: number) => {
    setCartItems(cartItems.map(item =>
      item.productId === productId
        ? { ...item, quantity, subtotal: item.unitPrice * quantity }
        : item
    ));
  };

  const calculateTotal = () => {
    return cartItems.reduce((total, item) => total + item.subtotal, 0);
  };

  const checkout = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post('/api/pos/sale', {
        items: cartItems.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice
        })),
        paymentMethod: 'cash', // TODO: Add payment method selection
        discountAmount: 0 // TODO: Add discount functionality
      });

      // Clear cart after successful checkout
      setCartItems([]);
      navigate(`/pos/sale/${response.data.id}`);
    } catch (err) {
      setError('Checkout failed');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold">Point of Sale</h1>
        <div className="flex items-center space-x-4">
          <input
            type="text"
            placeholder="Search products..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="border rounded px-4 py-2 w-64"
          />
          <button
            onClick={checkout}
            disabled={cartItems.length === 0 || loading}
            className="bg-primary-600 text-white px-6 py-2 rounded disabled:opacity-50"
          >
            Checkout
          </button>
        </div>
      </div>

      {/* Search Results */}
      {searchResults.length > 0 && (
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Search Results</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {searchResults.map((product: any) => (
              <div
                key={product.id}
                className="border rounded p-4 hover:shadow-md cursor-pointer"
                onClick={() => addToCart(product)}
              >
                <h3 className="font-semibold">{product.name}</h3>
                <p className="text-gray-600">SKU: {product.sku}</p>
                <p className="font-medium">{product.retailPrice} {product.currency}</p>
                <p className="text-sm text-gray-500">Stock: {product.inventory}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Cart */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Cart</h2>
        {cartItems.length === 0 ? (
          <p className="text-gray-500">Your cart is empty</p>
        ) : (
          <div>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="border-b">
                    <th className="py-2">Product</th>
                    <th className="py-2">Quantity</th>
                    <th className="py-2">Price</th>
                    <th className="py-2">Subtotal</th>
                    <th className="py-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {cartItems.map((item: any) => (
                    <tr key={item.productId} className="border-b">
                      <td className="py-2">{item.name}</td>
                      <td className="py-2">
                        <input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateQuantity(item.productId, parseInt(e.target.value))}
                          min="1"
                          className="w-16 border rounded px-2"
                        />
                      </td>
                      <td className="py-2">{item.unitPrice} {item.currency}</td>
                      <td className="py-2">{item.subtotal} {item.currency}</td>
                      <td className="py-2">
                        <button
                          onClick={() => removeFromCart(item.productId)}
                          className="text-red-500 hover:text-red-700"
                        >
                          Remove
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="mt-4 flex justify-between items-center">
              <div className="text-lg font-semibold">
                Total: {calculateTotal()} {cartItems[0]?.currency}
              </div>
              {error && <div className="text-red-500">{error}</div>}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default POS;
