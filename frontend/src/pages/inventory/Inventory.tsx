import React from 'react';

const Inventory: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Inventory Management</h1>
        <p className="text-gray-600">Manage your store inventory and stock levels</p>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Inventory Overview
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900">Total Items</h4>
              <p className="text-2xl font-bold text-blue-600">0</p>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-yellow-900">Low Stock</h4>
              <p className="text-2xl font-bold text-yellow-600">0</p>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-red-900">Out of Stock</h4>
              <p className="text-2xl font-bold text-red-600">0</p>
            </div>
          </div>
          
          <div className="mt-6">
            <p className="text-gray-500 text-center py-8">
              Inventory management functionality coming soon...
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Inventory;
