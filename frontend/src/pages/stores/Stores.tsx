import { useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { useState } from 'react';
import { PlusIcon } from '@heroicons/react/24/outline';
import StoreForm, { Store as FormStore } from '../../components/stores/StoreForm';
import StoreTable from '../../components/stores/StoreTable';

// API Store type (with required id)
interface ApiStore extends FormStore {
  id: number;
}

const Stores = () => {
  const [showForm, setShowForm] = useState(false);
  const queryClient = useQueryClient();

  const { data: stores, isLoading } = useQuery({
    queryKey: ['stores'],
    queryFn: () => axios.get('/api/stores').then(res => res.data as ApiStore[]),
  });

  const handleAddStore = async (storeData: FormStore) => {
    try {
      await axios.post('/api/stores', storeData);
      queryClient.invalidateQueries({ queryKey: ['stores'] });
      setShowForm(false);
    } catch (error) {
      console.error('Error adding store:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-gray-900">Stores</h2>
        <button
          onClick={() => setShowForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
          Add Store
        </button>
      </div>

      {showForm && (
        <div className="bg-white rounded-lg shadow p-6">
          <StoreForm onSubmit={handleAddStore} onClose={() => setShowForm(false)} />
        </div>
      )}

      {isLoading ? (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        </div>
      ) : (
        <StoreTable stores={stores || []} />
      )}
    </div>
  );
};

export default Stores;
