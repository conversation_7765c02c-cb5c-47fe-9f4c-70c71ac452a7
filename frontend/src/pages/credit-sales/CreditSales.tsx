import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';
import { UserRole } from '../../components/users/UserForm';

const CreditSales = () => {
  const { user } = useAuth();
  const [sales, setSales] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    fetchSales();
  }, [statusFilter, page, limit]);

  const fetchSales = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await axios.get('/api/credit-sales', {
        params: {
          status: statusFilter,
          page,
          limit
        }
      });
      setSales(response.data.sales);
      setTotal(response.data.total);
    } catch (err) {
      setError('Failed to fetch credit sales');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async (saleId: number) => {
    const amountStr = prompt('Enter payment amount:');
    if (!amountStr) return;
    const amount = parseFloat(amountStr);
    const paymentMethod = prompt('Enter payment method:') || 'cash';
    const paymentReference = prompt('Enter payment reference:') || '';
    const notes = prompt('Enter payment notes:') || '';

    try {
      await axios.post(`/api/credit-sales/${saleId}/payment`, {
        amount,
        paymentMethod,
        paymentReference,
        notes
      });
      fetchSales();
    } catch (err) {
      setError('Failed to record payment');
      console.error(err);
    }
  };

  const handleCustomerBalance = async (customerId: number) => {
    try {
      const response = await axios.get(`/api/credit-sales/balance/${customerId}`);
      alert(`Customer Balance:\n\nTotal Owed: $${response.data.totalOwed}\nTotal Paid: $${response.data.totalPaid}\nBalance: $${response.data.balance}\nStatus: ${response.data.status}`);
    } catch (err) {
      setError('Failed to get customer balance');
      console.error(err);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Credit Sales</h1>

      <div className="mb-8">
        <div className="flex gap-4 mb-4">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="border rounded px-4 py-2"
          >
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="paid">Paid</option>
          </select>
        </div>

        {loading && (
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
        )}

        {error && <div className="text-red-500 mb-4">{error}</div>}

        <div className="bg-white rounded-lg shadow p-6">
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b">
                  <th className="py-2">ID</th>
                  <th className="py-2">Customer</th>
                  <th className="py-2">Store</th>
                  <th className="py-2">Total Amount</th>
                  <th className="py-2">Due Date</th>
                  <th className="py-2">Status</th>
                  <th className="py-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {sales.map((sale: any) => (
                  <tr key={sale.id} className="border-b">
                    <td className="py-2">{sale.id}</td>
                    <td className="py-2">{sale.customer.name}</td>
                    <td className="py-2">{sale.store.name}</td>
                    <td className="py-2">${sale.totalAmount.toFixed(2)}</td>
                    <td className="py-2">{new Date(sale.dueDate).toLocaleDateString()}</td>
                    <td className="py-2">
                      <span className={`px-2 py-1 rounded ${
                        sale.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        sale.status === 'paid' ? 'bg-green-100 text-green-800' : ''
                      }`}>
                        {sale.status}
                      </span>
                    </td>
                    <td className="py-2">
                      <div className="flex gap-2">
                        {sale.status === 'pending' && (
                          <button
                            onClick={() => handlePayment(sale.id)}
                            className="bg-blue-600 text-white px-4 py-1 rounded"
                          >
                            Record Payment
                          </button>
                        )}
                        <button
                          onClick={() => handleCustomerBalance(sale.customerId)}
                          className="bg-gray-600 text-white px-4 py-1 rounded"
                        >
                          View Balance
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {total > limit && (
          <div className="flex justify-center mt-4">
            <button
              onClick={() => setPage(page - 1)}
              disabled={page <= 1}
              className="px-4 py-2 rounded"
            >
              Previous
            </button>
            <span className="mx-4">Page {page}</span>
            <button
              onClick={() => setPage(page + 1)}
              disabled={page * limit >= total}
              className="px-4 py-2 rounded"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreditSales;
