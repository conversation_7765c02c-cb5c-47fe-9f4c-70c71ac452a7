import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';
import { UserRole } from '../../components/users/UserForm';

interface Customer {
  id: number;
  [key: string]: any;
}

interface OverdueCustomer {
  customer: Customer;
  [key: string]: any;
}

interface PaymentHistoryItem {
  [key: string]: any;
}

const OverdueCustomers = () => {
  const { user } = useAuth();
  const [customers, setCustomers] = useState<OverdueCustomer[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<OverdueCustomer | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistoryItem[]>([]);
  const [message, setMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);

  useEffect(() => {
    fetchOverdueCustomers();
  }, []);

  const fetchOverdueCustomers = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await axios.get('/api/credit-sales/overdue');
      setCustomers(response.data);
    } catch (err) {
      setError('Failed to fetch overdue customers');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const fetchPaymentHistory = async (customerId: number) => {
    try {
      const response = await axios.get(`/api/credit-sales/${customerId}/history`);
      setPaymentHistory(response.data);
    } catch (err) {
      setError('Failed to fetch payment history');
      console.error(err);
    }
  };

  const handleSendSMS = async (customerId: number) => {
    if (!message.trim()) {
      setError('Please enter a message');
      return;
    }

    try {
      setSendingMessage(true);
      await axios.post(`/api/credit-sales/${customerId}/reminder`, {
        message
      });
      setMessage('');
      fetchOverdueCustomers();
    } catch (err) {
      setError('Failed to send SMS');
      console.error(err);
    } finally {
      setSendingMessage(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Overdue Credit Sales</h1>

      {loading && (
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
      )}

      {error && <div className="text-red-500 mb-4">{error}</div>}

      <div className="bg-white rounded-lg shadow p-6">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b">
                <th className="py-2">Customer</th>
                <th className="py-2">Phone</th>
                <th className="py-2">Total Owed</th>
                <th className="py-2">Due Date</th>
                <th className="py-2">Last Payment</th>
                <th className="py-2">Last Reminder</th>
                <th className="py-2">Actions</th>
              </tr>
            </thead>
            <tbody>
              {customers.map((customer: any) => (
                <tr key={customer.id} className="border-b hover:bg-gray-50">
                  <td className="py-2">{customer.customer.name}</td>
                  <td className="py-2">{customer.customer.phone}</td>
                  <td className="py-2">${(customer.creditSale.totalAmount - customer.totalPaid).toFixed(2)}</td>
                  <td className="py-2">{new Date(customer.creditSale.dueDate).toLocaleDateString()}</td>
                  <td className="py-2">{customer.lastPayment ? new Date(customer.lastPayment).toLocaleDateString() : 'N/A'}</td>
                  <td className="py-2">{customer.lastReminder ? new Date(customer.lastReminder).toLocaleDateString() : 'N/A'}</td>
                  <td className="py-2">
                    <div className="flex gap-2">
                      <button
                        onClick={() => {
                          setSelectedCustomer(customer);
                          fetchPaymentHistory(customer.customer.id);
                        }}
                        className="bg-blue-600 text-white px-4 py-1 rounded"
                      >
                        View History
                      </button>
                      <button
                        onClick={() => handleSendSMS(customer.customer.id)}
                        disabled={sendingMessage}
                        className="bg-green-600 text-white px-4 py-1 rounded"
                      >
                        Send Reminder
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Payment History Modal */}
      {selectedCustomer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 w-96">
            <h2 className="text-xl font-bold mb-4">Payment History</h2>
            <div className="overflow-y-auto max-h-[500px]">
              <table className="min-w-full">
                <thead>
                  <tr className="border-b">
                    <th className="py-2">Date</th>
                    <th className="py-2">Amount</th>
                    <th className="py-2">Type</th>
                    <th className="py-2">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {paymentHistory.map((payment: any) => (
                    <tr key={payment.id} className="border-b">
                      <td className="py-2">{new Date(payment.createdAt).toLocaleDateString()}</td>
                      <td className="py-2">${payment.amount}</td>
                      <td className="py-2">{payment.type}</td>
                      <td className="py-2">
                        <span className={`px-2 py-1 rounded ${
                          payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          payment.status === 'sent' ? 'bg-green-100 text-green-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {payment.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="mt-4 flex justify-end">
              <button
                onClick={() => setSelectedCustomer(null)}
                className="bg-gray-600 text-white px-4 py-2 rounded"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* SMS Message Input */}
      {selectedCustomer && (
        <div className="fixed bottom-4 right-4">
          <div className="bg-white rounded-lg p-4 shadow-lg">
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter reminder message..."
              className="w-full p-2 border rounded"
            />
            <button
              onClick={() => handleSendSMS(selectedCustomer.customer.id)}
              disabled={sendingMessage}
              className="mt-2 bg-blue-600 text-white px-4 py-2 rounded"
            >
              {sendingMessage ? 'Sending...' : 'Send Reminder'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default OverdueCustomers;
