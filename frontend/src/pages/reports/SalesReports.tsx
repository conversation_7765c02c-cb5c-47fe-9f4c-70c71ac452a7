import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';
import { UserRole } from '../../components/users/UserForm';

interface SalesSummary {
  totalRevenue: number;
  totalSales: number;
  topProducts: Array<{
    product: { name: string };
    total_sold: number;
    total_revenue: number;
  }>;
}

const SalesReports = () => {
  const { user } = useAuth();
  const [period, setPeriod] = useState('day');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [summary, setSummary] = useState<SalesSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchSummary();
  }, [period, startDate, endDate]);

  const fetchSummary = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await axios.get('/api/reports/summary', {
        params: {
          period,
          startDate,
          endDate
        }
      });
      setSummary(response.data);
    } catch (err) {
      setError('Failed to fetch sales summary');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      const response = await axios.get('/api/reports/export', {
        params: {
          startDate,
          endDate
        },
        responseType: 'blob'
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'sales_report.csv');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      setError('Failed to export sales data');
      console.error(err);
    }
  };

  const handlePrintReceipt = async (saleId: number) => {
    try {
      const response = await axios.get(`/api/reports/receipt/${saleId}`, {
        responseType: 'blob'
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `receipt_${saleId}.pdf`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      setError('Failed to print receipt');
      console.error(err);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Sales Reports</h1>

      <div className="mb-8">
        <div className="flex gap-4 mb-4">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="border rounded px-4 py-2"
          >
            <option value="day">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
          <input
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            className="border rounded px-4 py-2"
          />
          <input
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            className="border rounded px-4 py-2"
          />
          <button
            onClick={fetchSummary}
            className="bg-primary-600 text-white px-6 py-2 rounded"
          >
            Generate Report
          </button>
          <button
            onClick={handleExport}
            className="bg-green-600 text-white px-6 py-2 rounded"
          >
            Export to CSV
          </button>
        </div>

        {loading && (
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
        )}

        {error && <div className="text-red-500 mb-4">{error}</div>}

        {summary && (
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-white p-6 rounded shadow">
              <h3 className="text-lg font-semibold mb-2">Total Sales</h3>
              <p className="text-2xl">{summary.totalSales}</p>
            </div>
            <div className="bg-white p-6 rounded shadow">
              <h3 className="text-lg font-semibold mb-2">Total Revenue</h3>
              <p className="text-2xl">${summary.totalRevenue.toFixed(2)}</p>
            </div>
            <div className="bg-white p-6 rounded shadow">
              <h3 className="text-lg font-semibold mb-2">Average Sale</h3>
              <p className="text-2xl">${summary.averageSale.toFixed(2)}</p>
            </div>
          </div>
        )}

        {summary?.topProducts && (
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4">Top Selling Products</h2>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="py-2">Product</th>
                      <th className="py-2">Total Sold</th>
                      <th className="py-2">Revenue</th>
                    </tr>
                  </thead>
                  <tbody>
                    {summary.topProducts.map((product: any, index: number) => (
                      <tr key={index} className="border-b">
                        <td className="py-2">{product.product.name}</td>
                        <td className="py-2">{product.total_sold}</td>
                        <td className="py-2">${product.total_revenue.toFixed(2)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SalesReports;
