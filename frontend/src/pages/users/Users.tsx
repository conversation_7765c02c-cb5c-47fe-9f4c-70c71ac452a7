import { useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { useState } from 'react';
import { PlusIcon } from '@heroicons/react/24/outline';
import UserForm, { User as FormUser, UserRole } from '../../components/users/UserForm';
import UserTable from '../../components/users/UserTable';

// API User type (with required id)
interface ApiUser extends FormUser {
  id: number;
  store?: {
    name: string;
  };
}

const Users = () => {
  const [showForm, setShowForm] = useState(false);
  const queryClient = useQueryClient();

  const { data: users, isLoading } = useQuery({
    queryKey: ['users'],
    queryFn: () => axios.get('/api/users').then(res => res.data as ApiUser[]),
  });

  const handleAddUser = async (userData: FormUser) => {
    try {
      await axios.post('/api/users', userData);
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setShowForm(false);
    } catch (error) {
      console.error('Error adding user:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-gray-900">Users</h2>
        <button
          onClick={() => setShowForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
          Add User
        </button>
      </div>

      {showForm && (
        <div className="bg-white rounded-lg shadow p-6">
          <UserForm onSubmit={handleAddUser} onClose={() => setShowForm(false)} />
        </div>
      )}

      {isLoading ? (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        </div>
      ) : (
        <UserTable users={users || []} />
      )}
    </div>
  );
};

export default Users;
