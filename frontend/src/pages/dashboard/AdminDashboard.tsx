import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';
import { UserRole } from '../../components/users/UserForm';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

interface SalesDataItem {
  [key: string]: any;
}

interface StockItem {
  [key: string]: any;
}

interface ShopItem {
  [key: string]: any;
}

interface CreditSaleItem {
  [key: string]: any;
}

const AdminDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [salesData, setSalesData] = useState<SalesDataItem[]>([]);
  const [lowStock, setLowStock] = useState<StockItem[]>([]);
  const [topShops, setTopShops] = useState<ShopItem[]>([]);
  const [bnplBalance, setBnplBalance] = useState(0);
  const [creditSales, setCreditSales] = useState<CreditSaleItem[]>([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch sales data
      const salesResponse = await axios.get('/api/reports/sales');
      setSalesData(salesResponse.data);

      // Fetch low stock items
      const lowStockResponse = await axios.get('/api/inventory/low-stock');
      setLowStock(lowStockResponse.data);

      // Fetch top performing shops
      const shopsResponse = await axios.get('/api/reports/top-shops');
      setTopShops(shopsResponse.data);

      // Fetch BNPL balances
      const bnplResponse = await axios.get('/api/credit-sales/balance');
      setBnplBalance(bnplResponse.data.totalBalance);
      setCreditSales(bnplResponse.data.sales);
    } catch (err) {
      setError('Failed to fetch dashboard data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Admin Dashboard</h1>

      {loading && (
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
      )}

      {error && <div className="text-red-500 mb-4">{error}</div>}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {/* Total Sales Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Total Sales</h2>
          <LineChart width={400} height={200} data={salesData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Line
              type="monotone"
              dataKey="amount"
              stroke="#0088FE"
              strokeWidth={2}
            />
          </LineChart>
        </div>

        {/* Low Stock Alert Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Low Stock Alerts</h2>
          <div className="space-y-4">
            {lowStock.map((item: any, index: number) => (
              <div key={index} className="flex justify-between items-center p-3 rounded bg-yellow-50">
                <div>
                  <p className="font-semibold">{item.productName}</p>
                  <p className="text-sm text-gray-600">Current: {item.quantity}</p>
                </div>
                <div className="text-red-500">Low Stock</div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Shops Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Top Performing Shops</h2>
          <BarChart width={400} height={200} data={topShops}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="storeName" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="salesAmount" fill="#0088FE" />
          </BarChart>
        </div>
      </div>

      {/* BNPL Overview */}
      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h2 className="text-xl font-bold mb-4">BNPL Overview</h2>
        <div className="grid grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">Total BNPL Balance</h3>
            <p className="text-2xl font-bold">${bnplBalance.toFixed(2)}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-2">Active Credit Sales</h3>
            <PieChart width={200} height={200}>
              <Pie
                data={creditSales}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={80}
                fill="#8884d8"
                paddingAngle={5}
                dataKey="amount"
              >
                {creditSales.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-bold mb-4">Recent Activity</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b">
                <th className="py-2">Date</th>
                <th className="py-2">Type</th>
                <th className="py-2">Amount</th>
                <th className="py-2">Status</th>
              </tr>
            </thead>
            <tbody>
              {/* Add recent activity data */}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
