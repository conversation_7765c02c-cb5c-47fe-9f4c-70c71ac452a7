import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';
import { UserRole } from '../../components/users/UserForm';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

interface SaleItem {
  amount: number;
  [key: string]: any;
}

interface CustomerItem {
  [key: string]: any;
}

interface ProductItem {
  [key: string]: any;
}

const SalesRepDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [dailySales, setDailySales] = useState<SaleItem[]>([]);
  const [customers, setCustomers] = useState<CustomerItem[]>([]);
  const [topProducts, setTopProducts] = useState<ProductItem[]>([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError('');

      // Fetch daily sales
      const salesResponse = await axios.get(`/api/reports/daily-sales/${user.id}`);
      setDailySales(salesResponse.data);

      // Fetch customer list
      const customersResponse = await axios.get(`/api/customers/assigned/${user.id}`);
      setCustomers(customersResponse.data);

      // Fetch top products
      const productsResponse = await axios.get(`/api/reports/top-products/${user.storeId}`);
      setTopProducts(productsResponse.data);
    } catch (err) {
      setError('Failed to fetch dashboard data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Sales Rep Dashboard</h1>

      {loading && (
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
      )}

      {error && <div className="text-red-500 mb-4">{error}</div>}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* Daily Sales */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Daily Sales</h2>
          <LineChart width={400} height={200} data={dailySales}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Line
              type="monotone"
              dataKey="amount"
              stroke="#0088FE"
              strokeWidth={2}
            />
          </LineChart>
        </div>

        {/* Customer List */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">My Customers</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b">
                  <th className="py-2">Name</th>
                  <th className="py-2">Phone</th>
                  <th className="py-2">Last Purchase</th>
                </tr>
              </thead>
              <tbody>
                {customers.map((customer: any, index: number) => (
                  <tr key={index} className="border-b">
                    <td className="py-2">{customer.name}</td>
                    <td className="py-2">{customer.phone}</td>
                    <td className="py-2">{customer.lastPurchase}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Top Products */}
      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h2 className="text-xl font-bold mb-4">Top Products</h2>
        <BarChart width={600} height={300} data={topProducts}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="productName" />
          <YAxis />
          <Tooltip />
          <Bar dataKey="salesCount" fill="#0088FE" />
        </BarChart>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-bold mb-4">Performance Metrics</h2>
        <div className="grid grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">Total Sales</h3>
            <p className="text-2xl font-bold">${dailySales.reduce((sum, item) => sum + item.amount, 0).toFixed(2)}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-2">Active Customers</h3>
            <p className="text-2xl font-bold">{customers.filter(c => c.isActive).length}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-2">BNPL Sales</h3>
            <p className="text-2xl font-bold">{dailySales.filter(s => s.isBnpl).length}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-2">Average Sale</h3>
            <p className="text-2xl font-bold">${dailySales.reduce((sum, item) => sum + item.amount, 0) / dailySales.length || 0}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesRepDashboard;
