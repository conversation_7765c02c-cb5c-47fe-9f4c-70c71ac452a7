import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';
import { UserRole } from '../../components/users/UserForm';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

interface BnplItem {
  amount: number;
  [key: string]: any;
}

interface ShopPerformanceItem {
  [key: string]: any;
}

interface InventoryItem {
  [key: string]: any;
}

const StoreOwnerDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [shopPerformance, setShopPerformance] = useState<ShopPerformanceItem[]>([]);
  const [bnplData, setBnplData] = useState<BnplItem[]>([]);
  const [inventory, setInventory] = useState<InventoryItem[]>([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    if (!user?.storeId) return;

    try {
      setLoading(true);
      setError('');

      // Fetch shop performance
      const shopResponse = await axios.get(`/api/reports/shop-performance/${user.storeId}`);
      setShopPerformance(shopResponse.data);

      // Fetch BNPL data
      const bnplResponse = await axios.get(`/api/credit-sales/shop/${user.storeId}`);
      setBnplData(bnplResponse.data);

      // Fetch inventory
      const inventoryResponse = await axios.get(`/api/inventory/shop/${user.storeId}`);
      setInventory(inventoryResponse.data);
    } catch (err) {
      setError('Failed to fetch dashboard data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Store Owner Dashboard</h1>

      {loading && (
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
      )}

      {error && <div className="text-red-500 mb-4">{error}</div>}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* Shop Performance */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Shop Performance</h2>
          <LineChart width={400} height={200} data={shopPerformance}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Line
              type="monotone"
              dataKey="sales"
              stroke="#0088FE"
              strokeWidth={2}
            />
            <Line
              type="monotone"
              dataKey="profit"
              stroke="#00C49F"
              strokeWidth={2}
            />
          </LineChart>
        </div>

        {/* BNPL Overview */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">BNPL Overview</h2>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <PieChart width={200} height={200}>
                <Pie
                  data={bnplData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  fill="#8884d8"
                  paddingAngle={5}
                  dataKey="amount"
                >
                  {bnplData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Total BNPL Balance</h3>
              <p className="text-2xl font-bold">${bnplData.reduce((sum, item) => sum + item.amount, 0).toFixed(2)}</p>
              <h3 className="text-lg font-semibold mt-4 mb-2">Active Credit Sales</h3>
              <p className="text-xl font-bold">{bnplData.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Inventory Overview */}
      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h2 className="text-xl font-bold mb-4">Inventory Overview</h2>
        <BarChart width={600} height={300} data={inventory}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="productName" />
          <YAxis />
          <Tooltip />
          <Bar dataKey="quantity" fill="#0088FE" />
        </BarChart>
      </div>

      {/* Recent Transactions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-bold mb-4">Recent Transactions</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b">
                <th className="py-2">Date</th>
                <th className="py-2">Type</th>
                <th className="py-2">Amount</th>
                <th className="py-2">Status</th>
              </tr>
            </thead>
            <tbody>
              {/* Add recent transactions data */}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default StoreOwnerDashboard;
