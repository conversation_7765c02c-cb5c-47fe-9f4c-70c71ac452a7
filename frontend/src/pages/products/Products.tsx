import { useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { useState } from 'react';
import { PlusIcon } from '@heroicons/react/24/outline';
import ProductForm, { Product as FormProduct } from '../../components/products/ProductForm';
import ProductTable from '../../components/products/ProductTable';

// API Product type (with required id)
interface ApiProduct extends FormProduct {
  id: number;
}

const Products = () => {
  const [showForm, setShowForm] = useState(false);
  const queryClient = useQueryClient();

  const { data: products, isLoading } = useQuery({
    queryKey: ['products'],
    queryFn: () => axios.get('/api/products').then(res => res.data as ApiProduct[]),
  });

  const handleAddProduct = async (productData: FormProduct) => {
    try {
      await axios.post('/api/products', productData);
      queryClient.invalidateQueries({ queryKey: ['products'] });
      setShowForm(false);
    } catch (error) {
      console.error('Error adding product:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-gray-900">Products</h2>
        <button
          onClick={() => setShowForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
          Add Product
        </button>
      </div>

      {showForm && (
        <div className="bg-white rounded-lg shadow p-6">
          <ProductForm onSubmit={handleAddProduct} onClose={() => setShowForm(false)} />
        </div>
      )}

      {isLoading ? (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        </div>
      ) : (
        <ProductTable products={products || []} />
      )}
    </div>
  );
};

export default Products;
