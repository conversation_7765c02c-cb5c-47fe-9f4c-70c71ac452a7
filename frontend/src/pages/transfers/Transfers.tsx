import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';
import { UserRole } from '../../components/users/UserForm';

const Transfers = () => {
  const { user } = useAuth();
  const [transfers, setTransfers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    fetchTransfers();
  }, [statusFilter, page, limit]);

  const fetchTransfers = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await axios.get('/api/transfers', {
        params: {
          status: statusFilter,
          page,
          limit
        }
      });
      setTransfers(response.data.transfers);
      setTotal(response.data.total);
    } catch (err) {
      setError('Failed to fetch transfers');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (transferId: number) => {
    try {
      await axios.put(`/api/transfers/${transferId}/approve`);
      fetchTransfers();
    } catch (err) {
      setError('Failed to approve transfer');
      console.error(err);
    }
  };

  const handleCreateTransfer = async () => {
    if (!user) {
      alert('User not authenticated');
      return;
    }

    const fromStoreId = user.storeId;
    const toStoreIdInput = prompt('Enter destination store ID:'); // TODO: Replace with store selector
    const productIdInput = prompt('Enter product ID:'); // TODO: Replace with product selector
    const quantityInput = prompt('Enter quantity:');

    if (!toStoreIdInput || !productIdInput || !quantityInput) {
      alert('All fields are required');
      return;
    }

    const toStoreId = parseInt(toStoreIdInput);
    const productId = parseInt(productIdInput);
    const quantity = parseInt(quantityInput);

    try {
      await axios.post('/api/transfers', {
        fromStoreId,
        toStoreId,
        productId,
        quantity
      });
      fetchTransfers();
    } catch (err) {
      setError('Failed to create transfer');
      console.error(err);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Product Transfers</h1>

      <div className="mb-8">
        <div className="flex gap-4 mb-4">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="border rounded px-4 py-2"
          >
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
          </select>
          <button
            onClick={handleCreateTransfer}
            className="bg-primary-600 text-white px-6 py-2 rounded"
          >
            Create Transfer
          </button>
        </div>

        {loading && (
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
        )}

        {error && <div className="text-red-500 mb-4">{error}</div>}

        <div className="bg-white rounded-lg shadow p-6">
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b">
                  <th className="py-2">ID</th>
                  <th className="py-2">From Store</th>
                  <th className="py-2">To Store</th>
                  <th className="py-2">Product</th>
                  <th className="py-2">Quantity</th>
                  <th className="py-2">Status</th>
                  <th className="py-2">Requested By</th>
                  <th className="py-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {transfers.map((transfer: any) => (
                  <tr key={transfer.id} className="border-b">
                    <td className="py-2">{transfer.id}</td>
                    <td className="py-2">{transfer.fromStore.name}</td>
                    <td className="py-2">{transfer.toStore.name}</td>
                    <td className="py-2">{transfer.product.name}</td>
                    <td className="py-2">{transfer.quantity}</td>
                    <td className="py-2">
                      <span className={`px-2 py-1 rounded ${
                        transfer.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        transfer.status === 'approved' ? 'bg-green-100 text-green-800' : ''
                      }`}>
                        {transfer.status}
                      </span>
                    </td>
                    <td className="py-2">{transfer.requestedBy.name}</td>
                    <td className="py-2">
                      {transfer.status === 'pending' && user && (
                        user.role === UserRole.ADMIN || user.role === UserRole.STORE_OWNER ? (
                          <button
                            onClick={() => handleApprove(transfer.id)}
                            className="bg-green-600 text-white px-4 py-1 rounded"
                          >
                            Approve
                          </button>
                        ) : null
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {total > limit && (
          <div className="flex justify-center mt-4">
            <button
              onClick={() => setPage(page - 1)}
              disabled={page <= 1}
              className="px-4 py-2 rounded"
            >
              Previous
            </button>
            <span className="mx-4">Page {page}</span>
            <button
              onClick={() => setPage(page + 1)}
              disabled={page * limit >= total}
              className="px-4 py-2 rounded"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Transfers;
