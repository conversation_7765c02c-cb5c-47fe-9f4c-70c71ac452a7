import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';
import { UserRole } from '../../components/users/UserForm';

interface PurchaseOrderItem {
  productId: string;
  quantity: number;
  unitPrice: number;
  notes: string;
}

interface PurchaseOrder {
  id: number;
  supplierId: string;
  storeId: string;
  orderNumber: string;
  deliveryDate: string;
  items: PurchaseOrderItem[];
  notes: string;
}

const PurchaseOrders = () => {
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<PurchaseOrder | null>(null);
  const [suppliers, setSuppliers] = useState([]);
  const [products, setProducts] = useState([]);
  const [formData, setFormData] = useState<{
    supplierId: string;
    storeId: string;
    orderNumber: string;
    deliveryDate: string;
    items: PurchaseOrderItem[];
    notes: string;
  }>({
    supplierId: '',
    storeId: '',
    orderNumber: '',
    deliveryDate: '',
    items: [],
    notes: ''
  });

  useEffect(() => {
    fetchOrders();
    fetchSuppliers();
    fetchProducts();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await axios.get('/api/purchase-orders');
      setOrders(response.data);
    } catch (err) {
      setError('Failed to fetch purchase orders');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const fetchSuppliers = async () => {
    try {
      const response = await axios.get('/api/suppliers');
      setSuppliers(response.data);
    } catch (err) {
      console.error('Failed to fetch suppliers:', err);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await axios.get('/api/products');
      setProducts(response.data);
    } catch (err) {
      console.error('Failed to fetch products:', err);
    }
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      alert('User not authenticated');
      return;
    }

    try {
      if (selectedOrder) {
        await axios.put(`/api/purchase-orders/${selectedOrder.id}`, formData);
      } else {
        await axios.post('/api/purchase-orders', {
          ...formData,
          storeId: user.storeId
        });
      }
      setShowForm(false);
      setSelectedOrder(null);
      setFormData({
        supplierId: '',
        storeId: '',
        orderNumber: '',
        deliveryDate: '',
        items: [],
        notes: ''
      });
      fetchOrders();
    } catch (err) {
      setError('Failed to save purchase order');
      console.error(err);
    }
  };

  const handleAddItem = () => {
    setFormData({
      ...formData,
      items: [...formData.items, {
        productId: '',
        quantity: 1,
        unitPrice: 0,
        notes: ''
      }]
    });
  };

  const handleRemoveItem = (index: number) => {
    const newItems = [...formData.items];
    newItems.splice(index, 1);
    setFormData({ ...formData, items: newItems });
  };

  const handleEdit = (order: any) => {
    setSelectedOrder(order);
    setFormData({
      supplierId: order.supplierId,
      storeId: order.storeId,
      orderNumber: order.orderNumber,
      deliveryDate: order.deliveryDate,
      items: order.items || [],
      notes: order.notes
    });
    setShowForm(true);
  };

  const handleUpdateStatus = async (orderId: number, status: string) => {
    try {
      await axios.put(`/api/purchase-orders/${orderId}/status`, { status });
      fetchOrders();
    } catch (err) {
      setError('Failed to update order status');
      console.error(err);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Purchase Orders</h1>

      <div className="mb-6">
        <button
          onClick={() => {
            setShowForm(true);
            setSelectedOrder(null);
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded"
        >
          Create Purchase Order
        </button>
      </div>

      {loading && (
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
      )}

      {error && <div className="text-red-500 mb-4">{error}</div>}

      <div className="bg-white rounded-lg shadow p-6">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b">
                <th className="py-2">Order #</th>
                <th className="py-2">Supplier</th>
                <th className="py-2">Store</th>
                <th className="py-2">Total Amount</th>
                <th className="py-2">Status</th>
                <th className="py-2">Delivery Date</th>
                <th className="py-2">Actions</th>
              </tr>
            </thead>
            <tbody>
              {orders.map((order: any) => (
                <tr key={order.id} className="border-b hover:bg-gray-50">
                  <td className="py-2">{order.orderNumber}</td>
                  <td className="py-2">{order.supplier.name}</td>
                  <td className="py-2">{order.store.name}</td>
                  <td className="py-2">${order.totalAmount.toFixed(2)}</td>
                  <td className="py-2">
                    <span className={`px-2 py-1 rounded ${
                      order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      order.status === 'received' ? 'bg-green-100 text-green-800' :
                      order.status === 'paid' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {order.status}
                    </span>
                  </td>
                  <td className="py-2">{order.deliveryDate ? new Date(order.deliveryDate).toLocaleDateString() : '-'}</td>
                  <td className="py-2">
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEdit(order)}
                        className="bg-blue-600 text-white px-4 py-1 rounded mr-2"
                      >
                        View/Edit
                      </button>
                      {order.status === 'pending' && (
                        <button
                          onClick={() => handleUpdateStatus(order.id, 'received')}
                          className="bg-green-600 text-white px-4 py-1 rounded"
                        >
                          Mark as Received
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Purchase Order Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 w-[800px]">
            <h2 className="text-xl font-bold mb-4">{selectedOrder ? 'Edit Purchase Order' : 'Create Purchase Order'}</h2>
            <form onSubmit={handleFormSubmit}>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-gray-700 mb-2">Supplier</label>
                  <select
                    value={formData.supplierId}
                    onChange={(e) => setFormData({ ...formData, supplierId: e.target.value })}
                    className="w-full p-2 border rounded"
                    required
                  >
                    <option value="">Select supplier</option>
                    {suppliers.map((supplier: any) => (
                      <option key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-gray-700 mb-2">Order Number</label>
                  <input
                    type="text"
                    value={formData.orderNumber}
                    onChange={(e) => setFormData({ ...formData, orderNumber: e.target.value })}
                    className="w-full p-2 border rounded"
                    required
                  />
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-gray-700 mb-2">Delivery Date</label>
                <input
                  type="date"
                  value={formData.deliveryDate}
                  onChange={(e) => setFormData({ ...formData, deliveryDate: e.target.value })}
                  className="w-full p-2 border rounded"
                />
              </div>

              <div className="mb-4">
                <label className="block text-gray-700 mb-2">Items</label>
                <div className="space-y-4">
                  {formData.items.map((item: any, index: number) => (
                    <div key={index} className="flex gap-4">
                      <select
                        value={item.productId}
                        onChange={(e) => {
                          const newItems = [...formData.items];
                          newItems[index].productId = e.target.value;
                          setFormData({ ...formData, items: newItems });
                        }}
                        className="w-full p-2 border rounded"
                      >
                        <option value="">Select product</option>
                        {products.map((product: any) => (
                          <option key={product.id} value={product.id}>
                            {product.name}
                          </option>
                        ))}
                      </select>
                      <input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => {
                          const newItems = [...formData.items];
                          newItems[index].quantity = parseInt(e.target.value) || 0;
                          setFormData({ ...formData, items: newItems });
                        }}
                        className="w-24 p-2 border rounded"
                        min="1"
                      />
                      <input
                        type="number"
                        value={item.unitPrice}
                        onChange={(e) => {
                          const newItems = [...formData.items];
                          newItems[index].unitPrice = parseFloat(e.target.value) || 0;
                          setFormData({ ...formData, items: newItems });
                        }}
                        className="w-32 p-2 border rounded"
                        step="0.01"
                      />
                      <input
                        type="text"
                        value={item.notes}
                        onChange={(e) => {
                          const newItems = [...formData.items];
                          newItems[index].notes = e.target.value;
                          setFormData({ ...formData, items: newItems });
                        }}
                        className="flex-grow p-2 border rounded"
                        placeholder="Notes"
                      />
                      <button
                        type="button"
                        onClick={() => handleRemoveItem(index)}
                        className="bg-red-600 text-white px-4 py-2 rounded"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={handleAddItem}
                    className="mt-4 bg-green-600 text-white px-4 py-2 rounded"
                  >
                    Add Item
                  </button>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-gray-700 mb-2">Notes</label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  className="w-full p-2 border rounded"
                />
              </div>

              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => {
                    setShowForm(false);
                    setSelectedOrder(null);
                  }}
                  className="bg-gray-600 text-white px-4 py-2 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded"
                >
                  Save
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default PurchaseOrders;
