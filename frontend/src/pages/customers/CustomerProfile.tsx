import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';
import { UserRole } from '../../components/users/UserForm';
import { format } from 'date-fns';

interface CustomerProfile {
  id: number;
  name: string;
  phone: string;
  email: string;
  address: string;
  customerType: string;
  loyaltyTier: string;
  preferredStore: string;
  totalSpent: number;
  totalPurchases: number;
  lastPurchaseDate: string;
  points: number;
}

interface PurchaseHistory {
  id: number;
  date: string;
  amount: number;
  store: string;
  isBnpl: boolean;
}

const CustomerProfile: React.FC = () => {
  const { user } = useAuth();
  const [customer, setCustomer] = useState<CustomerProfile | null>(null);
  const [history, setHistory] = useState<PurchaseHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showEdit, setShowEdit] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    customerType: '',
    preferredStore: ''
  });

  useEffect(() => {
    const customerId = window.location.pathname.split('/').pop();
    if (customerId) {
      fetchCustomerProfile(customerId);
      fetchPurchaseHistory(customerId);
    }
  }, []);

  const fetchCustomerProfile = async (id: string) => {
    try {
      setLoading(true);
      setError('');
      const response = await axios.get(`/api/customers/profile/${id}`);
      setCustomer(response.data);
      setFormData({
        name: response.data.name,
        phone: response.data.phone,
        email: response.data.email,
        address: response.data.address,
        customerType: response.data.customerType,
        preferredStore: response.data.preferredStore
      });
    } catch (error) {
      setError('Failed to fetch customer profile');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPurchaseHistory = async (id: string) => {
    try {
      const response = await axios.get(`/api/customers/history/${id}`);
      setHistory(response.data);
    } catch (error) {
      console.error('Failed to fetch purchase history:', error);
    }
  };

  const handleEdit = () => {
    setShowEdit(true);
  };

  const handleCancel = () => {
    setShowEdit(false);
    setFormData({
      name: customer?.name || '',
      phone: customer?.phone || '',
      email: customer?.email || '',
      address: customer?.address || '',
      customerType: customer?.customerType || '',
      preferredStore: customer?.preferredStore || ''
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await axios.put(`/api/customers/${customer?.id}`, formData);
      setShowEdit(false);
      fetchCustomerProfile(customer?.id?.toString() || '');
    } catch (error) {
      console.error('Failed to update customer:', error);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>{error}</div>;
  if (!customer) return <div>Customer not found</div>;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Customer Profile</h1>

      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 className="text-xl font-bold mb-4">Customer Information</h2>
            {showEdit ? (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Name</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border rounded"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Phone</label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    className="w-full px-3 py-2 border rounded"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Email</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full px-3 py-2 border rounded"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Address</label>
                  <textarea
                    value={formData.address}
                    onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                    className="w-full px-3 py-2 border rounded"
                  />
                </div>
                <div className="flex justify-end space-x-4">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-4 py-2 text-gray-700 border rounded hover:bg-gray-100"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    Save
                  </button>
                </div>
              </form>
            ) : (
              <div className="space-y-4">
                <p><strong>Name:</strong> {customer.name}</p>
                <p><strong>Phone:</strong> {customer.phone}</p>
                <p><strong>Email:</strong> {customer.email}</p>
                <p><strong>Address:</strong> {customer.address}</p>
                <button
                  onClick={handleEdit}
                  className="px-4 py-2 text-blue-500 border border-blue-500 rounded hover:bg-blue-50"
                >
                  Edit Profile
                </button>
              </div>
            )}
          </div>

          <div>
            <h2 className="text-xl font-bold mb-4">Customer Insights</h2>
            <div className="space-y-4">
              <div className="border rounded p-4">
                <h3 className="font-semibold mb-2">Loyalty Status</h3>
                <div className="space-y-2">
                  <p><strong>Tier:</strong> {customer.loyaltyTier}</p>
                  <p><strong>Points:</strong> {customer.points}</p>
                </div>
              </div>
              <div className="border rounded p-4">
                <h3 className="font-semibold mb-2">Purchase History</h3>
                <div className="space-y-2">
                  <p><strong>Total Spent:</strong> ${customer.totalSpent.toFixed(2)}</p>
                  <p><strong>Total Purchases:</strong> {customer.totalPurchases}</p>
                  <p><strong>Last Purchase:</strong> {format(new Date(customer.lastPurchaseDate), 'MMM d, yyyy')}</p>
                </div>
              </div>
              <div className="border rounded p-4">
                <h3 className="font-semibold mb-2">Preferences</h3>
                <div className="space-y-2">
                  <p><strong>Customer Type:</strong> {customer.customerType}</p>
                  <p><strong>Preferred Store:</strong> {customer.preferredStore}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-bold mb-4">Purchase History</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b">
                <th className="py-2">Date</th>
                <th className="py-2">Store</th>
                <th className="py-2">Amount</th>
                <th className="py-2">Type</th>
              </tr>
            </thead>
            <tbody>
              {history.map((purchase) => (
                <tr key={purchase.id} className="border-b">
                  <td className="py-2">{format(new Date(purchase.date), 'MMM d, yyyy')}</td>
                  <td className="py-2">{purchase.store}</td>
                  <td className="py-2">${purchase.amount.toFixed(2)}</td>
                  <td className="py-2">
                    {purchase.isBnpl ? (
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        BNPL
                      </span>
                    ) : (
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                        Cash
                      </span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default CustomerProfile;
