import React from 'react';

const Customers: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Customer Management</h1>
        <p className="text-gray-600">Manage your customer database and relationships</p>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Customer Overview
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-indigo-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-indigo-900">Total Customers</h4>
              <p className="text-2xl font-bold text-indigo-600">0</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-green-900">Active Customers</h4>
              <p className="text-2xl font-bold text-green-600">0</p>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-yellow-900">New This Month</h4>
              <p className="text-2xl font-bold text-yellow-600">0</p>
            </div>
          </div>
          
          <div className="mt-6">
            <p className="text-gray-500 text-center py-8">
              Customer management functionality coming soon...
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Customers;
