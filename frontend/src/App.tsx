import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { UserRole } from './components/users/UserForm';
import ProtectedRoute from './components/auth/ProtectedRoute';

// Layouts
import DashboardLayout from './layouts/DashboardLayout';
import AuthLayout from './layouts/AuthLayout';

// Pages
import Dashboard from './pages/Dashboard';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import Unauthorized from './pages/auth/Unauthorized';
import Stores from './pages/stores/Stores';
import Products from './pages/products/Products';
import Inventory from './pages/inventory/Inventory';
import Sales from './pages/sales/Sales';
import Customers from './pages/customers/Customers';
import Suppliers from './pages/suppliers/Suppliers';
import SalesReports from './pages/reports/SalesReports';
import POS from './pages/pos/POS';
import Transfers from './pages/transfers/Transfers';
import CreditSales from './pages/credit-sales/CreditSales';
import OverdueCustomers from './pages/credit-sales/OverdueCustomers';
import Users from './pages/users/Users';
import PurchaseOrders from './pages/purchase-orders/PurchaseOrders';
import AdminDashboard from './pages/dashboard/AdminDashboard';
import StoreOwnerDashboard from './pages/dashboard/StoreOwnerDashboard';
import SalesRepDashboard from './pages/dashboard/SalesRepDashboard';
import CustomerProfile from './pages/customers/CustomerProfile';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <Routes>
          {/* Auth Routes */}
          <Route element={<AuthLayout />}>
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
          </Route>
          <Route path="/unauthorized" element={<Unauthorized />} />

          {/* Protected Routes */}
          <Route element={<DashboardLayout />}>
            {/* Admin Routes */}
            <Route
              path="/stores"
              element={
                <ProtectedRoute requiredRole={UserRole.ADMIN}>
                  <Stores />
                </ProtectedRoute>
              }
            />
            <Route
              path="/users"
              element={
                <ProtectedRoute requiredRole={UserRole.ADMIN}>
                  <Users />
                </ProtectedRoute>
              }
            />
            <Route
              path="/reports/sales"
              element={
                <ProtectedRoute requiredRole={UserRole.ADMIN}>
                  <SalesReports />
                </ProtectedRoute>
              }
            />

            {/* Store Owner Routes */}
            <Route
              path="/products"
              element={
                <ProtectedRoute requiredRole={UserRole.STORE_OWNER}>
                  <Products />
                </ProtectedRoute>
              }
            />
            <Route
              path="/inventory"
              element={
                <ProtectedRoute requiredRole={UserRole.STORE_OWNER}>
                  <Inventory />
                </ProtectedRoute>
              }
            />
            <Route
              path="/sales"
              element={
                <ProtectedRoute requiredRole={UserRole.STORE_OWNER}>
                  <Sales />
                </ProtectedRoute>
              }
            />
            <Route
              path="/customers"
              element={
                <ProtectedRoute requiredRole={UserRole.STORE_OWNER}>
                  <Customers />
                </ProtectedRoute>
              }
            />
            <Route
              path="/suppliers"
              element={
                <ProtectedRoute requiredRole={UserRole.STORE_OWNER}>
                  <Suppliers />
                </ProtectedRoute>
              }
            />
            <Route
              path="/purchase-orders"
              element={
                <ProtectedRoute requiredRole={UserRole.STORE_OWNER}>
                  <PurchaseOrders />
                </ProtectedRoute>
              }
            />
            <Route
              path="/transfers"
              element={
                <ProtectedRoute requiredRole={UserRole.STORE_OWNER}>
                  <Transfers />
                </ProtectedRoute>
              }
            />
            <Route
              path="/credit-sales"
              element={
                <ProtectedRoute requiredRole={UserRole.STORE_OWNER}>
                  <CreditSales />
                </ProtectedRoute>
              }
            />
            <Route
              path="/credit-sales/overdue"
              element={
                <ProtectedRoute requiredRole={UserRole.STORE_OWNER}>
                  <OverdueCustomers />
                </ProtectedRoute>
              }
            />
            <Route
              path="/pos"
              element={
                <ProtectedRoute requiredRole={UserRole.SALES_REP}>
                  <POS />
                </ProtectedRoute>
              }
            />
            <Route
              path="/customers/:id"
              element={
                <ProtectedRoute requiredRole={UserRole.SALES_REP}>
                  <CustomerProfile />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/admin"
              element={
                <ProtectedRoute requiredRole={UserRole.ADMIN}>
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/store-owner"
              element={
                <ProtectedRoute requiredRole={UserRole.STORE_OWNER}>
                  <StoreOwnerDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/sales-rep"
              element={
                <ProtectedRoute requiredRole={UserRole.SALES_REP}>
                  <SalesRepDashboard />
                </ProtectedRoute>
              }
            />

            {/* Default Dashboard Route */}
            <Route path="/" element={<Dashboard />} />
          </Route>
        </Routes>
      </Router>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

export default App;
