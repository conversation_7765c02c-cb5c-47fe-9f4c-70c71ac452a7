import { useState } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { Link } from 'react-router-dom';
import NotificationBell from '../components/notifications/NotificationBell';
import { BellIcon, UserCircleIcon } from '@heroicons/react/24/outline';

interface MenuItem {
  path: string;
  label: string;
  icon?: React.ReactNode;
}

interface DashboardLayoutProps {
  children?: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const location = useLocation();

  const menuItems: MenuItem[] = [
    { path: '/', label: 'Dashboard' },
    { path: '/stores', label: 'Stores' },
    { path: '/products', label: 'Products' },
    { path: '/inventory', label: 'Inventory' },
    { path: '/sales', label: 'Sales' },
    { path: '/customers', label: 'Customers' },
    { path: '/suppliers', label: 'Suppliers' },
    { path: '/purchases', label: 'Purchases' },
    { path: '/reports', label: 'Reports' },
  ];

  const isActive = (path: string): boolean => {
    return location.pathname === path;
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <aside
        className={`fixed top-0 left-0 h-screen w-64 bg-white shadow-lg transition-width duration-300 ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="p-4">
          <div className="flex items-center mb-8">
            <h1 className="text-2xl font-bold text-primary-500">Best Gadget</h1>
            <button
              onClick={toggleSidebar}
              className="ml-auto p-2 rounded hover:bg-gray-100"
              aria-label="Toggle sidebar"
            >
              {isSidebarOpen ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>
          <nav>
            <ul className="space-y-1">
              {menuItems.map((item) => (
                <li key={item.path} className="mb-2">
                  <Link
                    to={item.path}
                    className={`flex items-center px-4 py-2 rounded-md transition-colors duration-200 ${
                      isActive(item.path)
                        ? 'bg-primary-50 text-primary-600'
                        : 'text-gray-700 hover:bg-primary-50'
                    }`}
                  >
                    {item.icon && (
                      <span className="mr-3 h-5 w-5 flex-shrink-0">{item.icon}</span>
                    )}
                    <span>{item.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </aside>

      {/* Main Content */}
      <div className={`ml-64 flex-1 transition-all duration-300 ${!isSidebarOpen && 'ml-0'}`}>
        <header className="bg-white shadow-md">
          <div className="flex items-center justify-between p-4">
            <button
              onClick={toggleSidebar}
              className="p-2 rounded hover:bg-gray-100 lg:hidden"
              aria-label="Toggle sidebar"
            >
              {isSidebarOpen ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
            <div className="flex items-center space-x-4">
              <NotificationBell />
              <div className="relative">
                <button className="p-2 rounded hover:bg-gray-100 flex items-center">
                  <UserCircleIcon className="h-5 w-5" aria-hidden="true" />
                  <span className="ml-2 text-sm">Admin</span>
                  <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                  <span className="sr-only">Profile</span>
                </button>
              </div>
            </div>
          </div>
        </header>

        <main className="p-6">
          <Outlet />
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
