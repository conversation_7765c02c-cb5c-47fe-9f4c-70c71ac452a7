version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080" # Backend listens on 8080
    depends_on:
      - db
    env_file:
      - .env
    volumes:
      - ./backend:/usr/src/app # Mount backend code for development
      - /usr/src/app/node_modules # Avoid overwriting node_modules in container
    environment:
      - NODE_ENV=development
      - DATABASE_URL=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}
      - JWT_SECRET=${JWT_SECRET}
      - PORT=8080

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000" # React dev server typically runs on 3000
    depends_on:
      - backend
    volumes:
      - ./frontend:/usr/src/app # Mount frontend code for development
      - /usr/src/app/node_modules # Avoid overwriting node_modules in container
    environment:
      - REACT_APP_API_URL=http://localhost:8080/api # URL for backend API
      - NODE_ENV=development

  db:
    image: postgres:13-alpine
    ports:
      - "5432:5432" # Expose PostgreSQL port
    volumes:
      - postgres_data:/var/lib/postgresql/data # Persist database data
    env_file:
      - .env
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}

volumes:
  postgres_data:
